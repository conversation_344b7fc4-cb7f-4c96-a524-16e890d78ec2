{"version": 3, "file": "sessionManager.js", "sourceRoot": "", "sources": ["../../src/services/sessionManager.ts"], "names": [], "mappings": ";;;AAAA,+BAAoC;AACpC,yCAA+B;AAC/B,mEAAgE;AAChE,uDAAoD;AACpD,4CAAyC;AAGzC,MAAM,WAAW,GAAG;IAClB,IAAI,EAAE;QACJ,cAAc,EAAE,EAAE;QAClB,aAAa,EAAE,EAAE;QACjB,uBAAuB,EAAE,GAAG;QAC5B,YAAY,EAAE,CAAC,YAAY,EAAE,WAAW,EAAE,cAAc,EAAE,WAAW,CAAC;KACvE;IACD,OAAO,EAAE;QACP,cAAc,EAAE,GAAG;QACnB,aAAa,EAAE,EAAE;QACjB,uBAAuB,EAAE,IAAI;QAC7B,YAAY,EAAE,CAAC,GAAG,CAAC;KACpB;IACD,UAAU,EAAE;QACV,cAAc,EAAE,IAAI;QACpB,aAAa,EAAE,GAAG;QAClB,uBAAuB,EAAE,KAAK;QAC9B,YAAY,EAAE,CAAC,GAAG,CAAC;KACpB;CACF,CAAC;AAcF,MAAa,cAAc;IAKzB,MAAM,CAAC,KAAK,CAAC,kBAAkB,CAC7B,MAAc,EACd,gBAA0C,EAC1C,QAAgB;QAEhB,IAAI,CAAC;YACH,MAAM,UAAU,GAAG,WAAW,CAAC,gBAAgB,CAAC,CAAC;YAGjD,MAAM,WAAW,GAAG,UAAU,CAAC,YAAY,CAAC,QAAQ,CAAC,GAAG,CAAC;gBACtC,UAAU,CAAC,YAAY,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;YAE9D,IAAI,CAAC,WAAW,EAAE,CAAC;gBACjB,OAAO;oBACL,UAAU,EAAE,KAAK;oBACjB,MAAM,EAAE,kBAAkB;oBAC1B,OAAO,EAAE,QAAQ,QAAQ,yBAAyB,gBAAgB,OAAO;oBACzE,eAAe,EAAE,IAAI;iBACtB,CAAC;YACJ,CAAC;YAGD,MAAM,KAAK,GAAG,IAAI,IAAI,EAAE,CAAC;YACzB,KAAK,CAAC,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC;YAE3B,MAAM,UAAU,GAAG,MAAM,yBAAW,CAAC,OAAO,CAAC;gBAC3C,KAAK,EAAE;oBACL,OAAO,EAAE,MAAM;oBACf,UAAU,EAAE;wBACV,CAAC,cAAE,CAAC,GAAG,CAAC,EAAE,KAAK;qBAChB;iBACF;aACF,CAAC,CAAC;YAEH,MAAM,eAAe,GAAG,UAAU,CAAC,MAAM,CAAC;YAC1C,MAAM,wBAAwB,GAAG,UAAU,CAAC,MAAM,CAChD,CAAC,GAAG,EAAE,MAAM,EAAE,EAAE,CAAC,GAAG,GAAG,MAAM,CAAC,uBAAuB,EAAE,CAAC,CACzD,CAAC;YAGF,IAAI,eAAe,IAAI,UAAU,CAAC,cAAc,EAAE,CAAC;gBACjD,OAAO;oBACL,UAAU,EAAE,KAAK;oBACjB,MAAM,EAAE,kBAAkB;oBAC1B,OAAO,EAAE,uBAAuB,UAAU,CAAC,cAAc,UAAU;oBACnE,eAAe,EAAE,gBAAgB,KAAK,MAAM;iBAC7C,CAAC;YACJ,CAAC;YAED,IAAI,wBAAwB,IAAI,UAAU,CAAC,uBAAuB,EAAE,CAAC;gBACnE,OAAO;oBACL,UAAU,EAAE,KAAK;oBACjB,MAAM,EAAE,wBAAwB;oBAChC,OAAO,EAAE,qCAAqC;oBAC9C,eAAe,EAAE,gBAAgB,KAAK,MAAM;iBAC7C,CAAC;YACJ,CAAC;YAGD,MAAM,YAAY,GAAG,IAAA,SAAM,GAAE,CAAC;YAC9B,MAAM,SAAS,GAAG,IAAI,IAAI,EAAE,CAAC;YAC7B,SAAS,CAAC,QAAQ,CAAC,SAAS,CAAC,QAAQ,EAAE,GAAG,CAAC,CAAC,CAAC;YAE7C,MAAM,qCAAiB,CAAC,MAAM,CAAC;gBAC7B,OAAO,EAAE,MAAM;gBACf,aAAa,EAAE,YAAY;gBAC3B,UAAU,EAAE,SAAS;aACtB,CAAC,CAAC;YAEH,OAAO;gBACL,UAAU,EAAE,IAAI;gBAChB,YAAY;gBACZ,cAAc,EAAE;oBACd,KAAK,EAAE,UAAU,CAAC,cAAc,GAAG,eAAe;oBAClD,qBAAqB,EAAE,UAAU,CAAC,uBAAuB,GAAG,wBAAwB;iBACrF;aACF,CAAC;QAEJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,8BAA8B,EAAE,EAAE,MAAM,EAAE,KAAK,EAAE,CAAC,CAAC;YAChE,OAAO;gBACL,UAAU,EAAE,KAAK;gBACjB,MAAM,EAAE,cAAc;gBACtB,OAAO,EAAE,oCAAoC;aAC9C,CAAC;QACJ,CAAC;IACH,CAAC;IAKD,MAAM,CAAC,KAAK,CAAC,kBAAkB,CAC7B,YAAoB,EACpB,MAAc;QAEd,IAAI,CAAC;YACH,MAAM,OAAO,GAAG,MAAM,qCAAiB,CAAC,OAAO,CAAC;gBAC9C,KAAK,EAAE;oBACL,aAAa,EAAE,YAAY;oBAC3B,OAAO,EAAE,MAAM;iBAChB;aACF,CAAC,CAAC;YAEH,IAAI,CAAC,OAAO,EAAE,CAAC;gBACb,OAAO;oBACL,UAAU,EAAE,KAAK;oBACjB,MAAM,EAAE,mBAAmB;oBAC3B,OAAO,EAAE,8BAA8B;iBACxC,CAAC;YACJ,CAAC;YAED,IAAI,CAAC,OAAO,CAAC,QAAQ,EAAE,EAAE,CAAC;gBACxB,OAAO;oBACL,UAAU,EAAE,KAAK;oBACjB,MAAM,EAAE,iBAAiB;oBACzB,OAAO,EAAE,gCAAgC;iBAC1C,CAAC;YACJ,CAAC;YAED,OAAO;gBACL,UAAU,EAAE,IAAI;gBAChB,YAAY;aACb,CAAC;QAEJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,qCAAqC,EAAE,EAAE,YAAY,EAAE,MAAM,EAAE,KAAK,EAAE,CAAC,CAAC;YACrF,OAAO;gBACL,UAAU,EAAE,KAAK;gBACjB,MAAM,EAAE,cAAc;gBACtB,OAAO,EAAE,0BAA0B;aACpC,CAAC;QACJ,CAAC;IACH,CAAC;IAKD,MAAM,CAAC,KAAK,CAAC,gBAAgB,CAC3B,YAAoB,EACpB,MAAc,EACd,QAAgB,EAChB,UAAkB,EAClB,qBAA6B,EAC7B,cAAsB,EACtB,eAAuB,EACvB,OAAgB,EAChB,YAAqB;QAErB,IAAI,CAAC;YACH,MAAM,OAAO,GAAG,MAAM,qCAAiB,CAAC,OAAO,CAAC;gBAC9C,KAAK,EAAE;oBACL,aAAa,EAAE,YAAY;oBAC3B,OAAO,EAAE,MAAM;iBAChB;aACF,CAAC,CAAC;YAEH,IAAI,OAAO,IAAI,OAAO,CAAC,QAAQ,EAAE,EAAE,CAAC;gBAElC,OAAO,CAAC,cAAc,EAAE,CAAC;gBACzB,MAAM,OAAO,CAAC,IAAI,EAAE,CAAC;gBAGrB,MAAM,yBAAW,CAAC,MAAM,CAAC;oBACvB,OAAO,EAAE,MAAM;oBACf,SAAS,EAAE,QAAQ;oBACnB,YAAY,EAAE,UAAU;oBACxB,uBAAuB,EAAE,qBAAqB;oBAC9C,gBAAgB,EAAE,cAAc;oBAChC,iBAAiB,EAAE,eAAe;oBAClC,OAAO;oBACP,aAAa,EAAE,YAAY;iBAC5B,CAAC,CAAC;gBAEH,eAAM,CAAC,IAAI,CAAC,gCAAgC,EAAE;oBAC5C,YAAY;oBACZ,MAAM;oBACN,QAAQ;oBACR,OAAO;iBACR,CAAC,CAAC;YACL,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,uCAAuC,EAAE;gBACpD,YAAY;gBACZ,MAAM;gBACN,KAAK;aACN,CAAC,CAAC;QACL,CAAC;IACH,CAAC;IAKD,MAAM,CAAC,KAAK,CAAC,eAAe,CAAC,YAAoB,EAAE,MAAc;QAC/D,IAAI,CAAC;YACH,MAAM,OAAO,GAAG,MAAM,qCAAiB,CAAC,OAAO,CAAC;gBAC9C,KAAK,EAAE;oBACL,aAAa,EAAE,YAAY;oBAC3B,OAAO,EAAE,MAAM;iBAChB;aACF,CAAC,CAAC;YAEH,IAAI,OAAO,EAAE,CAAC;gBACZ,OAAO,CAAC,aAAa,EAAE,CAAC;gBACxB,MAAM,OAAO,CAAC,IAAI,EAAE,CAAC;gBAErB,eAAM,CAAC,IAAI,CAAC,mBAAmB,EAAE,EAAE,YAAY,EAAE,MAAM,EAAE,CAAC,CAAC;YAC7D,CAAC;QACH,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,0BAA0B,EAAE,EAAE,YAAY,EAAE,MAAM,EAAE,KAAK,EAAE,CAAC,CAAC;QAC5E,CAAC;IACH,CAAC;IAKD,MAAM,CAAC,KAAK,CAAC,WAAW,CAAC,YAAoB,EAAE,MAAc;QAC3D,IAAI,CAAC;YACH,MAAM,OAAO,GAAG,MAAM,qCAAiB,CAAC,OAAO,CAAC;gBAC9C,KAAK,EAAE;oBACL,aAAa,EAAE,YAAY;oBAC3B,OAAO,EAAE,MAAM;iBAChB;aACF,CAAC,CAAC;YAEH,OAAO,OAAO,CAAC,CAAC,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC,CAAC,KAAK,CAAC;QAChD,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,oCAAoC,EAAE,EAAE,YAAY,EAAE,MAAM,EAAE,KAAK,EAAE,CAAC,CAAC;YACpF,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC;IAKD,MAAM,CAAC,KAAK,CAAC,sBAAsB;QACjC,IAAI,CAAC;YACH,MAAM,GAAG,GAAG,IAAI,IAAI,EAAE,CAAC;YAEvB,MAAM,qCAAiB,CAAC,MAAM,CAC5B,EAAE,MAAM,EAAE,SAAS,EAAE,EACrB;gBACE,KAAK,EAAE;oBACL,UAAU,EAAE,EAAE,CAAC,cAAE,CAAC,EAAE,CAAC,EAAE,GAAG,EAAE;oBAC5B,MAAM,EAAE,QAAQ;iBACjB;aACF,CACF,CAAC;YAEF,eAAM,CAAC,IAAI,CAAC,6BAA6B,CAAC,CAAC;QAC7C,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,oCAAoC,EAAE,EAAE,KAAK,EAAE,CAAC,CAAC;QAChE,CAAC;IACH,CAAC;CACF;AAlQD,wCAkQC;AAGD,WAAW,CAAC,GAAG,EAAE;IACf,cAAc,CAAC,sBAAsB,EAAE,CAAC;AAC1C,CAAC,EAAE,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC"}