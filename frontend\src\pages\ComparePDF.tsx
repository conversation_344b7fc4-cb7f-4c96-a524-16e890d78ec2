import React, { useState } from 'react';
import { Eye, Download, ArrowRight } from 'lucide-react';
import ToolLayout from '../components/ToolLayout';
import FileUpload from '../components/FileUpload';

const ComparePDF = () => {
  const [originalFile, setOriginalFile] = useState<File[]>([]);
  const [modifiedFile, setModifiedFile] = useState<File[]>([]);
  const [isProcessing, setIsProcessing] = useState(false);
  const [comparisonType, setComparisonType] = useState<'text' | 'visual' | 'both'>('both');

  const handleOriginalFileSelect = (selectedFiles: File[]) => {
    setOriginalFile(selectedFiles);
  };

  const handleModifiedFileSelect = (selectedFiles: File[]) => {
    setModifiedFile(selectedFiles);
  };

  const handleCompare = () => {
    if (originalFile.length === 0 || modifiedFile.length === 0) return;
    
    setIsProcessing(true);
    // Simulate processing
    setTimeout(() => {
      setIsProcessing(false);
      alert('Comparaison terminée avec succès!');
    }, 3000);
  };

  return (
    <ToolLayout
      title="Comparer PDF"
      description="Permet de comparer des documents côte à côte, en mettant facilement en évidence les changements entre les différentes versions"
      icon={<Eye className="w-8 h-8" />}
      color="from-green-500 to-green-600"
    >
      <div className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <h3 className="text-lg font-semibold text-slate-700 mb-4">
              Document original
            </h3>
            <FileUpload
              onFileSelect={handleOriginalFileSelect}
              accept=".pdf"
              multiple={false}
              maxFiles={1}
              title="Fichier original"
              description="Glissez-déposez le document original ici"
            />
          </div>

          <div>
            <h3 className="text-lg font-semibold text-slate-700 mb-4">
              Document modifié
            </h3>
            <FileUpload
              onFileSelect={handleModifiedFileSelect}
              accept=".pdf"
              multiple={false}
              maxFiles={1}
              title="Fichier modifié"
              description="Glissez-déposez le document modifié ici"
            />
          </div>
        </div>

        {originalFile.length > 0 && modifiedFile.length > 0 && (
          <div className="bg-slate-50 p-6 rounded-xl">
            <h3 className="text-lg font-semibold text-slate-700 mb-4">
              Options de comparaison
            </h3>
            
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-slate-700 mb-2">
                  Type de comparaison
                </label>
                <div className="space-y-2">
                  <label className="flex items-center space-x-3">
                    <input
                      type="radio"
                      name="comparisonType"
                      value="text"
                      checked={comparisonType === 'text'}
                      onChange={(e) => setComparisonType(e.target.value as 'text')}
                      className="text-green-600"
                    />
                    <span className="text-slate-700">Comparaison textuelle uniquement</span>
                  </label>
                  <label className="flex items-center space-x-3">
                    <input
                      type="radio"
                      name="comparisonType"
                      value="visual"
                      checked={comparisonType === 'visual'}
                      onChange={(e) => setComparisonType(e.target.value as 'visual')}
                      className="text-green-600"
                    />
                    <span className="text-slate-700">Comparaison visuelle uniquement</span>
                  </label>
                  <label className="flex items-center space-x-3">
                    <input
                      type="radio"
                      name="comparisonType"
                      value="both"
                      checked={comparisonType === 'both'}
                      onChange={(e) => setComparisonType(e.target.value as 'both')}
                      className="text-green-600"
                    />
                    <span className="text-slate-700">Comparaison complète (recommandé)</span>
                  </label>
                </div>
              </div>

              <div className="space-y-3">
                <div className="flex items-center space-x-3">
                  <input
                    type="checkbox"
                    id="highlight-changes"
                    defaultChecked
                    className="text-green-600 rounded"
                  />
                  <label htmlFor="highlight-changes" className="text-slate-700">
                    Surligner les modifications
                  </label>
                </div>

                <div className="flex items-center space-x-3">
                  <input
                    type="checkbox"
                    id="show-deletions"
                    defaultChecked
                    className="text-green-600 rounded"
                  />
                  <label htmlFor="show-deletions" className="text-slate-700">
                    Afficher les suppressions
                  </label>
                </div>

                <div className="flex items-center space-x-3">
                  <input
                    type="checkbox"
                    id="show-additions"
                    defaultChecked
                    className="text-green-600 rounded"
                  />
                  <label htmlFor="show-additions" className="text-slate-700">
                    Afficher les ajouts
                  </label>
                </div>

                <div className="flex items-center space-x-3">
                  <input
                    type="checkbox"
                    id="generate-report"
                    className="text-green-600 rounded"
                  />
                  <label htmlFor="generate-report" className="text-slate-700">
                    Générer un rapport de comparaison
                  </label>
                </div>
              </div>

              <div className="bg-blue-50 p-4 rounded-lg">
                <div className="flex items-center space-x-2">
                  <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                  <span className="text-sm text-blue-800 font-medium">Types de changements détectés</span>
                </div>
                <ul className="text-sm text-blue-700 mt-1 space-y-1">
                  <li>• Ajouts de texte (surlignés en vert)</li>
                  <li>• Suppressions de texte (surlignés en rouge)</li>
                  <li>• Modifications de formatage</li>
                  <li>• Changements d'images</li>
                  <li>• Réorganisation de contenu</li>
                </ul>
              </div>
            </div>
          </div>
        )}

        {originalFile.length > 0 && modifiedFile.length > 0 && (
          <div className="flex justify-center">
            <button
              onClick={handleCompare}
              disabled={isProcessing}
              className="bg-gradient-to-r from-green-600 to-teal-600 text-white px-8 py-4 rounded-xl text-lg font-medium hover:shadow-xl transition-all duration-200 transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2"
            >
              {isProcessing ? (
                <>
                  <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white"></div>
                  <span>Comparaison en cours...</span>
                </>
              ) : (
                <>
                  <span>Comparer les documents</span>
                  <ArrowRight className="w-5 h-5" />
                </>
              )}
            </button>
          </div>
        )}
      </div>
    </ToolLayout>
  );
};

export default ComparePDF;