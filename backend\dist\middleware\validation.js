"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.validateFiles = exports.validate = exports.schemas = void 0;
const joi_1 = __importDefault(require("joi"));
const errorHandler_1 = require("./errorHandler");
exports.schemas = {
    fileUpload: joi_1.default.object({
        files: joi_1.default.array().items(joi_1.default.object({
            fieldname: joi_1.default.string().required(),
            originalname: joi_1.default.string().required(),
            encoding: joi_1.default.string().required(),
            mimetype: joi_1.default.string().valid('application/pdf', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', 'application/vnd.ms-excel', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document', 'application/msword', 'application/vnd.openxmlformats-officedocument.presentationml.presentation', 'application/vnd.ms-powerpoint', 'text/html', 'image/jpeg', 'image/png', 'image/bmp', 'image/tiff').required(),
            size: joi_1.default.number().max(50 * 1024 * 1024).required(),
            buffer: joi_1.default.binary().required()
        })).min(1).max(10).required()
    }),
    pdfToolParams: joi_1.default.object({
        toolName: joi_1.default.string().valid('repair_pdf', 'merge_pdf', 'split_pdf', 'compress_pdf', 'excel_to_pdf', 'word_to_pdf', 'powerpoint_to_pdf', 'html_to_pdf', 'pdf_to_excel', 'pdf_to_word', 'pdf_to_powerpoint', 'pdf_to_image', 'pdf_to_pdfa', 'ocr_pdf', 'sign_pdf', 'protect_pdf', 'unlock_pdf', 'watermark_pdf', 'rotate_pdf', 'crop_pdf', 'organize_pdf', 'page_numbers_pdf', 'redact_pdf', 'compare_pdf', 'edit_pdf', 'scan_to_pdf', 'image_to_pdf').required(),
        parameters: joi_1.default.object().optional()
    }),
    userRegistration: joi_1.default.object({
        email: joi_1.default.string().email().required(),
        password: joi_1.default.string().min(8).pattern(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/).required().messages({
            'string.pattern.base': 'Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character'
        }),
        confirmPassword: joi_1.default.string().valid(joi_1.default.ref('password')).required().messages({
            'any.only': 'Passwords do not match'
        }),
        firstName: joi_1.default.string().min(2).max(50).required(),
        lastName: joi_1.default.string().min(2).max(50).required()
    }),
    userLogin: joi_1.default.object({
        email: joi_1.default.string().email().required(),
        password: joi_1.default.string().required()
    }),
    passwordResetRequest: joi_1.default.object({
        email: joi_1.default.string().email().required()
    }),
    passwordReset: joi_1.default.object({
        token: joi_1.default.string().required(),
        password: joi_1.default.string().min(8).pattern(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/).required(),
        confirmPassword: joi_1.default.string().valid(joi_1.default.ref('password')).required()
    })
};
const validate = (schema, property = 'body') => {
    return (req, res, next) => {
        const { error, value } = schema.validate(req[property], {
            abortEarly: false,
            stripUnknown: true
        });
        if (error) {
            const errorMessage = error.details
                .map(detail => detail.message)
                .join(', ');
            throw new errorHandler_1.ValidationError(errorMessage);
        }
        req[property] = value;
        next();
    };
};
exports.validate = validate;
const validateFiles = (req, res, next) => {
    if (!req.files || !Array.isArray(req.files) || req.files.length === 0) {
        throw new errorHandler_1.ValidationError('At least one file is required');
    }
    if (req.files.length > 10) {
        throw new errorHandler_1.ValidationError('Maximum 10 files allowed');
    }
    for (const file of req.files) {
        if (file.size > 50 * 1024 * 1024) {
            throw new errorHandler_1.ValidationError(`File ${file.originalname} exceeds maximum size of 50MB`);
        }
        const toolName = req.body.toolName;
        if (!isValidFileType(file.mimetype, toolName)) {
            throw new errorHandler_1.ValidationError(`File ${file.originalname} has unsupported type for tool ${toolName}`);
        }
    }
    next();
};
exports.validateFiles = validateFiles;
function isValidFileType(mimetype, toolName) {
    const pdfTools = [
        'repair_pdf', 'merge_pdf', 'split_pdf', 'compress_pdf', 'pdf_to_excel',
        'pdf_to_word', 'pdf_to_powerpoint', 'pdf_to_image', 'pdf_to_pdfa',
        'ocr_pdf', 'sign_pdf', 'protect_pdf', 'unlock_pdf', 'watermark_pdf',
        'rotate_pdf', 'crop_pdf', 'organize_pdf', 'page_numbers_pdf',
        'redact_pdf', 'compare_pdf', 'edit_pdf'
    ];
    const officeToTools = ['excel_to_pdf', 'word_to_pdf', 'powerpoint_to_pdf'];
    const imageTools = ['image_to_pdf', 'scan_to_pdf'];
    const htmlTools = ['html_to_pdf'];
    if (pdfTools.includes(toolName)) {
        return mimetype === 'application/pdf';
    }
    if (officeToTools.includes(toolName)) {
        return [
            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            'application/vnd.ms-excel',
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
            'application/msword',
            'application/vnd.openxmlformats-officedocument.presentationml.presentation',
            'application/vnd.ms-powerpoint'
        ].includes(mimetype);
    }
    if (imageTools.includes(toolName)) {
        return [
            'image/jpeg',
            'image/png',
            'image/bmp',
            'image/tiff'
        ].includes(mimetype);
    }
    if (htmlTools.includes(toolName)) {
        return mimetype === 'text/html';
    }
    return false;
}
//# sourceMappingURL=validation.js.map