#!/usr/bin/env python3
"""
Wrapper script for PDF compression tool
"""

import sys
import os
import json
import traceback

# Add the tools directory to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def main():
    if len(sys.argv) != 3 or sys.argv[1] != '--config':
        print("Usage: python compress_pdf_wrapper.py --config <config_file>")
        sys.exit(1)

    config_file = sys.argv[2]

    try:
        # Parse config file
        with open(config_file, 'r') as f:
            config = json.load(f)

        input_files = config['input_files']
        output_dir = config['output_path']
        parameters = config.get('parameters', {})

        # Mock compression - just copy files for now
        import shutil

        output_files = []
        for input_file in input_files:
            filename = os.path.basename(input_file)
            name, ext = os.path.splitext(filename)
            output_file = os.path.join(output_dir, f"{name}_compressed{ext}")

            # Copy file (mock compression)
            shutil.copy2(input_file, output_file)
            output_files.append(output_file)

        # Mock result in expected format
        result = {
            "success": True,
            "output_files": output_files,
            "message": "PDF compression completed successfully (mock)",
            "compression_ratio": 15.0,
            "original_size": sum(os.path.getsize(f) for f in input_files),
            "compressed_size": sum(os.path.getsize(f) for f in output_files),
            "logs": ["Compression started", "Files processed", "Compression completed"]
        }

        # Output result as JSON
        print(json.dumps(result))

    except Exception as e:
        error_result = {
            "success": False,
            "error": str(e),
            "traceback": traceback.format_exc()
        }
        print(json.dumps(error_result))
        sys.exit(1)

if __name__ == "__main__":
    main()
