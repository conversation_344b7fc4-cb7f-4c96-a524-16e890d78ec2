"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const sequelize_1 = require("sequelize");
const errorHandler_1 = require("../middleware/errorHandler");
const auth_1 = require("../middleware/auth");
const logger_1 = require("../utils/logger");
const UsageRecord_1 = require("../models/UsageRecord");
const router = express_1.default.Router();
const PLAN_LIMITS = {
    free: {
        maxFilesPerDay: 10,
        maxFileSizeMB: 10,
        maxProcessingTimePerDay: 300,
        allowedTools: ['repair_pdf', 'merge_pdf', 'compress_pdf', 'split_pdf']
    },
    premium: {
        maxFilesPerDay: 100,
        maxFileSizeMB: 50,
        maxProcessingTimePerDay: 3600,
        allowedTools: ['*']
    },
    enterprise: {
        maxFilesPerDay: 1000,
        maxFileSizeMB: 100,
        maxProcessingTimePerDay: 36000,
        allowedTools: ['*']
    }
};
router.get('/usage', auth_1.authenticate, (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const userId = req.user.id;
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    const todayUsage = await UsageRecord_1.UsageRecord.findAll({
        where: {
            user_id: userId,
            created_at: {
                [sequelize_1.Op.gte]: today
            }
        }
    });
    const totalFilesToday = todayUsage.length;
    const totalProcessingTimeToday = todayUsage.reduce((sum, record) => sum + record.processing_time_seconds, 0);
    const totalFileSizeToday = todayUsage.reduce((sum, record) => sum + Number(record.file_size_mb), 0);
    const planLimits = PLAN_LIMITS[req.user.subscriptionPlan];
    const toolUsage = todayUsage.reduce((acc, record) => {
        acc[record.tool_name] = (acc[record.tool_name] || 0) + 1;
        return acc;
    }, {});
    res.json({
        success: true,
        data: {
            today: {
                files: totalFilesToday,
                processingTimeSeconds: totalProcessingTimeToday,
                fileSizeMB: Math.round(totalFileSizeToday * 100) / 100,
                toolUsage
            },
            limits: {
                maxFilesPerDay: planLimits.maxFilesPerDay,
                maxFileSizeMB: planLimits.maxFileSizeMB,
                maxProcessingTimePerDay: planLimits.maxProcessingTimePerDay
            },
            remaining: {
                files: Math.max(0, planLimits.maxFilesPerDay - totalFilesToday),
                processingTimeSeconds: Math.max(0, planLimits.maxProcessingTimePerDay - totalProcessingTimeToday)
            },
            subscriptionPlan: req.user.subscriptionPlan
        }
    });
}));
router.get('/usage/history', auth_1.authenticate, (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const userId = req.user.id;
    const { days = 7, page = 1, limit = 50 } = req.query;
    const daysAgo = new Date();
    daysAgo.setDate(daysAgo.getDate() - Number(days));
    const offset = (Number(page) - 1) * Number(limit);
    const { count, rows: usageHistory } = await UsageRecord_1.UsageRecord.findAndCountAll({
        where: {
            user_id: userId,
            created_at: {
                [sequelize_1.Op.gte]: daysAgo
            }
        },
        order: [['created_at', 'DESC']],
        limit: Number(limit),
        offset: offset
    });
    res.json({
        success: true,
        data: {
            usage: usageHistory.map(record => ({
                id: record.id,
                toolName: record.tool_name,
                fileSizeMB: Number(record.file_size_mb),
                processingTimeSeconds: record.processing_time_seconds,
                inputFileCount: record.input_file_count,
                outputFileCount: record.output_file_count,
                success: record.success,
                timestamp: record.created_at
            })),
            pagination: {
                page: Number(page),
                limit: Number(limit),
                total: count,
                pages: Math.ceil(count / Number(limit))
            }
        }
    });
}));
router.get('/can-use/:toolName', auth_1.authenticate, (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const { toolName } = req.params;
    const userId = req.user.id;
    const userPlan = req.user.subscriptionPlan;
    const planLimits = PLAN_LIMITS[userPlan];
    const toolAllowed = planLimits.allowedTools.includes('*') ||
        planLimits.allowedTools.includes(toolName);
    if (!toolAllowed) {
        return res.json({
            success: true,
            data: {
                canUse: false,
                reason: 'tool_not_allowed',
                message: `Tool ${toolName} is not available for ${userPlan} plan`,
                upgradeRequired: true
            }
        });
    }
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    const todayUsage = await UsageRecord_1.UsageRecord.findAll({
        where: {
            user_id: userId,
            created_at: {
                [sequelize_1.Op.gte]: today
            }
        }
    });
    const totalFilesToday = todayUsage.length;
    const totalProcessingTimeToday = todayUsage.reduce((sum, record) => sum + record.processing_time_seconds, 0);
    if (totalFilesToday >= planLimits.maxFilesPerDay) {
        return res.json({
            success: true,
            data: {
                canUse: false,
                reason: 'daily_file_limit',
                message: `Daily file limit of ${planLimits.maxFilesPerDay} reached`,
                upgradeRequired: userPlan === 'free'
            }
        });
    }
    if (totalProcessingTimeToday >= planLimits.maxProcessingTimePerDay) {
        return res.json({
            success: true,
            data: {
                canUse: false,
                reason: 'daily_processing_limit',
                message: `Daily processing time limit reached`,
                upgradeRequired: userPlan === 'free'
            }
        });
    }
    res.json({
        success: true,
        data: {
            canUse: true,
            remaining: {
                files: planLimits.maxFilesPerDay - totalFilesToday,
                processingTimeSeconds: planLimits.maxProcessingTimePerDay - totalProcessingTimeToday
            }
        }
    });
}));
router.post('/usage/record', auth_1.authenticate, (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const { toolName, fileSizeMB, processingTimeSeconds, inputFileCount = 1, outputFileCount = 1, success = true, errorMessage, parameters } = req.body;
    const userId = req.user.id;
    const usageRecord = await UsageRecord_1.UsageRecord.create({
        user_id: userId,
        tool_name: toolName,
        file_size_mb: Number(fileSizeMB),
        processing_time_seconds: Number(processingTimeSeconds),
        input_file_count: Number(inputFileCount),
        output_file_count: Number(outputFileCount),
        success: Boolean(success),
        error_message: errorMessage,
        parameters: parameters
    });
    logger_1.logger.info('Usage recorded', {
        userId,
        toolName,
        fileSizeMB,
        processingTimeSeconds,
        success
    });
    res.json({
        success: true,
        data: {
            recorded: true,
            usageId: usageRecord.id
        }
    });
}));
router.get('/plans', (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const plans = [
        {
            id: 'free',
            name: 'Free',
            price: 0,
            currency: 'USD',
            interval: 'month',
            features: [
                '10 files per day',
                'Basic PDF tools',
                '10MB file size limit',
                'Community support'
            ],
            limits: PLAN_LIMITS.free
        },
        {
            id: 'premium',
            name: 'Premium',
            price: 9.99,
            currency: 'USD',
            interval: 'month',
            features: [
                '100 files per day',
                'All PDF tools',
                '50MB file size limit',
                'Priority support',
                'Advanced OCR',
                'Batch processing'
            ],
            limits: PLAN_LIMITS.premium
        },
        {
            id: 'enterprise',
            name: 'Enterprise',
            price: 29.99,
            currency: 'USD',
            interval: 'month',
            features: [
                '1000 files per day',
                'All PDF tools',
                '100MB file size limit',
                'Dedicated support',
                'API access',
                'Custom integrations'
            ],
            limits: PLAN_LIMITS.enterprise
        }
    ];
    res.json({
        success: true,
        data: { plans }
    });
}));
exports.default = router;
//# sourceMappingURL=user.js.map