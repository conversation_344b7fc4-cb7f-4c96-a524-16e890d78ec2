import React, { useState } from 'react';
import { X, User, <PERSON><PERSON><PERSON>3, CreditCard, Check, Crown, Zap, Star, Settings } from 'lucide-react';
import { useAuth } from '../contexts/AuthContext';
import { useUsage } from '../contexts/UsageContext';

interface SettingsModalProps {
  isOpen: boolean;
  onClose: () => void;
  activeTab?: 'profile' | 'usage' | 'billing';
}

const SettingsModal: React.FC<SettingsModalProps> = ({ isOpen, onClose, activeTab = 'profile' }) => {
  const [currentTab, setCurrentTab] = useState(activeTab);
  const [isUpdating, setIsUpdating] = useState(false);
  const [updateMessage, setUpdateMessage] = useState('');
  const { user, updateUser } = useAuth();
  const { usage, history, refreshUsage, getUsageHistory } = useUsage();

  // Update currentTab when activeTab prop changes
  React.useEffect(() => {
    setCurrentTab(activeTab);
  }, [activeTab]);

  if (!isOpen || !user) return null;

  const handleUpgradeToPremium = async () => {
    setIsUpdating(true);
    try {
      const result = await updateUser({
        subscriptionPlan: 'premium'
      });

      if (result.success) {
        setUpdateMessage('Mise à niveau vers Premium réussie!');
        setTimeout(() => setUpdateMessage(''), 3000);
      } else {
        setUpdateMessage('Erreur lors de la mise à niveau: ' + result.error);
      }
    } catch (error) {
      setUpdateMessage('Erreur lors de la mise à niveau');
    } finally {
      setIsUpdating(false);
    }
  };

  const handleUpdateProfile = async (field: string, value: string) => {
    setIsUpdating(true);
    try {
      const result = await updateUser({ [field]: value });

      if (result.success) {
        setUpdateMessage('Profil mis à jour avec succès!');
        setTimeout(() => setUpdateMessage(''), 3000);
      } else {
        setUpdateMessage('Erreur lors de la mise à jour: ' + result.error);
      }
    } catch (error) {
      setUpdateMessage('Erreur lors de la mise à jour');
    } finally {
      setIsUpdating(false);
    }
  };

  const plans = [
    {
      name: 'Gratuit',
      price: '0€',
      period: '/toujours',
      current: user.subscriptionPlan === 'free',
      features: [
        '10 fichiers par jour',
        'Tous les outils PDF',
        'Fichiers jusqu\'à 10MB',
        'Traitement sécurisé',
        'Support communautaire'
      ],
      icon: <Star className="w-6 h-6 text-blue-600" />,
      buttonText: 'Plan actuel',
      buttonDisabled: true
    },
    {
      name: 'Premium',
      price: '9.99€',
      period: '/mois',
      current: user.subscriptionPlan === 'premium',
      popular: true,
      features: [
        '100 fichiers par jour',
        'Tous les outils PDF',
        'Fichiers jusqu\'à 50MB',
        'Traitement prioritaire',
        'Pas de publicité',
        'Support par email',
        'Historique étendu'
      ],
      icon: <Zap className="w-6 h-6 text-blue-600" />,
      buttonText: user.subscriptionPlan === 'premium' ? 'Plan actuel' : 'Mettre à niveau',
      buttonDisabled: user.subscriptionPlan === 'premium'
    },
    {
      name: 'Entreprise',
      price: '29.99€',
      period: '/mois',
      current: user.subscriptionPlan === 'enterprise',
      features: [
        '1000 fichiers par jour',
        'Tous les outils PDF',
        'Fichiers jusqu\'à 100MB',
        'Traitement en lot',
        'Accès API',
        'Intégrations personnalisées',
        'Support prioritaire',
        'SLA garanti'
      ],
      icon: <Crown className="w-6 h-6 text-blue-600" />,
      buttonText: user.subscriptionPlan === 'enterprise' ? 'Plan actuel' : 'Mettre à niveau',
      buttonDisabled: user.subscriptionPlan === 'enterprise'
    }
  ];

  const renderProfileTab = () => (
    <div className="space-y-6">
      <div className="bg-gradient-to-r from-blue-50 to-purple-50 rounded-2xl p-6 border border-blue-100">
        <div className="flex items-center space-x-4">
          <div className="w-16 h-16 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center">
            <User className="w-8 h-8 text-white" />
          </div>
          <div>
            <h3 className="text-xl font-bold text-gray-900">{user.firstName} {user.lastName}</h3>
            <p className="text-gray-600">{user.email}</p>
            <div className="flex items-center space-x-2 mt-1">
              <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                user.subscriptionPlan === 'premium'
                  ? 'bg-yellow-100 text-yellow-800'
                  : 'bg-blue-100 text-blue-800'
              }`}>
                {user.subscriptionPlan === 'premium' ? 'Premium' : 'Gratuit'}
              </span>
              {user.plan === 'premium' && <Crown className="w-4 h-4 text-yellow-500" />}
            </div>
          </div>
        </div>
      </div>

      <div className="space-y-4">
        <div className="grid grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Prénom
            </label>
            <input
              type="text"
              value={user.firstName}
              onChange={(e) => handleUpdateProfile('firstName', e.target.value)}
              disabled={isUpdating}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:bg-gray-50"
            />
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Nom
            </label>
            <input
              type="text"
              value={user.lastName}
              onChange={(e) => handleUpdateProfile('lastName', e.target.value)}
              disabled={isUpdating}
              className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:bg-gray-50"
            />
          </div>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Adresse email
          </label>
          <input
            type="email"
            value={user.email}
            disabled
            className="w-full px-3 py-2 border border-gray-300 rounded-lg bg-gray-50 text-gray-500 cursor-not-allowed"
          />
          <p className="text-xs text-gray-500 mt-1">L'email ne peut pas être modifié</p>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Membre depuis
          </label>
          <input
            type="text"
            value={new Date(user.createdAt).toLocaleDateString('fr-FR')}
            disabled
            className="w-full px-3 py-2 border border-gray-300 rounded-lg bg-gray-50 text-gray-500 cursor-not-allowed"
          />
        </div>
      </div>

      <div className="bg-gray-50 rounded-lg p-4">
        <h4 className="font-medium text-gray-900 mb-2">Préférences</h4>
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <span className="text-sm text-gray-700">Notifications par email</span>
            <input type="checkbox" defaultChecked className="text-blue-600 rounded" />
          </div>
          <div className="flex items-center justify-between">
            <span className="text-sm text-gray-700">Mises à jour produit</span>
            <input type="checkbox" defaultChecked className="text-blue-600 rounded" />
          </div>
          <div className="flex items-center justify-between">
            <span className="text-sm text-gray-700">Conseils et astuces</span>
            <input type="checkbox" className="text-blue-600 rounded" />
          </div>
        </div>
      </div>
    </div>
  );
  const renderUsageTab = () => (
    <div className="space-y-6">
      {/* Usage Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="bg-gradient-to-r from-blue-50 to-purple-50 rounded-2xl p-6 border border-blue-100">
          <div className="text-center">
            <div className="text-3xl font-bold text-blue-600 mb-2">
              {usage?.dailyUsage || 0}
            </div>
            <p className="text-blue-700 font-medium">Utilisé aujourd'hui</p>
            <p className="text-xs text-blue-600 mt-1">
              {usage ? `${usage.dailyLimit - usage.dailyUsage} restants` : 'Chargement...'}
            </p>
          </div>
        </div>

        <div className="bg-gradient-to-r from-green-50 to-emerald-50 rounded-2xl p-6 border border-green-100">
          <div className="text-center">
            <div className="text-3xl font-bold text-green-600 mb-2">
              {usage?.dailyLimit || 0}
            </div>
            <p className="text-green-700 font-medium">Limite quotidienne</p>
            <p className="text-xs text-green-600 mt-1">
              Plan {user.subscriptionPlan}
            </p>
          </div>
        </div>

        <div className="bg-gradient-to-r from-purple-50 to-pink-50 rounded-2xl p-6 border border-purple-100">
          <div className="text-center">
            <div className="text-3xl font-bold text-purple-600 mb-2">
              {usage?.totalUsage || 0}
            </div>
            <p className="text-purple-700 font-medium">Total utilisé</p>
            <p className="text-xs text-purple-600 mt-1">Depuis le début</p>
          </div>
        </div>
      </div>

      {/* Usage History */}
      <div className="bg-white rounded-2xl border border-slate-200 p-6">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold text-slate-800">Historique récent</h3>
          <button
            onClick={() => getUsageHistory(30)}
            className="text-blue-600 hover:text-blue-700 text-sm font-medium"
          >
            Actualiser
          </button>
        </div>

        {history.length > 0 ? (
          <div className="space-y-3 max-h-64 overflow-y-auto">
            {history.slice(0, 10).map((record, index) => (
              <div key={index} className="flex items-center justify-between p-3 bg-slate-50 rounded-lg">
                <div className="flex items-center space-x-3">
                  <div className={`w-3 h-3 rounded-full ${record.success ? 'bg-green-500' : 'bg-red-500'}`}></div>
                  <div>
                    <p className="text-sm font-medium text-slate-800">{record.toolName.replace('_', ' ')}</p>
                    <p className="text-xs text-slate-500">
                      {new Date(record.createdAt).toLocaleDateString('fr-FR')} à {new Date(record.createdAt).toLocaleTimeString('fr-FR')}
                    </p>
                  </div>
                </div>
                <div className="text-right">
                  <p className="text-sm text-slate-600">{record.inputFileCount} fichier(s)</p>
                  <p className="text-xs text-slate-500">{record.fileSizeMB.toFixed(1)} MB</p>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <div className="text-center py-8 text-slate-500">
            <p>Aucun historique d'utilisation</p>
          </div>
        )}
      </div>

      {/* Upgrade Prompt for Free Users */}
      {user.subscriptionPlan === 'free' && (
        <div className="bg-gradient-to-r from-yellow-50 to-orange-50 border border-yellow-200 rounded-2xl p-6">
          <div className="flex items-center space-x-4">
            <div className="w-12 h-12 bg-gradient-to-r from-yellow-400 to-orange-500 rounded-xl flex items-center justify-center">
              <Crown className="w-6 h-6 text-white" />
            </div>
            <div className="flex-1">
              <h3 className="text-lg font-bold text-yellow-900">Passer à Premium</h3>
              <p className="text-yellow-700">Accès illimité et fonctionnalités premium</p>
            </div>
            <button
              onClick={() => setCurrentTab('billing')}
              className="bg-gradient-to-r from-yellow-500 to-orange-500 text-white px-6 py-2 rounded-lg font-medium hover:shadow-lg transition-all duration-200"
            >
              Mettre à niveau
            </button>
          </div>
        </div>
      )}
    </div>
  );

  const renderBillingTab = () => (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        {plans.map((plan, index) => (
          <div
            key={plan.name}
            className={`relative bg-white border-2 rounded-2xl p-6 transition-all duration-200 ${
              plan.current
                ? 'border-blue-500 ring-2 ring-blue-100'
                : 'border-gray-200 hover:border-gray-300'
            }`}
          >
            {plan.current && (
              <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                <span className="bg-blue-500 text-white px-3 py-1 rounded-full text-xs font-medium">
                  Current Plan
                </span>
              </div>
            )}
            
            {plan.popular && !plan.current && (
              <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                <span className="bg-gradient-to-r from-yellow-500 to-orange-500 text-white px-3 py-1 rounded-full text-xs font-medium">
                  Most Popular
                </span>
              </div>
            )}

            <div className="text-center mb-6">
              <div className="w-12 h-12 bg-gradient-to-r from-blue-100 to-purple-100 rounded-xl flex items-center justify-center mx-auto mb-4">
                {plan.icon}
              </div>
              <h3 className="text-xl font-bold text-gray-900 mb-2">{plan.name}</h3>
              <div className="text-3xl font-bold text-gray-900">
                {plan.price}
                <span className="text-sm font-normal text-gray-500">{plan.period}</span>
              </div>
            </div>

            <ul className="space-y-3 mb-6">
              {plan.features.map((feature, featureIndex) => (
                <li key={featureIndex} className="flex items-center gap-3">
                  <Check className="w-4 h-4 text-green-500 flex-shrink-0" />
                  <span className="text-sm text-gray-600">{feature}</span>
                </li>
              ))}
            </ul>

            <button
              disabled={plan.buttonDisabled || isUpdating}
              onClick={() => {
                if (plan.name === 'Premium' && !plan.current) {
                  handleUpgradeToPremium();
                } else if (plan.name === 'Entreprise' && !plan.current) {
                  handleUpdateProfile('subscriptionPlan', 'enterprise');
                }
              }}
              className={`w-full py-3 px-4 rounded-lg font-medium transition-all duration-200 ${
                plan.buttonDisabled || isUpdating
                  ? 'bg-gray-100 text-gray-500 cursor-not-allowed'
                  : 'bg-gradient-to-r from-blue-500 to-purple-600 text-white hover:from-blue-600 hover:to-purple-700 shadow-lg hover:shadow-xl'
              }`}
            >
              {isUpdating ? 'Mise à jour...' : plan.buttonText}
            </button>
          </div>
        ))}
      </div>
    </div>
  );

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 overflow-y-auto">
      <div className="p-4 w-full flex items-center justify-center min-h-full">
        <div className="bg-white rounded-2xl shadow-2xl max-w-4xl w-full max-h-[90vh] overflow-hidden">
          {/* Header */}
          <div className="flex items-center justify-between p-6 border-b border-gray-200">
            <h2 className="text-2xl font-bold text-gray-900">Paramètres</h2>
            <button
              onClick={onClose}
              className="p-2 text-gray-400 hover:text-gray-600 transition-colors"
            >
              <X className="w-5 h-5" />
            </button>
          </div>

          {/* Update Message */}
          {updateMessage && (
            <div className="mx-6 mt-4 p-3 bg-green-50 border border-green-200 rounded-lg">
              <p className="text-green-700 text-sm">{updateMessage}</p>
            </div>
          )}

          {/* Tabs */}
          <div className="flex border-b border-gray-200">
            <button
              onClick={() => setCurrentTab('profile')}
              className={`px-6 py-3 font-medium transition-colors ${
                currentTab === 'profile'
                  ? 'text-blue-600 border-b-2 border-blue-600'
                  : 'text-gray-500 hover:text-gray-700'
              }`}
            >
              Profil
            </button>
            <button
              onClick={() => setCurrentTab('usage')}
              className={`px-6 py-3 font-medium transition-colors ${
                currentTab === 'usage'
                  ? 'text-blue-600 border-b-2 border-blue-600'
                  : 'text-gray-500 hover:text-gray-700'
              }`}
            >
              Utilisation
            </button>
            <button
              onClick={() => setCurrentTab('billing')}
              className={`px-6 py-3 font-medium transition-colors ${
                currentTab === 'billing'
                  ? 'text-blue-600 border-b-2 border-blue-600'
                  : 'text-gray-500 hover:text-gray-700'
              }`}
            >
              Abonnement
            </button>
          </div>

          {/* Content */}
          <div className="p-6 max-h-[60vh] overflow-y-auto">
            {currentTab === 'profile' && renderProfileTab()}
            {currentTab === 'usage' && renderUsageTab()}
            {currentTab === 'billing' && renderBillingTab()}
          </div>
        </div>
      </div>
    </div>
  );
};

export default SettingsModal;