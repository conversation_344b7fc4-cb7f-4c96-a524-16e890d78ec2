import React, { useState } from 'react';
import { X, User, Bar<PERSON>hart3, CreditCard, Check, Crown, Zap, Star, Settings } from 'lucide-react';
import { useAuth } from '../contexts/AuthContext';

interface SettingsModalProps {
  isOpen: boolean;
  onClose: () => void;
  activeTab?: 'profile' | 'usage' | 'billing';
}

const SettingsModal: React.FC<SettingsModalProps> = ({ isOpen, onClose, activeTab = 'profile' }) => {
  const [currentTab, setCurrentTab] = useState(activeTab);
  const { user, updateUser, getRemainingUsage } = useAuth();

  // Update currentTab when activeTab prop changes
  React.useEffect(() => {
    setCurrentTab(activeTab);
  }, [activeTab]);

  if (!isOpen || !user) return null;

  const handleUpgradeToPremium = () => {
    const premiumExpiresAt = new Date();
    premiumExpiresAt.setMonth(premiumExpiresAt.getMonth() + 1);
    
    updateUser({
      plan: 'premium',
      premiumExpiresAt: premiumExpiresAt.toISOString()
    });
  };

  const plans = [
    {
      name: 'Free',
      price: '$0',
      period: '/forever',
      current: user.plan === 'free',
      features: [
        'Up to 3 comics',
        '50 pages per month',
        '500 bubbles per month',
        'Basic text extraction',
        'Export to TXT'
      ],
      icon: <Star className="w-6 h-6 text-blue-600" />,
      buttonText: 'Current Plan',
      buttonDisabled: true
    },
    {
      name: 'Pro',
      price: '$9.99',
      period: '/month',
      current: user.plan === 'premium',
      popular: true,
      features: [
        'Up to 25 comics',
        '500 pages per month',
        '5,000 bubbles per month',
        'Advanced AI extraction',
        'Export to multiple formats',
        'Priority processing',
        'Email support'
      ],
      icon: <Zap className="w-6 h-6 text-blue-600" />,
      buttonText: user.plan === 'premium' ? 'Current Plan' : 'Upgrade',
      buttonDisabled: user.plan === 'premium'
    },
    {
      name: 'Enterprise',
      price: '$29.99',
      period: '/month',
      current: false,
      features: [
        'Unlimited comics',
        '2,000 pages per month',
        '20,000 bubbles per month',
        'Premium AI extraction',
        'Batch processing',
        'API access',
        'Custom integrations',
        'Priority support'
      ],
      icon: <Crown className="w-6 h-6 text-blue-600" />,
      buttonText: 'Upgrade',
      buttonDisabled: false
    }
  ];

  const renderProfileTab = () => (
    <div className="space-y-6">
      <div className="bg-gradient-to-r from-blue-50 to-purple-50 rounded-2xl p-6 border border-blue-100">
        <div className="flex items-center space-x-4">
          <div className="w-16 h-16 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center">
            <User className="w-8 h-8 text-white" />
          </div>
          <div>
            <h3 className="text-xl font-bold text-gray-900">{user.name}</h3>
            <p className="text-gray-600">{user.email}</p>
            <div className="flex items-center space-x-2 mt-1">
              <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                user.plan === 'premium' 
                  ? 'bg-yellow-100 text-yellow-800' 
                  : 'bg-blue-100 text-blue-800'
              }`}>
                {user.plan === 'premium' ? 'Premium' : 'Gratuit'}
              </span>
              {user.plan === 'premium' && <Crown className="w-4 h-4 text-yellow-500" />}
            </div>
          </div>
        </div>
      </div>

      <div className="space-y-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Nom complet
          </label>
          <input
            type="text"
            value={user.name}
            onChange={(e) => updateUser({ name: e.target.value })}
            className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Adresse email
          </label>
          <input
            type="email"
            value={user.email}
            disabled
            className="w-full px-3 py-2 border border-gray-300 rounded-lg bg-gray-50 text-gray-500 cursor-not-allowed"
          />
          <p className="text-xs text-gray-500 mt-1">L'email ne peut pas être modifié</p>
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Membre depuis
          </label>
          <input
            type="text"
            value={new Date(user.createdAt).toLocaleDateString('fr-FR')}
            disabled
            className="w-full px-3 py-2 border border-gray-300 rounded-lg bg-gray-50 text-gray-500 cursor-not-allowed"
          />
        </div>
      </div>

      <div className="bg-gray-50 rounded-lg p-4">
        <h4 className="font-medium text-gray-900 mb-2">Préférences</h4>
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <span className="text-sm text-gray-700">Notifications par email</span>
            <input type="checkbox" defaultChecked className="text-blue-600 rounded" />
          </div>
          <div className="flex items-center justify-between">
            <span className="text-sm text-gray-700">Mises à jour produit</span>
            <input type="checkbox" defaultChecked className="text-blue-600 rounded" />
          </div>
          <div className="flex items-center justify-between">
            <span className="text-sm text-gray-700">Conseils et astuces</span>
            <input type="checkbox" className="text-blue-600 rounded" />
          </div>
        </div>
      </div>
    </div>
  );
  const renderUsageTab = () => (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="bg-gradient-to-r from-blue-50 to-purple-50 rounded-2xl p-6 border border-blue-100">
          <div className="text-center">
            <div className="text-3xl font-bold text-blue-600 mb-2">
              {user.dailyUsage}
            </div>
            <p className="text-blue-700 font-medium">Tools Used Today</p>
            <p className="text-xs text-blue-600 mt-1">
              {user.plan === 'premium' ? 'Unlimited' : `${getRemainingUsage()} remaining`}
            </p>
          </div>
        </div>

        <div className="bg-gradient-to-r from-green-50 to-emerald-50 rounded-2xl p-6 border border-green-100">
          <div className="text-center">
            <div className="text-3xl font-bold text-green-600 mb-2">
              {user.plan === 'premium' ? '∞' : '3'}
            </div>
            <p className="text-green-700 font-medium">Daily Limit</p>
            <p className="text-xs text-green-600 mt-1">
              {user.plan === 'premium' ? 'No limits' : 'Free plan'}
            </p>
          </div>
        </div>

        <div className="bg-gradient-to-r from-purple-50 to-pink-50 rounded-2xl p-6 border border-purple-100">
          <div className="text-center">
            <div className="text-3xl font-bold text-purple-600 mb-2">
              {Math.floor((Date.now() - new Date(user.createdAt).getTime()) / (1000 * 60 * 60 * 24))}
            </div>
            <p className="text-purple-700 font-medium">Days Active</p>
            <p className="text-xs text-purple-600 mt-1">Member since</p>
          </div>
        </div>
      </div>

      {user.plan === 'free' && (
        <div className="bg-gradient-to-r from-yellow-50 to-orange-50 border border-yellow-200 rounded-2xl p-6">
          <div className="flex items-center space-x-4">
            <div className="w-12 h-12 bg-gradient-to-r from-yellow-400 to-orange-500 rounded-xl flex items-center justify-center">
              <Crown className="w-6 h-6 text-white" />
            </div>
            <div className="flex-1">
              <h3 className="text-lg font-bold text-yellow-900">Upgrade to Pro</h3>
              <p className="text-yellow-700">Get unlimited access and premium features</p>
            </div>
            <button
              onClick={() => setCurrentTab('billing')}
              className="bg-gradient-to-r from-yellow-500 to-orange-500 text-white px-6 py-2 rounded-lg font-medium hover:shadow-lg transition-all duration-200"
            >
              Upgrade
            </button>
          </div>
        </div>
      )}
    </div>
  );

  const renderBillingTab = () => (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        {plans.map((plan, index) => (
          <div
            key={plan.name}
            className={`relative bg-white border-2 rounded-2xl p-6 transition-all duration-200 ${
              plan.current
                ? 'border-blue-500 ring-2 ring-blue-100'
                : 'border-gray-200 hover:border-gray-300'
            }`}
          >
            {plan.current && (
              <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                <span className="bg-blue-500 text-white px-3 py-1 rounded-full text-xs font-medium">
                  Current Plan
                </span>
              </div>
            )}
            
            {plan.popular && !plan.current && (
              <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                <span className="bg-gradient-to-r from-yellow-500 to-orange-500 text-white px-3 py-1 rounded-full text-xs font-medium">
                  Most Popular
                </span>
              </div>
            )}

            <div className="text-center mb-6">
              <div className="w-12 h-12 bg-gradient-to-r from-blue-100 to-purple-100 rounded-xl flex items-center justify-center mx-auto mb-4">
                {plan.icon}
              </div>
              <h3 className="text-xl font-bold text-gray-900 mb-2">{plan.name}</h3>
              <div className="text-3xl font-bold text-gray-900">
                {plan.price}
                <span className="text-sm font-normal text-gray-500">{plan.period}</span>
              </div>
            </div>

            <ul className="space-y-3 mb-6">
              {plan.features.map((feature, featureIndex) => (
                <li key={featureIndex} className="flex items-center gap-3">
                  <Check className="w-4 h-4 text-green-500 flex-shrink-0" />
                  <span className="text-sm text-gray-600">{feature}</span>
                </li>
              ))}
            </ul>

            <button
              disabled={plan.buttonDisabled}
              onClick={plan.name === 'Pro' && !plan.current ? handleUpgradeToPremium : undefined}
              className={`w-full py-3 px-4 rounded-lg font-medium transition-all duration-200 ${
                plan.buttonDisabled
                  ? 'bg-gray-100 text-gray-500 cursor-not-allowed'
                  : 'bg-gradient-to-r from-blue-500 to-purple-600 text-white hover:from-blue-600 hover:to-purple-700 shadow-lg hover:shadow-xl'
              }`}
            >
              {plan.buttonText}
            </button>
          </div>
        ))}
      </div>
    </div>
  );

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 overflow-y-auto">
      <div className="p-4 w-full flex items-center justify-center min-h-full">
        <div className="bg-white rounded-2xl shadow-2xl max-w-4xl w-full max-h-[90vh] overflow-hidden">
          {/* Header */}
          <div className="flex items-center justify-between p-6 border-b border-gray-200">
            <h2 className="text-2xl font-bold text-gray-900">Paramètres</h2>
            <button
              onClick={onClose}
              className="p-2 text-gray-400 hover:text-gray-600 transition-colors"
            >
              <X className="w-5 h-5" />
            </button>
          </div>

          {/* Tabs */}
          <div className="flex border-b border-gray-200">
            <button
              onClick={() => setCurrentTab('profile')}
              className={`px-6 py-3 font-medium transition-colors ${
                currentTab === 'profile'
                  ? 'text-blue-600 border-b-2 border-blue-600'
                  : 'text-gray-500 hover:text-gray-700'
              }`}
            >
              Profil
            </button>
            <button
              onClick={() => setCurrentTab('usage')}
              className={`px-6 py-3 font-medium transition-colors ${
                currentTab === 'usage'
                  ? 'text-blue-600 border-b-2 border-blue-600'
                  : 'text-gray-500 hover:text-gray-700'
              }`}
            >
              Utilisation
            </button>
            <button
              onClick={() => setCurrentTab('billing')}
              className={`px-6 py-3 font-medium transition-colors ${
                currentTab === 'billing'
                  ? 'text-blue-600 border-b-2 border-blue-600'
                  : 'text-gray-500 hover:text-gray-700'
              }`}
            >
              Abonnement
            </button>
          </div>

          {/* Content */}
          <div className="p-6 max-h-[60vh] overflow-y-auto">
            {currentTab === 'profile' && renderProfileTab()}
            {currentTab === 'usage' && renderUsageTab()}
            {currentTab === 'billing' && renderBillingTab()}
          </div>
        </div>
      </div>
    </div>
  );
};

export default SettingsModal;