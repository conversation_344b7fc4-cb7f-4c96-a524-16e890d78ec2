import React, { useState } from 'react';
import { Edit, Download, ArrowRight } from 'lucide-react';
import ToolLayout from '../components/ToolLayout';
import FileUpload from '../components/FileUpload';

const EditPDF = () => {
  const [files, setFiles] = useState<File[]>([]);
  const [isProcessing, setIsProcessing] = useState(false);

  const handleFileSelect = (selectedFiles: File[]) => {
    setFiles(selectedFiles);
  };

  const handleEdit = () => {
    if (files.length === 0) return;
    
    setIsProcessing(true);
    // Simulate processing
    setTimeout(() => {
      setIsProcessing(false);
      alert('Éditeur PDF ouvert avec succès!');
    }, 2000);
  };

  return (
    <ToolLayout
      title="Modifier PDF"
      description="Ajouter du texte, des images, des formes ou des annotations manuscrites à un document PDF"
      icon={<Edit className="w-8 h-8" />}
      color="from-violet-500 to-violet-600"
    >
      <div className="space-y-6">
        <FileUpload
          onFileSelect={handleFileSelect}
          accept=".pdf"
          multiple={false}
          maxFiles={1}
          title="Sélectionnez votre fichier PDF"
          description="Glissez-déposez un fichier PDF ici ou cliquez pour sélectionner"
        />

        {files.length > 0 && (
          <div className="bg-slate-50 p-6 rounded-xl">
            <h3 className="text-lg font-semibold text-slate-700 mb-4">
              Outils d'édition disponibles
            </h3>
            
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div className="flex flex-col items-center p-4 bg-white rounded-lg hover:shadow-md transition-shadow">
                <div className="w-12 h-12 bg-violet-100 rounded-lg flex items-center justify-center mb-2">
                  <span className="text-violet-600 font-bold text-xl">T</span>
                </div>
                <span className="text-sm text-slate-700">Texte</span>
              </div>

              <div className="flex flex-col items-center p-4 bg-white rounded-lg hover:shadow-md transition-shadow">
                <div className="w-12 h-12 bg-violet-100 rounded-lg flex items-center justify-center mb-2">
                  <span className="text-violet-600 font-bold text-xl">🖼️</span>
                </div>
                <span className="text-sm text-slate-700">Images</span>
              </div>

              <div className="flex flex-col items-center p-4 bg-white rounded-lg hover:shadow-md transition-shadow">
                <div className="w-12 h-12 bg-violet-100 rounded-lg flex items-center justify-center mb-2">
                  <span className="text-violet-600 font-bold text-xl">⬜</span>
                </div>
                <span className="text-sm text-slate-700">Formes</span>
              </div>

              <div className="flex flex-col items-center p-4 bg-white rounded-lg hover:shadow-md transition-shadow">
                <div className="w-12 h-12 bg-violet-100 rounded-lg flex items-center justify-center mb-2">
                  <span className="text-violet-600 font-bold text-xl">✏️</span>
                </div>
                <span className="text-sm text-slate-700">Annotations</span>
              </div>
            </div>
          </div>
        )}

        {files.length > 0 && (
          <div className="flex justify-center">
            <button
              onClick={handleEdit}
              disabled={isProcessing}
              className="bg-gradient-to-r from-violet-600 to-purple-600 text-white px-8 py-4 rounded-xl text-lg font-medium hover:shadow-xl transition-all duration-200 transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2"
            >
              {isProcessing ? (
                <>
                  <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white"></div>
                  <span>Ouverture de l'éditeur...</span>
                </>
              ) : (
                <>
                  <span>Ouvrir l'éditeur PDF</span>
                  <ArrowRight className="w-5 h-5" />
                </>
              )}
            </button>
          </div>
        )}
      </div>
    </ToolLayout>
  );
};

export default EditPDF;