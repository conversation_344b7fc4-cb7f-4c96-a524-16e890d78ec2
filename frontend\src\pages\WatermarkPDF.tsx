import React, { useState } from 'react';
import { FileText, Download, ArrowRight } from 'lucide-react';
import ToolLayout from '../components/ToolLayout';
import FileUpload from '../components/FileUpload';

const WatermarkPDF = () => {
  const [files, setFiles] = useState<File[]>([]);
  const [isProcessing, setIsProcessing] = useState(false);
  const [watermarkType, setWatermarkType] = useState<'text' | 'image'>('text');
  const [watermarkText, setWatermarkText] = useState('');
  const [transparency, setTransparency] = useState(50);

  const handleFileSelect = (selectedFiles: File[]) => {
    setFiles(selectedFiles);
  };

  const handleWatermark = () => {
    if (files.length === 0) return;
    
    setIsProcessing(true);
    // Simulate processing
    setTimeout(() => {
      setIsProcessing(false);
      alert('Filigrane ajouté avec succès!');
    }, 2000);
  };

  return (
    <ToolLayout
      title="Filigrane"
      description="Choisissez une image ou un texte à appliquer sur votre PDF. Sélectionnez l'emplacement, la transparence et la typographie"
      icon={<FileText className="w-8 h-8" />}
      color="from-lime-500 to-lime-600"
    >
      <div className="space-y-6">
        <FileUpload
          onFileSelect={handleFileSelect}
          accept=".pdf"
          multiple={false}
          maxFiles={1}
          title="Sélectionnez votre fichier PDF"
          description="Glissez-déposez un fichier PDF ici ou cliquez pour sélectionner"
        />

        {files.length > 0 && (
          <div className="bg-slate-50 p-6 rounded-xl">
            <h3 className="text-lg font-semibold text-slate-700 mb-4">
              Configuration du filigrane
            </h3>
            
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-slate-700 mb-2">
                  Type de filigrane
                </label>
                <div className="space-y-2">
                  <label className="flex items-center space-x-3">
                    <input
                      type="radio"
                      name="watermarkType"
                      value="text"
                      checked={watermarkType === 'text'}
                      onChange={(e) => setWatermarkType(e.target.value as 'text')}
                      className="text-lime-600"
                    />
                    <span className="text-slate-700">Texte</span>
                  </label>
                  <label className="flex items-center space-x-3">
                    <input
                      type="radio"
                      name="watermarkType"
                      value="image"
                      checked={watermarkType === 'image'}
                      onChange={(e) => setWatermarkType(e.target.value as 'image')}
                      className="text-lime-600"
                    />
                    <span className="text-slate-700">Image</span>
                  </label>
                </div>
              </div>

              {watermarkType === 'text' && (
                <div>
                  <label className="block text-sm font-medium text-slate-700 mb-2">
                    Texte du filigrane
                  </label>
                  <input
                    type="text"
                    placeholder="Entrez votre texte"
                    value={watermarkText}
                    onChange={(e) => setWatermarkText(e.target.value)}
                    className="w-full p-3 border border-slate-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-lime-500"
                  />
                </div>
              )}

              {watermarkType === 'image' && (
                <div>
                  <label className="block text-sm font-medium text-slate-700 mb-2">
                    Image du filigrane
                  </label>
                  <input
                    type="file"
                    accept=".png,.jpg,.jpeg"
                    className="w-full p-3 border border-slate-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-lime-500"
                  />
                </div>
              )}

              <div>
                <label className="block text-sm font-medium text-slate-700 mb-2">
                  Transparence: {transparency}%
                </label>
                <input
                  type="range"
                  min="10"
                  max="90"
                  value={transparency}
                  onChange={(e) => setTransparency(Number(e.target.value))}
                  className="w-full"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-slate-700 mb-2">
                  Position
                </label>
                <select className="w-full p-3 border border-slate-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-lime-500">
                  <option value="center">Centre</option>
                  <option value="top-left">Haut gauche</option>
                  <option value="top-right">Haut droite</option>
                  <option value="bottom-left">Bas gauche</option>
                  <option value="bottom-right">Bas droite</option>
                </select>
              </div>
            </div>
          </div>
        )}

        {files.length > 0 && (
          <div className="flex justify-center">
            <button
              onClick={handleWatermark}
              disabled={isProcessing}
              className="bg-gradient-to-r from-lime-600 to-green-600 text-white px-8 py-4 rounded-xl text-lg font-medium hover:shadow-xl transition-all duration-200 transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2"
            >
              {isProcessing ? (
                <>
                  <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white"></div>
                  <span>Ajout du filigrane...</span>
                </>
              ) : (
                <>
                  <span>Ajouter le filigrane</span>
                  <ArrowRight className="w-5 h-5" />
                </>
              )}
            </button>
          </div>
        )}
      </div>
    </ToolLayout>
  );
};

export default WatermarkPDF;