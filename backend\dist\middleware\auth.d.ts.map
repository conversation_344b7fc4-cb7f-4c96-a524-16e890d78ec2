{"version": 3, "file": "auth.d.ts", "sourceRoot": "", "sources": ["../../src/middleware/auth.ts"], "names": [], "mappings": "AACA,OAAO,EAAE,OAAO,EAAE,QAAQ,EAAE,YAAY,EAAE,MAAM,SAAS,CAAC;AAI1D,OAAO,CAAC,MAAM,CAAC;IACb,UAAU,OAAO,CAAC;QAChB,UAAU,OAAO;YACf,IAAI,CAAC,EAAE;gBACL,EAAE,EAAE,MAAM,CAAC;gBACX,KAAK,EAAE,MAAM,CAAC;gBACd,gBAAgB,EAAE,MAAM,CAAC;gBACzB,UAAU,EAAE,OAAO,CAAC;aACrB,CAAC;SACH;KACF;CACF;AAED,MAAM,WAAW,UAAU;IACzB,MAAM,EAAE,MAAM,CAAC;IACf,KAAK,EAAE,MAAM,CAAC;IACd,gBAAgB,EAAE,MAAM,CAAC;IACzB,UAAU,EAAE,OAAO,CAAC;IACpB,GAAG,CAAC,EAAE,MAAM,CAAC;IACb,GAAG,CAAC,EAAE,MAAM,CAAC;CACd;AAED,qBAAa,WAAW;IACtB,OAAO,CAAC,MAAM,CAAC,QAAQ,CAAC,UAAU,CAA4D;IAC9F,OAAO,CAAC,MAAM,CAAC,QAAQ,CAAC,cAAc,CAAyC;IAC/E,OAAO,CAAC,MAAM,CAAC,QAAQ,CAAC,wBAAwB,CAAoD;IAEpG,MAAM,CAAC,cAAc,CAAC,OAAO,EAAE,IAAI,CAAC,UAAU,EAAE,KAAK,GAAG,KAAK,CAAC,GAAG;QAAE,WAAW,EAAE,MAAM,CAAC;QAAC,YAAY,EAAE,MAAM,CAAA;KAAE;IAkB9G,MAAM,CAAC,WAAW,CAAC,KAAK,EAAE,MAAM,GAAG,UAAU;IAc7C,MAAM,CAAC,sBAAsB,CAAC,UAAU,CAAC,EAAE,MAAM,GAAG,MAAM;CAY3D;AAGD,eAAO,MAAM,YAAY,GAAI,KAAK,OAAO,EAAE,MAAM,QAAQ,EAAE,MAAM,YAAY,SAgB5E,CAAC;AAGF,eAAO,MAAM,YAAY,GAAI,KAAK,OAAO,EAAE,MAAM,QAAQ,EAAE,MAAM,YAAY,SAmB5E,CAAC;AAGF,eAAO,MAAM,SAAS,GAAI,gBAAe,MAAM,EAAsC,MAC3E,KAAK,OAAO,EAAE,MAAM,QAAQ,EAAE,MAAM,YAAY,SAWzD,CAAC;AAGF,eAAO,MAAM,mBAAmB,GAAI,KAAK,OAAO,EAAE,MAAM,QAAQ,EAAE,MAAM,YAAY,SAUnF,CAAC;AAGF,eAAO,MAAM,aAAa,GAAI,aAAa,MAAM,EAAE,UAAU,MAAM,MAGzD,KAAK,OAAO,EAAE,KAAK,QAAQ,EAAE,MAAM,YAAY,SA8BxD,CAAC"}