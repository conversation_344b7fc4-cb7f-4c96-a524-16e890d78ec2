"""
PDF to PowerPoint conversion tool for converting PDF pages to presentation slides.
"""
import os
import tempfile
from typing import List, Dict, Any, Optional
import structlog

from .base_tool import BasePDFTool, ProcessingError, ValidationError

logger = structlog.get_logger()


class PDFToPowerPointTool(BasePDFTool):
    """Tool for converting PDF files to PowerPoint presentations."""
    
    def __init__(self):
        super().__init__("pdf_to_powerpoint")
        
        # Output formats
        self.output_formats = {
            "pptx": "PowerPoint Presentation (Office 2007+)",
            "ppt": "PowerPoint Presentation (Legacy)",
            "odp": "OpenDocument Presentation"
        }
        
        # Conversion methods
        self.conversion_methods = {
            "image": "Convert pages to images in slides",
            "text": "Extract text and create text-based slides",
            "mixed": "Combine images and text extraction"
        }
    
    async def process(
        self,
        input_files: List[str],
        output_path: str,
        parameters: Optional[Dict[str, Any]] = None
    ) -> List[str]:
        """
        Convert PDF files to PowerPoint presentations.
        
        Args:
            input_files: List of PDF file paths to convert
            output_path: Output directory for PowerPoint files
            parameters: Conversion parameters (format, method, etc.)
            
        Returns:
            List containing paths to converted PowerPoint files
        """
        # Validate inputs
        if len(input_files) == 0:
            raise ValidationError("At least 1 PDF file is required for conversion")
        
        self.validate_input_files(input_files)
        self.validate_pdf_files(input_files)
        
        # Prepare parameters
        params = parameters or {}
        output_format = params.get("output_format", "pptx")
        conversion_method = params.get("conversion_method", "image")
        pages = params.get("pages", "all")
        slide_layout = params.get("slide_layout", "blank")
        
        # Validate parameters
        if output_format not in self.output_formats:
            raise ValidationError(f"Invalid output format: {output_format}. Available: {list(self.output_formats.keys())}")
        
        if conversion_method not in self.conversion_methods:
            raise ValidationError(f"Invalid conversion method: {conversion_method}. Available: {list(self.conversion_methods.keys())}")
        
        # Ensure output directory exists
        self.ensure_output_directory(output_path)
        
        output_files = []
        
        try:
            self.logger.info(
                "Starting PDF to PowerPoint conversion",
                input_count=len(input_files),
                output_format=output_format,
                conversion_method=conversion_method,
                pages=pages
            )
            
            for i, input_file in enumerate(input_files):
                try:
                    self.logger.debug(f"Converting file {i+1}/{len(input_files)}", file_path=input_file)
                    
                    # Generate output filename
                    base_name = os.path.splitext(os.path.basename(input_file))[0]
                    output_filename = f"{base_name}.{output_format}"
                    output_file = os.path.join(output_path, output_filename)
                    
                    # Convert the PDF
                    conversion_result = await self._convert_pdf_to_powerpoint(
                        input_file,
                        output_file,
                        output_format,
                        conversion_method,
                        pages,
                        slide_layout
                    )
                    
                    # Verify output file was created
                    if not os.path.exists(output_file):
                        raise ProcessingError("Failed to create PowerPoint file")
                    
                    output_size = self.get_file_size_mb(output_file)
                    input_size = self.get_file_size_mb(input_file)
                    
                    self.logger.info(
                        f"File {i+1} converted successfully",
                        input_file=input_file,
                        output_file=output_file,
                        slides_created=conversion_result["slides_created"],
                        pages_processed=conversion_result["pages_processed"],
                        input_size_mb=round(input_size, 2),
                        output_size_mb=round(output_size, 2)
                    )
                    
                    output_files.append(output_file)
                    
                except Exception as e:
                    self.logger.error(f"Failed to convert file {i+1}", file_path=input_file, error=str(e))
                    # Clean up any partial output files
                    self.cleanup_files(output_files)
                    raise ProcessingError(f"Failed to convert {os.path.basename(input_file)}: {str(e)}")
            
            total_input_size = sum(self.get_file_size_mb(f) for f in input_files)
            total_output_size = sum(self.get_file_size_mb(f) for f in output_files)
            
            self.logger.info(
                "PDF to PowerPoint conversion completed successfully",
                input_files=len(input_files),
                output_files=len(output_files),
                total_input_size_mb=round(total_input_size, 2),
                total_output_size_mb=round(total_output_size, 2)
            )
            
            return output_files
            
        except Exception as e:
            # Clean up any output files on error
            self.cleanup_files(output_files)
            
            if isinstance(e, (ValidationError, ProcessingError)):
                raise e
            else:
                raise ProcessingError(f"Unexpected error during PDF to PowerPoint conversion: {str(e)}")
    
    async def _convert_pdf_to_powerpoint(
        self,
        input_file: str,
        output_file: str,
        output_format: str,
        conversion_method: str,
        pages: str,
        slide_layout: str
    ) -> Dict[str, Any]:
        """Convert a single PDF file to PowerPoint."""
        try:
            # Use python-pptx for PowerPoint creation
            return await self._convert_with_python_pptx(
                input_file, output_file, output_format, conversion_method, pages, slide_layout
            )
            
        except Exception as e:
            raise ProcessingError(f"Failed to convert PDF to PowerPoint: {str(e)}")
    
    async def _convert_with_python_pptx(
        self,
        input_file: str,
        output_file: str,
        output_format: str,
        conversion_method: str,
        pages: str,
        slide_layout: str
    ) -> Dict[str, Any]:
        """Convert PDF using python-pptx."""
        try:
            import fitz  # PyMuPDF
            from pptx import Presentation
            from pptx.util import Inches
            from PIL import Image
            import io
            
            # Open PDF
            doc = fitz.open(input_file)
            
            if doc.needs_pass:
                doc.close()
                raise ProcessingError("Cannot convert password-protected PDF")
            
            total_pages = len(doc)
            
            # Determine which pages to convert
            pages_to_convert = self._parse_pages_parameter(pages, total_pages)
            
            # Create PowerPoint presentation
            prs = Presentation()
            
            slides_created = 0
            pages_processed = 0
            
            # Process each page
            for page_num in range(total_pages):
                if (page_num + 1) not in pages_to_convert:
                    continue
                
                page = doc[page_num]
                pages_processed += 1
                
                # Add slide
                slide_layout_obj = prs.slide_layouts[6]  # Blank layout
                slide = prs.slides.add_slide(slide_layout_obj)
                
                if conversion_method in ["image", "mixed"]:
                    # Convert page to image
                    mat = fitz.Matrix(2.0, 2.0)  # 2x zoom for better quality
                    pix = page.get_pixmap(matrix=mat)
                    img_data = pix.tobytes("png")
                    
                    # Add image to slide
                    with tempfile.NamedTemporaryFile(suffix=".png", delete=False) as temp_img:
                        temp_img.write(img_data)
                        temp_img_path = temp_img.name
                    
                    try:
                        # Calculate image dimensions to fit slide
                        slide_width = prs.slide_width
                        slide_height = prs.slide_height
                        
                        # Add image with proper sizing
                        left = Inches(0.5)
                        top = Inches(0.5)
                        width = slide_width - Inches(1)
                        height = slide_height - Inches(1)
                        
                        slide.shapes.add_picture(temp_img_path, left, top, width, height)
                        
                    finally:
                        # Clean up temporary image
                        os.unlink(temp_img_path)
                
                if conversion_method in ["text", "mixed"]:
                    # Extract text and add to slide
                    text = page.get_text()
                    
                    if text.strip():
                        # Add text box
                        left = Inches(1)
                        top = Inches(1) if conversion_method == "text" else Inches(5)
                        width = Inches(8)
                        height = Inches(2)
                        
                        textbox = slide.shapes.add_textbox(left, top, width, height)
                        text_frame = textbox.text_frame
                        
                        # Limit text length
                        if len(text) > 500:
                            text = text[:497] + "..."
                        
                        text_frame.text = text
                        
                        # Format text
                        for paragraph in text_frame.paragraphs:
                            paragraph.font.size = Inches(0.15)  # Small font size
                
                # Add slide title
                if hasattr(slide, 'shapes') and len(slide.shapes) > 0:
                    # Try to add a title
                    title_left = Inches(0.5)
                    title_top = Inches(0.1)
                    title_width = Inches(9)
                    title_height = Inches(0.5)
                    
                    title_box = slide.shapes.add_textbox(title_left, title_top, title_width, title_height)
                    title_frame = title_box.text_frame
                    title_frame.text = f"Page {page_num + 1}"
                    
                    # Format title
                    for paragraph in title_frame.paragraphs:
                        paragraph.font.size = Inches(0.2)
                        paragraph.font.bold = True
                
                slides_created += 1
                
                self.logger.debug(f"Created slide for page {page_num + 1}")
            
            doc.close()
            
            # Save presentation
            if output_format == "pptx":
                prs.save(output_file)
            else:
                # For other formats, save as PPTX first then convert if needed
                temp_pptx = output_file.replace(f".{output_format}", ".pptx")
                prs.save(temp_pptx)
                
                if output_format != "pptx":
                    # Try to convert using LibreOffice if available
                    try:
                        await self._convert_pptx_format(temp_pptx, output_file, output_format)
                        os.unlink(temp_pptx)  # Clean up temporary file
                    except Exception as e:
                        # If conversion fails, keep the PPTX file
                        os.rename(temp_pptx, output_file.replace(f".{output_format}", ".pptx"))
                        self.logger.warning(f"Could not convert to {output_format}, saved as PPTX: {str(e)}")
            
            return {
                "slides_created": slides_created,
                "pages_processed": pages_processed,
                "conversion_method": conversion_method
            }
            
        except ImportError:
            raise ProcessingError("python-pptx is required for PowerPoint conversion. Install with: pip install python-pptx")
        except Exception as e:
            raise ProcessingError(f"Failed to convert with python-pptx: {str(e)}")
    
    async def _convert_pptx_format(self, input_pptx: str, output_file: str, output_format: str):
        """Convert PPTX to other formats using LibreOffice."""
        import subprocess
        import shutil
        
        # Check if LibreOffice is available
        libreoffice_cmd = None
        for cmd in ['libreoffice', 'soffice']:
            if shutil.which(cmd):
                libreoffice_cmd = cmd
                break
        
        if not libreoffice_cmd:
            raise ProcessingError("LibreOffice not found for format conversion")
        
        # Create temporary directory for output
        with tempfile.TemporaryDirectory() as temp_dir:
            # Run LibreOffice conversion
            cmd = [
                libreoffice_cmd,
                '--headless',
                '--convert-to', output_format,
                '--outdir', temp_dir,
                input_pptx
            ]
            
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=60)
            
            if result.returncode != 0:
                raise ProcessingError(f"LibreOffice format conversion failed: {result.stderr}")
            
            # Find the generated file
            base_name = os.path.splitext(os.path.basename(input_pptx))[0]
            temp_output = os.path.join(temp_dir, f"{base_name}.{output_format}")
            
            if not os.path.exists(temp_output):
                raise ProcessingError("LibreOffice did not generate expected output file")
            
            # Move to final location
            shutil.move(temp_output, output_file)
    
    def _parse_pages_parameter(self, pages: str, total_pages: int) -> set:
        """Parse the pages parameter to get set of page numbers to convert."""
        if pages == "all":
            return set(range(1, total_pages + 1))
        else:
            # Parse specific pages (e.g., "1,3,5-7,10")
            page_numbers = set()
            
            for part in pages.split(','):
                part = part.strip()
                
                if '-' in part:
                    # Range like "5-7"
                    start_str, end_str = part.split('-', 1)
                    start = int(start_str.strip())
                    end = int(end_str.strip())
                    
                    if start < 1 or end > total_pages or start > end:
                        raise ValidationError(f"Invalid page range: {part}")
                    
                    page_numbers.update(range(start, end + 1))
                else:
                    # Single page like "1"
                    page = int(part)
                    
                    if page < 1 or page > total_pages:
                        raise ValidationError(f"Invalid page number: {page}")
                    
                    page_numbers.add(page)
            
            return page_numbers
    
    def get_conversion_options(self) -> Dict[str, Dict[str, Any]]:
        """Get available conversion options."""
        return {
            "output_format": {
                "description": "Output presentation format",
                "type": "string",
                "options": list(self.output_formats.keys()),
                "default": "pptx",
                "format_descriptions": self.output_formats
            },
            "conversion_method": {
                "description": "Conversion method",
                "type": "string",
                "options": list(self.conversion_methods.keys()),
                "default": "image",
                "method_descriptions": self.conversion_methods
            },
            "pages": {
                "description": "Pages to convert to slides",
                "type": "string",
                "default": "all",
                "example": "1,3,5-7"
            },
            "slide_layout": {
                "description": "Slide layout template",
                "type": "string",
                "options": ["blank", "title", "content"],
                "default": "blank"
            }
        }
    
    def get_output_formats(self) -> Dict[str, str]:
        """Get supported output formats."""
        return self.output_formats.copy()
    
    def get_conversion_methods(self) -> Dict[str, str]:
        """Get available conversion methods."""
        return self.conversion_methods.copy()


# Create tool instance
pdf_to_powerpoint_tool = PDFToPowerPointTool()
