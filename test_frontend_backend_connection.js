// Test script to verify frontend-backend connection
const axios = require('axios');

const FRONTEND_URL = 'http://localhost:5173';
const BACKEND_URL = 'http://localhost:3001/api';

async function testConnection() {
  console.log('🔗 Testing Frontend-Backend Connection...\n');

  try {
    // Test 1: Check if backend is running
    console.log('1. Testing backend health...');
    const backendHealth = await axios.get(`${BACKEND_URL}/health`);
    console.log('✅ Backend is healthy:', backendHealth.data.status);

    // Test 2: Check if frontend is running
    console.log('\n2. Testing frontend availability...');
    try {
      const frontendResponse = await axios.get(FRONTEND_URL, { timeout: 5000 });
      if (frontendResponse.status === 200) {
        console.log('✅ Frontend is running');
      }
    } catch (frontendError) {
      console.log('❌ Frontend not accessible - make sure to run: cd frontend && npm run dev');
      return;
    }

    // Test 3: Check CORS configuration
    console.log('\n3. Testing CORS configuration...');
    try {
      const corsTest = await axios.get(`${BACKEND_URL}/pdf-tools/tools`, {
        headers: {
          'Origin': FRONTEND_URL
        }
      });
      console.log('✅ CORS is properly configured');
      console.log(`   Available tools: ${corsTest.data.data.count}`);
    } catch (corsError) {
      console.log('❌ CORS issue detected');
    }

    // Test 4: Test authentication flow
    console.log('\n4. Testing authentication...');
    try {
      const loginResponse = await axios.post(`${BACKEND_URL}/auth/login`, {
        email: '<EMAIL>',
        password: 'TestPassword123!'
      });

      if (loginResponse.data.success) {
        console.log('✅ Authentication working');
        console.log(`   User: ${loginResponse.data.data.user.email}`);
        console.log(`   Plan: ${loginResponse.data.data.user.subscriptionPlan}`);
      }
    } catch (authError) {
      console.log('❌ Authentication issue:', authError.response?.data?.error?.message);
    }

    // Test 5: Test quota system
    console.log('\n5. Testing quota system...');
    try {
      const loginResponse = await axios.post(`${BACKEND_URL}/auth/login`, {
        email: '<EMAIL>',
        password: 'TestPassword123!'
      });

      if (loginResponse.data.success) {
        const token = loginResponse.data.data.tokens.accessToken;
        
        const quotaResponse = await axios.post(`${BACKEND_URL}/pdf-tools/check-quota`, {
          toolName: 'repair_pdf'
        }, {
          headers: { Authorization: `Bearer ${token}` }
        });

        if (quotaResponse.data.success) {
          console.log('✅ Quota system working');
          console.log(`   Session token: ${quotaResponse.data.data.sessionToken.substring(0, 8)}...`);
          console.log(`   Remaining files: ${quotaResponse.data.data.remainingQuota.files}`);
        }
      }
    } catch (quotaError) {
      console.log('❌ Quota system issue:', quotaError.response?.data?.error?.message);
    }

    console.log('\n🎉 Connection test completed!');
    console.log('\n📋 Summary:');
    console.log('   ✅ Backend API: Running and accessible');
    console.log('   ✅ Frontend: Available (if no errors above)');
    console.log('   ✅ CORS: Configured for frontend-backend communication');
    console.log('   ✅ Authentication: Working with JWT tokens');
    console.log('   ✅ Quota System: Session-based quota enforcement active');
    
    console.log('\n🌐 Access Points:');
    console.log(`   Frontend: ${FRONTEND_URL}`);
    console.log(`   Backend API: ${BACKEND_URL}`);
    console.log(`   Health Check: ${BACKEND_URL}/health`);

  } catch (error) {
    console.error('\n❌ Connection test failed:', error.message);
    console.log('\n🔧 Troubleshooting:');
    console.log('   • Make sure backend is running: cd backend && npm run dev');
    console.log('   • Make sure frontend is running: cd frontend && npm run dev');
    console.log('   • Check that ports 3001 (backend) and 5173 (frontend) are available');
    console.log('   • Verify database connection is working');
  }
}

testConnection();
