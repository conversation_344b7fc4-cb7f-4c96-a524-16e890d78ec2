"""
PDF to Image conversion tool.
"""
import os
import fitz  # PyMuPDF
from typing import List, Dict, Any, Optional
from PIL import Image
import structlog

from .base_tool import Base<PERSON>FTool, ProcessingError, ValidationError

logger = structlog.get_logger()


class PDFToImageTool(BasePDFTool):
    """Tool for converting PDF files to image files (JPG, PNG)."""
    
    def __init__(self):
        super().__init__("pdf_to_image")
        
        # Supported output formats
        self.supported_formats = {
            "jpg": {"extension": ".jpg", "pil_format": "JPEG", "quality_support": True},
            "jpeg": {"extension": ".jpg", "pil_format": "JPEG", "quality_support": True},
            "png": {"extension": ".png", "pil_format": "PNG", "quality_support": False}
        }
    
    async def process(
        self,
        input_files: List[str],
        output_path: str,
        parameters: Optional[Dict[str, Any]] = None
    ) -> List[str]:
        """
        Convert PDF files to image files.
        
        Args:
            input_files: List of PDF file paths to convert
            output_path: Output directory for image files
            parameters: Conversion parameters (format, quality, DPI, etc.)
            
        Returns:
            List containing paths to converted image files
        """
        # Validate inputs
        if len(input_files) == 0:
            raise ValidationError("At least 1 PDF file is required for conversion")
        
        self.validate_input_files(input_files)
        self.validate_pdf_files(input_files)
        
        # Prepare parameters
        params = parameters or {}
        output_format = params.get("output_format", "jpg").lower()
        dpi = params.get("dpi", 150)
        quality = params.get("quality", 85)
        pages = params.get("pages", "all")  # "all", "first", "last", or specific pages
        
        # Validate parameters
        if output_format not in self.supported_formats:
            raise ValidationError(f"Unsupported output format: {output_format}")
        
        if not isinstance(dpi, int) or dpi < 72 or dpi > 600:
            raise ValidationError("DPI must be between 72 and 600")
        
        if not isinstance(quality, int) or quality < 1 or quality > 100:
            raise ValidationError("Quality must be between 1 and 100")
        
        # Ensure output directory exists
        self.ensure_output_directory(output_path)
        
        output_files = []
        
        try:
            self.logger.info(
                "Starting PDF to image conversion",
                input_count=len(input_files),
                output_format=output_format,
                dpi=dpi,
                quality=quality,
                pages=pages
            )
            
            for i, input_file in enumerate(input_files):
                try:
                    self.logger.debug(f"Converting file {i+1}/{len(input_files)}", file_path=input_file)
                    
                    # Convert the PDF
                    file_output_files = await self._convert_pdf_to_images(
                        input_file, 
                        output_path,
                        output_format,
                        dpi,
                        quality,
                        pages
                    )
                    
                    output_files.extend(file_output_files)
                    
                    self.logger.info(
                        f"File {i+1} converted successfully",
                        input_file=input_file,
                        output_images=len(file_output_files)
                    )
                    
                except Exception as e:
                    self.logger.error(f"Failed to convert file {i+1}", file_path=input_file, error=str(e))
                    # Clean up any partial output files
                    self.cleanup_files(output_files)
                    raise ProcessingError(f"Failed to convert {os.path.basename(input_file)}: {str(e)}")
            
            total_input_size = sum(self.get_file_size_mb(f) for f in input_files)
            total_output_size = sum(self.get_file_size_mb(f) for f in output_files if os.path.exists(f))
            
            self.logger.info(
                "PDF to image conversion completed successfully",
                input_files=len(input_files),
                output_images=len(output_files),
                total_input_size_mb=round(total_input_size, 2),
                total_output_size_mb=round(total_output_size, 2)
            )
            
            return output_files
            
        except Exception as e:
            # Clean up any output files on error
            self.cleanup_files(output_files)
            
            if isinstance(e, (ValidationError, ProcessingError)):
                raise e
            else:
                raise ProcessingError(f"Unexpected error during PDF to image conversion: {str(e)}")
    
    async def _convert_pdf_to_images(
        self, 
        input_file: str, 
        output_path: str,
        output_format: str,
        dpi: int,
        quality: int,
        pages: str
    ) -> List[str]:
        """Convert a single PDF file to image files."""
        try:
            # Open the PDF document
            doc = fitz.open(input_file)
            
            # Check if document is encrypted
            if doc.needs_pass:
                doc.close()
                raise ProcessingError("Cannot convert password-protected PDF")
            
            total_pages = len(doc)
            
            # Determine which pages to convert
            page_numbers = self._parse_pages_parameter(pages, total_pages)
            
            output_files = []
            base_name = os.path.splitext(os.path.basename(input_file))[0]
            format_info = self.supported_formats[output_format]
            
            for page_num in page_numbers:
                try:
                    # Get the page (convert to 0-based index)
                    page = doc[page_num - 1]
                    
                    # Create transformation matrix for DPI
                    zoom = dpi / 72.0  # 72 DPI is the default
                    mat = fitz.Matrix(zoom, zoom)
                    
                    # Render page to pixmap
                    pix = page.get_pixmap(matrix=mat)
                    
                    # Convert to PIL Image
                    img_data = pix.tobytes("png")
                    pil_image = Image.open(io.BytesIO(img_data))
                    
                    # Generate output filename
                    if len(page_numbers) == 1:
                        # Single page - no page number in filename
                        output_filename = f"{base_name}{format_info['extension']}"
                    else:
                        # Multiple pages - include page number
                        output_filename = f"{base_name}_page_{page_num:03d}{format_info['extension']}"
                    
                    output_file = os.path.join(output_path, output_filename)
                    
                    # Save the image
                    if format_info["quality_support"]:
                        pil_image.save(output_file, format_info["pil_format"], quality=quality, optimize=True)
                    else:
                        pil_image.save(output_file, format_info["pil_format"], optimize=True)
                    
                    output_files.append(output_file)
                    
                    self.logger.debug(
                        f"Converted page {page_num}",
                        output_file=output_file,
                        size=f"{pil_image.width}x{pil_image.height}"
                    )
                    
                    # Clean up
                    pix = None
                    pil_image.close()
                
                except Exception as e:
                    self.logger.error(f"Failed to convert page {page_num}", error=str(e))
                    raise ProcessingError(f"Failed to convert page {page_num}: {str(e)}")
            
            # Close the PDF document
            doc.close()
            
            return output_files
            
        except Exception as e:
            raise ProcessingError(f"Failed to convert PDF to images: {str(e)}")
    
    def _parse_pages_parameter(self, pages: str, total_pages: int) -> List[int]:
        """Parse the pages parameter to get list of page numbers."""
        if pages == "all":
            return list(range(1, total_pages + 1))
        elif pages == "first":
            return [1] if total_pages > 0 else []
        elif pages == "last":
            return [total_pages] if total_pages > 0 else []
        else:
            # Parse specific pages (e.g., "1,3,5-7,10")
            page_numbers = set()
            
            for part in pages.split(','):
                part = part.strip()
                
                if '-' in part:
                    # Range like "5-7"
                    start_str, end_str = part.split('-', 1)
                    start = int(start_str.strip())
                    end = int(end_str.strip())
                    
                    if start < 1 or end > total_pages or start > end:
                        raise ValidationError(f"Invalid page range: {part}")
                    
                    page_numbers.update(range(start, end + 1))
                else:
                    # Single page like "1"
                    page = int(part)
                    
                    if page < 1 or page > total_pages:
                        raise ValidationError(f"Invalid page number: {page}")
                    
                    page_numbers.add(page)
            
            return sorted(list(page_numbers))
    
    def get_conversion_options(self) -> Dict[str, Dict[str, Any]]:
        """Get available conversion options."""
        return {
            "output_format": {
                "description": "Output image format",
                "type": "string",
                "options": list(self.supported_formats.keys()),
                "default": "jpg"
            },
            "dpi": {
                "description": "Image resolution in DPI",
                "type": "integer",
                "min": 72,
                "max": 600,
                "default": 150
            },
            "quality": {
                "description": "Image quality (1-100, for JPEG only)",
                "type": "integer",
                "min": 1,
                "max": 100,
                "default": 85
            },
            "pages": {
                "description": "Pages to convert",
                "type": "string",
                "options": ["all", "first", "last", "specific (e.g., 1,3,5-7)"],
                "default": "all"
            }
        }
    
    def get_supported_formats(self) -> List[str]:
        """Get list of supported output formats."""
        return list(self.supported_formats.keys())
    
    async def estimate_output_size(self, file_path: str, parameters: Dict[str, Any]) -> Dict[str, Any]:
        """Estimate output size and file count."""
        try:
            with open(file_path, 'rb') as pdf_file:
                doc = fitz.open(pdf_file)
                total_pages = len(doc)
                doc.close()
            
            # Parse pages parameter
            pages = parameters.get("pages", "all")
            page_numbers = self._parse_pages_parameter(pages, total_pages)
            
            # Estimate file size based on DPI and format
            dpi = parameters.get("dpi", 150)
            output_format = parameters.get("output_format", "jpg")
            
            # Rough estimation (typical A4 page at different DPIs)
            size_estimates = {
                72: {"jpg": 0.1, "png": 0.3},    # MB per page
                150: {"jpg": 0.3, "png": 0.8},
                300: {"jpg": 1.0, "png": 2.5},
                600: {"jpg": 3.0, "png": 8.0}
            }
            
            # Find closest DPI estimate
            closest_dpi = min(size_estimates.keys(), key=lambda x: abs(x - dpi))
            estimated_size_per_page = size_estimates[closest_dpi].get(output_format, 0.5)
            
            total_estimated_size = len(page_numbers) * estimated_size_per_page
            
            return {
                "total_pages": total_pages,
                "pages_to_convert": len(page_numbers),
                "estimated_total_size_mb": round(total_estimated_size, 2),
                "estimated_size_per_page_mb": round(estimated_size_per_page, 2),
                "output_format": output_format,
                "dpi": dpi
            }
            
        except Exception as e:
            self.logger.error("Failed to estimate output size", file_path=file_path, error=str(e))
            return {"error": str(e)}


# Add missing import
import io

# Create tool instance
pdf_to_image_tool = PDFToImageTool()
