import { DataTypes, Model, Optional } from 'sequelize';
import { sequelize } from '../config/database';
import { User } from './User';

// Processing session attributes interface
export interface ProcessingSessionAttributes {
  id: string;
  user_id: string;
  session_token: string;
  status: 'active' | 'completed' | 'expired';
  files_processed: number;
  quota_consumed: number;
  can_download: boolean;
  expires_at: Date;
  created_at?: Date;
  updated_at?: Date;
}

// Processing session creation attributes
export interface ProcessingSessionCreationAttributes extends Optional<ProcessingSessionAttributes, 
  'id' | 'status' | 'files_processed' | 'quota_consumed' | 'can_download' | 'created_at' | 'updated_at'> {}

// Processing session model class
export class ProcessingSession extends Model<ProcessingSessionAttributes, ProcessingSessionCreationAttributes> 
  implements ProcessingSessionAttributes {
  public id!: string;
  public user_id!: string;
  public session_token!: string;
  public status!: 'active' | 'completed' | 'expired';
  public files_processed!: number;
  public quota_consumed!: number;
  public can_download!: boolean;
  public expires_at!: Date;

  // Timestamps
  public readonly created_at!: Date;
  public readonly updated_at!: Date;

  // Associations
  public readonly user?: User;

  // Instance methods
  public isActive(): boolean {
    return this.status === 'active' && new Date() < this.expires_at;
  }

  public markCompleted(): void {
    this.status = 'completed';
    this.can_download = true;
  }

  public incrementUsage(): void {
    this.files_processed += 1;
    this.quota_consumed += 1;
  }

  public canProcessMore(userQuota: number): boolean {
    // Allow processing if session is active and user hasn't exceeded their daily quota
    // when the session started
    return this.isActive() && this.quota_consumed < userQuota;
  }
}

// Initialize ProcessingSession model
ProcessingSession.init(
  {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true,
    },
    user_id: {
      type: DataTypes.UUID,
      allowNull: false,
      references: {
        model: User,
        key: 'id',
      },
      onUpdate: 'CASCADE',
      onDelete: 'CASCADE',
    },
    session_token: {
      type: DataTypes.STRING(255),
      allowNull: false,
      unique: true,
    },
    status: {
      type: DataTypes.ENUM('active', 'completed', 'expired'),
      allowNull: false,
      defaultValue: 'active',
    },
    files_processed: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 0,
      validate: {
        min: 0,
      },
    },
    quota_consumed: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 0,
      validate: {
        min: 0,
      },
    },
    can_download: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: true,
    },
    expires_at: {
      type: DataTypes.DATE,
      allowNull: false,
    },
  },
  {
    sequelize,
    modelName: 'ProcessingSession',
    tableName: 'processing_sessions',
    timestamps: true,
    underscored: true,
    indexes: [
      {
        fields: ['user_id'],
      },
      {
        fields: ['session_token'],
        unique: true,
      },
      {
        fields: ['status'],
      },
      {
        fields: ['expires_at'],
      },
      {
        fields: ['user_id', 'status'],
      },
    ],
  }
);

export default ProcessingSession;
