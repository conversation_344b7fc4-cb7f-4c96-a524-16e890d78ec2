"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const errorHandler_1 = require("../middleware/errorHandler");
const validation_1 = require("../middleware/validation");
const upload_1 = require("../middleware/upload");
const pythonExecutor_1 = require("../services/pythonExecutor");
const sessionManager_1 = require("../services/sessionManager");
const auth_1 = require("../middleware/auth");
const logger_1 = require("../utils/logger");
const uuid_1 = require("uuid");
const path_1 = __importDefault(require("path"));
const router = express_1.default.Router();
router.get('/tools', (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const tools = await pythonExecutor_1.pythonExecutor.getAvailableTools();
    const toolMetadata = {
        repair_pdf: {
            name: 'Repair PDF',
            description: 'Fix corrupted or damaged PDF files',
            inputTypes: ['application/pdf'],
            parameters: {
                strategy: { type: 'string', options: ['basic', 'advanced', 'reconstruct'], default: 'advanced' },
                preserve_metadata: { type: 'boolean', default: true }
            }
        },
        merge_pdf: {
            name: 'Merge PDFs',
            description: 'Combine multiple PDF files into one',
            inputTypes: ['application/pdf'],
            parameters: {
                bookmark_titles: { type: 'array', description: 'Custom bookmark titles for each PDF' }
            }
        },
        compress_pdf: {
            name: 'Compress PDF',
            description: 'Reduce PDF file size',
            inputTypes: ['application/pdf'],
            parameters: {
                quality: { type: 'string', options: ['low', 'medium', 'high'], default: 'medium' },
                optimize_images: { type: 'boolean', default: true }
            }
        },
        excel_to_pdf: {
            name: 'Excel to PDF',
            description: 'Convert Excel spreadsheets to PDF',
            inputTypes: ['application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', 'application/vnd.ms-excel'],
            parameters: {
                orientation: { type: 'string', options: ['portrait', 'landscape', 'auto'], default: 'portrait' },
                sheets: { type: 'string', default: 'all' },
                include_gridlines: { type: 'boolean', default: true }
            }
        },
        ocr_pdf: {
            name: 'OCR PDF',
            description: 'Extract text from scanned PDFs using OCR',
            inputTypes: ['application/pdf', 'image/jpeg', 'image/png'],
            parameters: {
                language: { type: 'string', options: ['eng', 'fra', 'deu', 'spa'], default: 'eng' },
                output_format: { type: 'string', options: ['pdf', 'text', 'docx', 'json'], default: 'pdf' },
                dpi: { type: 'number', min: 150, max: 600, default: 300 }
            }
        }
    };
    const availableTools = tools.map(tool => ({
        id: tool,
        ...toolMetadata[tool] || {
            name: tool.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase()),
            description: `${tool.replace(/_/g, ' ')} tool`,
            inputTypes: ['application/pdf'],
            parameters: {}
        }
    }));
    res.json({
        success: true,
        data: {
            tools: availableTools,
            count: availableTools.length
        }
    });
}));
router.post('/check-quota', auth_1.authenticate, (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const { toolName } = req.body;
    const userId = req.user.id;
    const subscriptionPlan = req.user.subscriptionPlan;
    if (!toolName) {
        return res.status(400).json({
            success: false,
            error: {
                message: 'Tool name is required',
                type: 'ValidationError'
            }
        });
    }
    const quotaCheck = await sessionManager_1.SessionManager.canStartNewSession(userId, subscriptionPlan, toolName);
    if (quotaCheck.canProcess) {
        res.json({
            success: true,
            data: {
                canProcess: true,
                sessionToken: quotaCheck.sessionToken,
                remainingQuota: quotaCheck.remainingQuota
            }
        });
    }
    else {
        res.status(429).json({
            success: false,
            error: {
                message: quotaCheck.message,
                type: 'QuotaExceeded',
                reason: quotaCheck.reason,
                upgradeRequired: quotaCheck.upgradeRequired
            }
        });
    }
}));
router.post('/process', upload_1.upload.array('files', 10), validation_1.validateFiles, (0, validation_1.validate)(validation_1.schemas.pdfToolParams), auth_1.optionalAuth, (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const { toolName, sessionToken } = req.body;
    let parameters = {};
    if (req.body.parameters) {
        try {
            parameters = typeof req.body.parameters === 'string'
                ? JSON.parse(req.body.parameters)
                : req.body.parameters;
        }
        catch (error) {
            return res.status(400).json({
                success: false,
                error: {
                    message: 'Invalid parameters format',
                    type: 'ValidationError'
                }
            });
        }
    }
    const files = req.files;
    const jobId = (0, uuid_1.v4)();
    const userId = req.user?.id;
    logger_1.logger.info('Starting PDF tool processing', {
        jobId,
        toolName,
        fileCount: files.length,
        parameters,
        sessionToken,
        userId
    });
    try {
        if (userId && sessionToken) {
            const sessionCheck = await sessionManager_1.SessionManager.canContinueSession(sessionToken, userId);
            if (!sessionCheck.canProcess) {
                return res.status(429).json({
                    success: false,
                    error: {
                        message: sessionCheck.message,
                        type: 'SessionError',
                        reason: sessionCheck.reason
                    }
                });
            }
        }
        const totalFileSizeMB = files.reduce((sum, file) => sum + file.size, 0) / (1024 * 1024);
        const inputFiles = await upload_1.FileManager.saveUploadedFiles(files, userId);
        const outputPath = await upload_1.FileManager.createOutputDirectory(userId);
        const startTime = Date.now();
        const result = await pythonExecutor_1.pythonExecutor.executeTool({
            toolName,
            inputFiles,
            outputPath,
            parameters,
            timeout: 300000
        });
        const processingTimeSeconds = Math.round((Date.now() - startTime) / 1000);
        if (userId && sessionToken) {
            await sessionManager_1.SessionManager.recordProcessing(sessionToken, userId, toolName, totalFileSizeMB, processingTimeSeconds, files.length, result.outputFiles.length, result.success, result.error);
        }
        if (result.success) {
            upload_1.FileManager.scheduleCleanup(inputFiles, 3600000);
            const downloadUrls = result.outputFiles.map(filePath => {
                const relativePath = path_1.default.relative(upload_1.FileManager.getProcessedPath(), filePath);
                const downloadUrl = `/api/pdf-tools/download/${encodeURIComponent(relativePath)}`;
                return sessionToken ? `${downloadUrl}?session=${sessionToken}` : downloadUrl;
            });
            res.json({
                success: true,
                data: {
                    jobId,
                    sessionToken,
                    outputFiles: result.outputFiles.map((filePath, index) => ({
                        path: filePath,
                        name: path_1.default.basename(filePath),
                        downloadUrl: downloadUrls[index]
                    })),
                    processingTime: result.processingTime,
                    logs: result.logs,
                    quotaConsumed: userId && sessionToken ? true : false
                }
            });
        }
        else {
            await upload_1.FileManager.cleanupFiles(inputFiles);
            res.status(422).json({
                success: false,
                error: {
                    message: result.error || 'Processing failed',
                    type: 'ProcessingError'
                },
                data: {
                    jobId,
                    sessionToken,
                    processingTime: result.processingTime
                }
            });
        }
    }
    catch (error) {
        logger_1.logger.error('PDF tool processing failed', {
            jobId,
            toolName,
            error: error instanceof Error ? error.message : String(error)
        });
        res.status(500).json({
            success: false,
            error: {
                message: 'Internal processing error',
                type: 'InternalError'
            },
            data: { jobId, sessionToken }
        });
    }
}));
router.get('/download/:filePath', auth_1.optionalAuth, (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const filePath = decodeURIComponent(req.params.filePath);
    const sessionToken = req.query.session;
    const userId = req.user?.id;
    const fullPath = path_1.default.join(upload_1.FileManager.getProcessedPath(), filePath);
    const resolvedPath = path_1.default.resolve(fullPath);
    const processedDir = path_1.default.resolve(upload_1.FileManager.getProcessedPath());
    if (!resolvedPath.startsWith(processedDir)) {
        return res.status(403).json({
            success: false,
            error: {
                message: 'Access denied',
                type: 'SecurityError'
            }
        });
    }
    if (userId && sessionToken) {
        const canDownload = await sessionManager_1.SessionManager.canDownload(sessionToken, userId);
        if (!canDownload) {
            return res.status(403).json({
                success: false,
                error: {
                    message: 'Download not allowed for this session',
                    type: 'SessionError'
                }
            });
        }
    }
    const fileInfo = await upload_1.FileManager.getFileInfo(fullPath);
    if (!fileInfo.exists) {
        return res.status(404).json({
            success: false,
            error: {
                message: 'File not found',
                type: 'NotFoundError'
            }
        });
    }
    const fileName = path_1.default.basename(fullPath);
    const fileExtension = path_1.default.extname(fileName).toLowerCase();
    let contentType = 'application/octet-stream';
    if (fileExtension === '.pdf') {
        contentType = 'application/pdf';
    }
    else if (fileExtension === '.txt') {
        contentType = 'text/plain';
    }
    else if (fileExtension === '.json') {
        contentType = 'application/json';
    }
    else if (fileExtension === '.docx') {
        contentType = 'application/vnd.openxmlformats-officedocument.wordprocessingml.document';
    }
    res.setHeader('Content-Type', contentType);
    res.setHeader('Content-Disposition', `attachment; filename="${fileName}"`);
    res.setHeader('Content-Length', fileInfo.size || 0);
    res.sendFile(fullPath);
}));
router.post('/complete-session', auth_1.authenticate, (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const { sessionToken } = req.body;
    const userId = req.user.id;
    if (!sessionToken) {
        return res.status(400).json({
            success: false,
            error: {
                message: 'Session token is required',
                type: 'ValidationError'
            }
        });
    }
    await sessionManager_1.SessionManager.completeSession(sessionToken, userId);
    res.json({
        success: true,
        data: {
            message: 'Session completed successfully',
            sessionToken
        }
    });
}));
router.get('/status/:jobId', (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const { jobId } = req.params;
    res.json({
        success: true,
        data: {
            jobId,
            status: 'completed',
            message: 'Job status endpoint - to be implemented with database'
        }
    });
}));
exports.default = router;
//# sourceMappingURL=pdfTools.js.map