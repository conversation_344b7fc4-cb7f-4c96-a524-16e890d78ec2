"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const errorHandler_1 = require("../middleware/errorHandler");
const validation_1 = require("../middleware/validation");
const upload_1 = require("../middleware/upload");
const pythonExecutor_1 = require("../services/pythonExecutor");
const logger_1 = require("../utils/logger");
const uuid_1 = require("uuid");
const path_1 = __importDefault(require("path"));
const router = express_1.default.Router();
router.get('/tools', (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const tools = await pythonExecutor_1.pythonExecutor.getAvailableTools();
    const toolMetadata = {
        repair_pdf: {
            name: 'Repair PDF',
            description: 'Fix corrupted or damaged PDF files',
            inputTypes: ['application/pdf'],
            parameters: {
                strategy: { type: 'string', options: ['basic', 'advanced', 'reconstruct'], default: 'advanced' },
                preserve_metadata: { type: 'boolean', default: true }
            }
        },
        merge_pdf: {
            name: 'Merge PDFs',
            description: 'Combine multiple PDF files into one',
            inputTypes: ['application/pdf'],
            parameters: {
                bookmark_titles: { type: 'array', description: 'Custom bookmark titles for each PDF' }
            }
        },
        compress_pdf: {
            name: 'Compress PDF',
            description: 'Reduce PDF file size',
            inputTypes: ['application/pdf'],
            parameters: {
                quality: { type: 'string', options: ['low', 'medium', 'high'], default: 'medium' },
                optimize_images: { type: 'boolean', default: true }
            }
        },
        excel_to_pdf: {
            name: 'Excel to PDF',
            description: 'Convert Excel spreadsheets to PDF',
            inputTypes: ['application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', 'application/vnd.ms-excel'],
            parameters: {
                orientation: { type: 'string', options: ['portrait', 'landscape', 'auto'], default: 'portrait' },
                sheets: { type: 'string', default: 'all' },
                include_gridlines: { type: 'boolean', default: true }
            }
        },
        ocr_pdf: {
            name: 'OCR PDF',
            description: 'Extract text from scanned PDFs using OCR',
            inputTypes: ['application/pdf', 'image/jpeg', 'image/png'],
            parameters: {
                language: { type: 'string', options: ['eng', 'fra', 'deu', 'spa'], default: 'eng' },
                output_format: { type: 'string', options: ['pdf', 'text', 'docx', 'json'], default: 'pdf' },
                dpi: { type: 'number', min: 150, max: 600, default: 300 }
            }
        }
    };
    const availableTools = tools.map(tool => ({
        id: tool,
        ...toolMetadata[tool] || {
            name: tool.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase()),
            description: `${tool.replace(/_/g, ' ')} tool`,
            inputTypes: ['application/pdf'],
            parameters: {}
        }
    }));
    res.json({
        success: true,
        data: {
            tools: availableTools,
            count: availableTools.length
        }
    });
}));
router.post('/process', upload_1.upload.array('files', 10), validation_1.validateFiles, (0, validation_1.validate)(validation_1.schemas.pdfToolParams), (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const { toolName, parameters } = req.body;
    const files = req.files;
    const jobId = (0, uuid_1.v4)();
    logger_1.logger.info('Starting PDF tool processing', {
        jobId,
        toolName,
        fileCount: files.length,
        parameters
    });
    try {
        const inputFiles = await upload_1.FileManager.saveUploadedFiles(files, req.user?.id);
        const outputPath = await upload_1.FileManager.createOutputDirectory(req.user?.id);
        const result = await pythonExecutor_1.pythonExecutor.executeTool({
            toolName,
            inputFiles,
            outputPath,
            parameters,
            timeout: 300000
        });
        if (result.success) {
            upload_1.FileManager.scheduleCleanup(inputFiles, 3600000);
            const downloadUrls = result.outputFiles.map(filePath => {
                const relativePath = path_1.default.relative(upload_1.FileManager.getProcessedPath(), filePath);
                return `/api/pdf-tools/download/${encodeURIComponent(relativePath)}`;
            });
            res.json({
                success: true,
                data: {
                    jobId,
                    outputFiles: result.outputFiles.map((filePath, index) => ({
                        path: filePath,
                        name: path_1.default.basename(filePath),
                        downloadUrl: downloadUrls[index]
                    })),
                    processingTime: result.processingTime,
                    logs: result.logs
                }
            });
        }
        else {
            await upload_1.FileManager.cleanupFiles(inputFiles);
            res.status(422).json({
                success: false,
                error: {
                    message: result.error || 'Processing failed',
                    type: 'ProcessingError'
                },
                data: {
                    jobId,
                    processingTime: result.processingTime
                }
            });
        }
    }
    catch (error) {
        logger_1.logger.error('PDF tool processing failed', {
            jobId,
            toolName,
            error: error instanceof Error ? error.message : String(error)
        });
        res.status(500).json({
            success: false,
            error: {
                message: 'Internal processing error',
                type: 'InternalError'
            },
            data: { jobId }
        });
    }
}));
router.get('/download/:filePath', (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const filePath = decodeURIComponent(req.params.filePath);
    const fullPath = path_1.default.join(upload_1.FileManager.getProcessedPath(), filePath);
    const resolvedPath = path_1.default.resolve(fullPath);
    const processedDir = path_1.default.resolve(upload_1.FileManager.getProcessedPath());
    if (!resolvedPath.startsWith(processedDir)) {
        return res.status(403).json({
            success: false,
            error: {
                message: 'Access denied',
                type: 'SecurityError'
            }
        });
    }
    const fileInfo = await upload_1.FileManager.getFileInfo(fullPath);
    if (!fileInfo.exists) {
        return res.status(404).json({
            success: false,
            error: {
                message: 'File not found',
                type: 'NotFoundError'
            }
        });
    }
    const fileName = path_1.default.basename(fullPath);
    const fileExtension = path_1.default.extname(fileName).toLowerCase();
    let contentType = 'application/octet-stream';
    if (fileExtension === '.pdf') {
        contentType = 'application/pdf';
    }
    else if (fileExtension === '.txt') {
        contentType = 'text/plain';
    }
    else if (fileExtension === '.json') {
        contentType = 'application/json';
    }
    else if (fileExtension === '.docx') {
        contentType = 'application/vnd.openxmlformats-officedocument.wordprocessingml.document';
    }
    res.setHeader('Content-Type', contentType);
    res.setHeader('Content-Disposition', `attachment; filename="${fileName}"`);
    res.setHeader('Content-Length', fileInfo.size || 0);
    res.sendFile(fullPath);
}));
router.get('/status/:jobId', (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const { jobId } = req.params;
    res.json({
        success: true,
        data: {
            jobId,
            status: 'completed',
            message: 'Job status endpoint - to be implemented with database'
        }
    });
}));
exports.default = router;
//# sourceMappingURL=pdfTools.js.map