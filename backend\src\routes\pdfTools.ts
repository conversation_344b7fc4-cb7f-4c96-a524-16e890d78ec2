import express from 'express';
import { as<PERSON><PERSON><PERSON><PERSON> } from '../middleware/errorHandler';
import { validate, validateFiles, schemas } from '../middleware/validation';
import { upload, FileManager } from '../middleware/upload';
import { pythonExecutor } from '../services/pythonExecutor';
import { SessionManager } from '../services/sessionManager';
import { authenticate, optionalAuth } from '../middleware/auth';
import { logger } from '../utils/logger';
import { v4 as uuidv4 } from 'uuid';
import path from 'path';

const router = express.Router();

// Get available PDF tools
router.get('/tools', asyncHandler(async (req, res) => {
  const tools = await pythonExecutor.getAvailableTools();
  
  // Tool metadata
  const toolMetadata = {
    repair_pdf: {
      name: 'Repair PDF',
      description: 'Fix corrupted or damaged PDF files',
      inputTypes: ['application/pdf'],
      parameters: {
        strategy: { type: 'string', options: ['basic', 'advanced', 'reconstruct'], default: 'advanced' },
        preserve_metadata: { type: 'boolean', default: true }
      }
    },
    merge_pdf: {
      name: 'Merge PDFs',
      description: 'Combine multiple PDF files into one',
      inputTypes: ['application/pdf'],
      parameters: {
        bookmark_titles: { type: 'array', description: 'Custom bookmark titles for each PDF' }
      }
    },
    compress_pdf: {
      name: 'Compress PDF',
      description: 'Reduce PDF file size',
      inputTypes: ['application/pdf'],
      parameters: {
        quality: { type: 'string', options: ['low', 'medium', 'high'], default: 'medium' },
        optimize_images: { type: 'boolean', default: true }
      }
    },
    excel_to_pdf: {
      name: 'Excel to PDF',
      description: 'Convert Excel spreadsheets to PDF',
      inputTypes: ['application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', 'application/vnd.ms-excel'],
      parameters: {
        orientation: { type: 'string', options: ['portrait', 'landscape', 'auto'], default: 'portrait' },
        sheets: { type: 'string', default: 'all' },
        include_gridlines: { type: 'boolean', default: true }
      }
    },
    ocr_pdf: {
      name: 'OCR PDF',
      description: 'Extract text from scanned PDFs using OCR',
      inputTypes: ['application/pdf', 'image/jpeg', 'image/png'],
      parameters: {
        language: { type: 'string', options: ['eng', 'fra', 'deu', 'spa'], default: 'eng' },
        output_format: { type: 'string', options: ['pdf', 'text', 'docx', 'json'], default: 'pdf' },
        dpi: { type: 'number', min: 150, max: 600, default: 300 }
      }
    }
  };

  const availableTools = tools.map(tool => ({
    id: tool,
    ...toolMetadata[tool as keyof typeof toolMetadata] || {
      name: tool.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase()),
      description: `${tool.replace(/_/g, ' ')} tool`,
      inputTypes: ['application/pdf'],
      parameters: {}
    }
  }));

  res.json({
    success: true,
    data: {
      tools: availableTools,
      count: availableTools.length
    }
  });
}));

// Check quota and start new session
router.post('/check-quota',
  optionalAuth,
  asyncHandler(async (req: any, res: any) => {
    const { toolName } = req.body;
    const userId = req.user?.id;
    const subscriptionPlan = req.user?.subscriptionPlan || 'guest';

    if (!toolName) {
      return res.status(400).json({
        success: false,
        error: {
          message: 'Tool name is required',
          type: 'ValidationError'
        }
      });
    }

    const quotaCheck = await SessionManager.canStartNewSession(
      userId,
      subscriptionPlan as any,
      toolName,
      req
    );

    if (quotaCheck.canProcess) {
      res.json({
        success: true,
        data: {
          canProcess: true,
          sessionToken: quotaCheck.sessionToken,
          remainingQuota: quotaCheck.remainingQuota
        }
      });
    } else {
      res.status(429).json({
        success: false,
        error: {
          message: quotaCheck.message,
          type: 'QuotaExceeded',
          reason: quotaCheck.reason,
          upgradeRequired: quotaCheck.upgradeRequired
        }
      });
    }
  })
);

// Process files with a PDF tool
router.post('/process',
  upload.array('files', 10),
  validateFiles,
  optionalAuth,
  asyncHandler(async (req: any, res: any) => {
    const { toolName, sessionToken } = req.body;
    let parameters = {};

    // Parse parameters if provided (comes as string from FormData)
    if (req.body.parameters) {
      try {
        parameters = typeof req.body.parameters === 'string'
          ? JSON.parse(req.body.parameters)
          : req.body.parameters;
      } catch (error) {
        return res.status(400).json({
          success: false,
          error: {
            message: 'Invalid parameters format',
            type: 'ValidationError'
          }
        });
      }
    }

    // Validate tool name
    const validTools = [
      'repair_pdf', 'merge_pdf', 'split_pdf', 'compress_pdf',
      'excel_to_pdf', 'word_to_pdf', 'powerpoint_to_pdf', 'html_to_pdf',
      'pdf_to_excel', 'pdf_to_word', 'pdf_to_powerpoint', 'pdf_to_image',
      'pdf_to_pdfa', 'ocr_pdf', 'sign_pdf', 'protect_pdf', 'unlock_pdf',
      'watermark_pdf', 'rotate_pdf', 'crop_pdf', 'organize_pdf',
      'page_numbers_pdf', 'redact_pdf', 'compare_pdf', 'edit_pdf',
      'scan_to_pdf', 'image_to_pdf'
    ];

    if (!toolName || !validTools.includes(toolName)) {
      return res.status(400).json({
        success: false,
        error: {
          message: 'Invalid or missing tool name',
          type: 'ValidationError'
        }
      });
    }

    const files = req.files as Express.Multer.File[];
    const jobId = uuidv4();
    const userId = req.user?.id;

    logger.info('Starting PDF tool processing', {
      jobId,
      toolName,
      fileCount: files.length,
      parameters,
      sessionToken,
      userId
    });

    try {
      // Check session if user is authenticated
      if (userId && sessionToken) {
        const sessionCheck = await SessionManager.canContinueSession(sessionToken, userId);
        if (!sessionCheck.canProcess) {
          return res.status(429).json({
            success: false,
            error: {
              message: sessionCheck.message,
              type: 'SessionError',
              reason: sessionCheck.reason
            }
          });
        }
      }

      // Calculate total file size
      const totalFileSizeMB = files.reduce((sum, file) => sum + file.size, 0) / (1024 * 1024);

      // Save uploaded files
      const inputFiles = await FileManager.saveUploadedFiles(files, userId);

      // Create output directory
      const outputPath = await FileManager.createOutputDirectory(userId);

      const startTime = Date.now();

      // Execute Python tool
      const result = await pythonExecutor.executeTool({
        toolName,
        inputFiles,
        outputPath,
        parameters,
        timeout: 300000 // 5 minutes
      });

      const processingTimeSeconds = Math.round((Date.now() - startTime) / 1000);

      // Record usage in session (for both authenticated and guest users)
      if (sessionToken) {
        await SessionManager.recordProcessing(
          sessionToken,
          userId,
          toolName,
          totalFileSizeMB,
          processingTimeSeconds,
          files.length,
          result.outputFiles.length,
          result.success,
          result.error,
          req
        );
      }

      if (result.success) {
        // Schedule cleanup of input files
        FileManager.scheduleCleanup(inputFiles, 3600000); // 1 hour

        // Generate download URLs for output files
        const downloadUrls = result.outputFiles.map(filePath => {
          const relativePath = path.relative(FileManager.getProcessedPath(), filePath);
          const downloadUrl = `/api/pdf-tools/download/${encodeURIComponent(relativePath)}`;
          return sessionToken ? `${downloadUrl}?session=${sessionToken}` : downloadUrl;
        });

        res.json({
          success: true,
          data: {
            jobId,
            sessionToken,
            outputFiles: result.outputFiles.map((filePath, index) => ({
              path: filePath,
              name: path.basename(filePath),
              downloadUrl: downloadUrls[index]
            })),
            processingTime: result.processingTime,
            logs: result.logs,
            quotaConsumed: userId && sessionToken ? true : false
          }
        });
      } else {
        // Clean up input files immediately on failure
        await FileManager.cleanupFiles(inputFiles);

        res.status(422).json({
          success: false,
          error: {
            message: result.error || 'Processing failed',
            type: 'ProcessingError'
          },
          data: {
            jobId,
            sessionToken,
            processingTime: result.processingTime
          }
        });
      }

    } catch (error) {
      logger.error('PDF tool processing failed', {
        jobId,
        toolName,
        error: error instanceof Error ? error.message : String(error)
      });

      res.status(500).json({
        success: false,
        error: {
          message: 'Internal processing error',
          type: 'InternalError'
        },
        data: { jobId, sessionToken }
      });
    }
  })
);

// Download processed file
router.get('/download/:filePath',
  optionalAuth,
  asyncHandler(async (req: any, res: any) => {
    const filePath = decodeURIComponent(req.params.filePath);
    const sessionToken = req.query.session;
    const userId = req.user?.id;
    const fullPath = path.join(FileManager.getProcessedPath(), filePath);

    // Security check: ensure file is within processed directory
    const resolvedPath = path.resolve(fullPath);
    const processedDir = path.resolve(FileManager.getProcessedPath());

    if (!resolvedPath.startsWith(processedDir)) {
      return res.status(403).json({
        success: false,
        error: {
          message: 'Access denied',
          type: 'SecurityError'
        }
      });
    }

    // Check session permission if provided
    if (userId && sessionToken) {
      const canDownload = await SessionManager.canDownload(sessionToken, userId);
      if (!canDownload) {
        return res.status(403).json({
          success: false,
          error: {
            message: 'Download not allowed for this session',
            type: 'SessionError'
          }
        });
      }
    }

    // Check if file exists
    const fileInfo = await FileManager.getFileInfo(fullPath);
    if (!fileInfo.exists) {
      return res.status(404).json({
        success: false,
        error: {
          message: 'File not found',
          type: 'NotFoundError'
        }
      });
    }

    // Set appropriate headers
    const fileName = path.basename(fullPath);
    const fileExtension = path.extname(fileName).toLowerCase();

    let contentType = 'application/octet-stream';
    if (fileExtension === '.pdf') {
      contentType = 'application/pdf';
    } else if (fileExtension === '.txt') {
      contentType = 'text/plain';
    } else if (fileExtension === '.json') {
      contentType = 'application/json';
    } else if (fileExtension === '.docx') {
      contentType = 'application/vnd.openxmlformats-officedocument.wordprocessingml.document';
    }

    res.setHeader('Content-Type', contentType);
    res.setHeader('Content-Disposition', `attachment; filename="${fileName}"`);
    res.setHeader('Content-Length', fileInfo.size || 0);

    // Stream the file
    res.sendFile(fullPath);
  })
);

// Complete processing session
router.post('/complete-session',
  optionalAuth,
  asyncHandler(async (req: any, res: any) => {
    const { sessionToken } = req.body;
    const userId = req.user?.id;

    if (!sessionToken) {
      return res.status(400).json({
        success: false,
        error: {
          message: 'Session token is required',
          type: 'ValidationError'
        }
      });
    }

    await SessionManager.completeSession(sessionToken, userId);

    res.json({
      success: true,
      data: {
        message: 'Session completed successfully',
        sessionToken
      }
    });
  })
);

// Get processing status (for future async processing)
router.get('/status/:jobId', asyncHandler(async (req, res) => {
  const { jobId } = req.params;
  
  // This would typically check a database or cache for job status
  // For now, return a simple response
  res.json({
    success: true,
    data: {
      jobId,
      status: 'completed', // pending, processing, completed, failed
      message: 'Job status endpoint - to be implemented with database'
    }
  });
}));

export default router;
