import { ApiService } from './apiClient';

// Quota Service for graceful quota enforcement
class QuotaService {
  constructor() {
    this.currentSession = null;
  }

  /**
   * Check quota and start new processing session
   */
  async checkQuotaAndStartSession(toolName) {
    try {
      const response = await ApiService.checkQuota(toolName);

      if (response.success) {
        this.currentSession = {
          token: response.data.sessionToken,
          toolName,
          remainingQuota: response.data.remainingQuota,
          startTime: new Date()
        };

        return {
          canProcess: true,
          sessionToken: response.data.sessionToken,
          remainingQuota: response.data.remainingQuota
        };
      }

      return {
        canProcess: false,
        error: response.error
      };

    } catch (error) {
      if (error.response?.status === 429) {
        return {
          canProcess: false,
          error: error.response.data.error
        };
      }
      throw error;
    }
  }

  /**
   * Process files with session token
   */
  async processFiles(files, toolName, parameters = {}) {
    if (!this.currentSession || this.currentSession.toolName !== toolName) {
      throw new Error('No valid session for this tool. Please check quota first.');
    }

    try {
      const response = await ApiService.processFiles(
        files,
        toolName,
        this.currentSession.token,
        parameters
      );

      return response;
    } catch (error) {
      if (error.response?.status === 429) {
        // Session expired or quota exceeded during processing
        this.currentSession = null;
        throw new Error('Session expired. Please start a new session.');
      }
      throw error;
    }
  }

  /**
   * Complete current session after downloads
   */
  async completeSession() {
    if (!this.currentSession) {
      return;
    }

    try {
      await ApiService.completeSession(this.currentSession.token);

      // Clear current session
      const completedSession = this.currentSession;
      this.currentSession = null;

      return completedSession;
    } catch (error) {
      console.warn('Failed to complete session:', error);
      this.currentSession = null;
    }
  }

  /**
   * Get current session info
   */
  getCurrentSession() {
    return this.currentSession;
  }

  /**
   * Clear current session
   */
  clearSession() {
    this.currentSession = null;
  }
}

// Example usage in a React component
const ExampleUsage = `
import { useState } from 'react';
import { quotaService } from './services/quotaService';

function PDFProcessor() {
  const [files, setFiles] = useState([]);
  const [processing, setProcessing] = useState(false);
  const [results, setResults] = useState(null);
  const [quotaWarning, setQuotaWarning] = useState(null);

  const handleProcess = async (toolName) => {
    try {
      setProcessing(true);
      
      // Step 1: Check quota and start session
      const quotaCheck = await quotaService.checkQuotaAndStartSession(toolName);
      
      if (!quotaCheck.canProcess) {
        // Show quota exceeded error BEFORE processing
        setQuotaWarning({
          type: 'quota_exceeded',
          message: quotaCheck.error.message,
          upgradeRequired: quotaCheck.error.upgradeRequired
        });
        return;
      }

      // Step 2: Process files with session
      const result = await quotaService.processFiles(files, toolName);
      
      if (result.success) {
        setResults(result.data);
        
        // Don't show quota warning yet - let user download first
      }

    } catch (error) {
      console.error('Processing failed:', error);
    } finally {
      setProcessing(false);
    }
  };

  const handleDownloadComplete = async () => {
    // Step 3: Complete session after downloads
    const completedSession = await quotaService.completeSession();
    
    // Step 4: NOW show quota warning if needed
    if (completedSession && completedSession.remainingQuota) {
      const { files: remainingFiles } = completedSession.remainingQuota;
      
      if (remainingFiles <= 2) {
        setQuotaWarning({
          type: 'quota_warning',
          message: \`You have \${remainingFiles} files remaining in your daily quota.\`,
          upgradeRequired: remainingFiles === 0
        });
      }
    }
  };

  return (
    <div>
      {/* File upload and processing UI */}
      
      {results && (
        <div>
          {/* Download buttons */}
          {results.outputFiles.map(file => (
            <a 
              key={file.path}
              href={file.downloadUrl}
              download={file.name}
              onClick={handleDownloadComplete}
            >
              Download {file.name}
            </a>
          ))}
        </div>
      )}
      
      {/* Show quota warning AFTER downloads */}
      {quotaWarning && (
        <div className="quota-warning">
          <p>{quotaWarning.message}</p>
          {quotaWarning.upgradeRequired && (
            <button>Upgrade Plan</button>
          )}
        </div>
      )}
    </div>
  );
}
`;

// Create singleton instance
const quotaService = new QuotaService();

export default QuotaService;
export { quotaService, ExampleUsage };
