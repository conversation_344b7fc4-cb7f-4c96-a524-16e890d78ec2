import { sequelize } from '../config/database';
import { User } from './User';
import { UsageRecord } from './UsageRecord';
import { ProcessingJob } from './ProcessingJob';
import { ProcessingSession } from './ProcessingSession';

// Define associations
User.hasMany(UsageRecord, {
  foreignKey: 'user_id',
  as: 'usageRecords',
  onDelete: 'CASCADE',
});

UsageRecord.belongsTo(User, {
  foreignKey: 'user_id',
  as: 'user',
});

User.hasMany(ProcessingJob, {
  foreignKey: 'user_id',
  as: 'processingJobs',
  onDelete: 'CASCADE',
});

ProcessingJob.belongsTo(User, {
  foreignKey: 'user_id',
  as: 'user',
});

User.hasMany(ProcessingSession, {
  foreignKey: 'user_id',
  as: 'processingSessions',
  onDelete: 'CASCADE',
});

ProcessingSession.belongsTo(User, {
  foreignKey: 'user_id',
  as: 'user',
});

// Export models and sequelize instance
export {
  sequelize,
  User,
  UsageRecord,
  ProcessingJob,
  ProcessingSession,
};

// Export default object with all models
export default {
  sequelize,
  User,
  UsageRecord,
  ProcessingJob,
  ProcessingSession,
};
