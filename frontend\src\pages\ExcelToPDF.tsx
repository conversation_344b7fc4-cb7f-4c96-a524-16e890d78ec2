import React, { useState } from 'react';
import { FileText, Download, ArrowRight } from 'lucide-react';
import ToolLayout from '../components/ToolLayout';
import FileUpload from '../components/FileUpload';

const ExcelToPDF = () => {
  const [files, setFiles] = useState<File[]>([]);
  const [isProcessing, setIsProcessing] = useState(false);

  const handleFileSelect = (selectedFiles: File[]) => {
    setFiles(selectedFiles);
  };

  const handleConvert = () => {
    if (files.length === 0) return;
    
    setIsProcessing(true);
    // Simulate processing
    setTimeout(() => {
      setIsProcessing(false);
      alert('Feuille de calcul Excel convertie en PDF avec succès!');
    }, 2000);
  };

  return (
    <ToolLayout
      title="Excel en PDF"
      description="Facilitez la lecture de vos feuilles de calcul EXCEL en les convertissant en PDF"
      icon={<FileText className="w-8 h-8" />}
      color="from-teal-500 to-teal-600"
    >
      <div className="space-y-6">
        <FileUpload
          onFileSelect={handleFileSelect}
          accept=".xls,.xlsx"
          multiple={false}
          maxFiles={1}
          title="Sélectionnez votre feuille de calcul Excel"
          description="Glissez-déposez un fichier XLS ou XLSX ici ou cliquez pour sélectionner"
        />

        {files.length > 0 && (
          <div className="bg-slate-50 p-6 rounded-xl">
            <h3 className="text-lg font-semibold text-slate-700 mb-4">
              Options de conversion
            </h3>
            
            <div className="space-y-4">
              <div className="flex items-center space-x-3">
                <input
                  type="checkbox"
                  id="all-sheets"
                  defaultChecked
                  className="text-teal-600 rounded"
                />
                <label htmlFor="all-sheets" className="text-slate-700">
                  Inclure toutes les feuilles de calcul
                </label>
              </div>

              <div className="flex items-center space-x-3">
                <input
                  type="checkbox"
                  id="preserve-formatting"
                  defaultChecked
                  className="text-teal-600 rounded"
                />
                <label htmlFor="preserve-formatting" className="text-slate-700">
                  Préserver le formatage et les couleurs
                </label>
              </div>

              <div className="flex items-center space-x-3">
                <input
                  type="checkbox"
                  id="fit-to-page"
                  defaultChecked
                  className="text-teal-600 rounded"
                />
                <label htmlFor="fit-to-page" className="text-slate-700">
                  Ajuster à la page
                </label>
              </div>

              <div className="flex items-center space-x-3">
                <input
                  type="checkbox"
                  id="include-headers"
                  defaultChecked
                  className="text-teal-600 rounded"
                />
                <label htmlFor="include-headers" className="text-slate-700">
                  Inclure les en-têtes et pieds de page
                </label>
              </div>
            </div>
          </div>
        )}

        {files.length > 0 && (
          <div className="flex justify-center">
            <button
              onClick={handleConvert}
              disabled={isProcessing}
              className="bg-gradient-to-r from-teal-600 to-green-600 text-white px-8 py-4 rounded-xl text-lg font-medium hover:shadow-xl transition-all duration-200 transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2"
            >
              {isProcessing ? (
                <>
                  <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white"></div>
                  <span>Conversion en cours...</span>
                </>
              ) : (
                <>
                  <span>Convertir en PDF</span>
                  <ArrowRight className="w-5 h-5" />
                </>
              )}
            </button>
          </div>
        )}
      </div>
    </ToolLayout>
  );
};

export default ExcelToPDF;