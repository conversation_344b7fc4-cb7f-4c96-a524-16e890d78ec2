{"version": 3, "file": "auth.js", "sourceRoot": "", "sources": ["../../src/middleware/auth.ts"], "names": [], "mappings": ";;;;;;AAAA,gEAA+B;AAE/B,iDAAyE;AAyBzE,MAAa,WAAW;IAKtB,MAAM,CAAC,cAAc,CAAC,OAAwC;QAC5D,MAAM,WAAW,GAAG,sBAAG,CAAC,IAAI,CAC1B,OAAiB,EACjB,IAAI,CAAC,UAAwB,EAC7B;YACE,SAAS,EAAE,IAAI,CAAC,cAAc;SAC/B,CACF,CAAC;QAEF,MAAM,YAAY,GAAG,sBAAG,CAAC,IAAI,CAC3B,EAAE,MAAM,EAAE,OAAO,CAAC,MAAM,EAAY,EACpC,IAAI,CAAC,UAAwB,EAC7B,EAAE,SAAS,EAAE,IAAI,CAAC,wBAAwB,EAAE,CAC7C,CAAC;QAEF,OAAO,EAAE,WAAW,EAAE,YAAY,EAAE,CAAC;IACvC,CAAC;IAED,MAAM,CAAC,WAAW,CAAC,KAAa;QAC9B,IAAI,CAAC;YACH,OAAO,sBAAG,CAAC,MAAM,CAAC,KAAK,EAAE,IAAI,CAAC,UAAwB,CAAe,CAAC;QACxE,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IAAI,KAAK,YAAY,sBAAG,CAAC,iBAAiB,EAAE,CAAC;gBAC3C,MAAM,IAAI,kCAAmB,CAAC,mBAAmB,CAAC,CAAC;YACrD,CAAC;iBAAM,IAAI,KAAK,YAAY,sBAAG,CAAC,iBAAiB,EAAE,CAAC;gBAClD,MAAM,IAAI,kCAAmB,CAAC,eAAe,CAAC,CAAC;YACjD,CAAC;iBAAM,CAAC;gBACN,MAAM,IAAI,kCAAmB,CAAC,2BAA2B,CAAC,CAAC;YAC7D,CAAC;QACH,CAAC;IACH,CAAC;IAED,MAAM,CAAC,sBAAsB,CAAC,UAAmB;QAC/C,IAAI,CAAC,UAAU,EAAE,CAAC;YAChB,MAAM,IAAI,kCAAmB,CAAC,kCAAkC,CAAC,CAAC;QACpE,CAAC;QAED,MAAM,KAAK,GAAG,UAAU,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;QACpC,IAAI,KAAK,CAAC,MAAM,KAAK,CAAC,IAAI,KAAK,CAAC,CAAC,CAAC,KAAK,QAAQ,EAAE,CAAC;YAChD,MAAM,IAAI,kCAAmB,CAAC,wDAAwD,CAAC,CAAC;QAC1F,CAAC;QAED,OAAO,KAAK,CAAC,CAAC,CAAE,CAAC;IACnB,CAAC;;AAhDH,kCAiDC;AAhDyB,sBAAU,GAAG,OAAO,CAAC,GAAG,CAAC,YAAY,CAAC,IAAI,2BAA2B,CAAC;AACtE,0BAAc,GAAG,OAAO,CAAC,GAAG,CAAC,gBAAgB,CAAC,IAAI,IAAI,CAAC;AACvD,oCAAwB,GAAG,OAAO,CAAC,GAAG,CAAC,0BAA0B,CAAC,IAAI,KAAK,CAAC;AAiD/F,MAAM,YAAY,GAAG,CAAC,GAAY,EAAE,IAAc,EAAE,IAAkB,EAAE,EAAE;IAC/E,IAAI,CAAC;QACH,MAAM,KAAK,GAAG,WAAW,CAAC,sBAAsB,CAAC,GAAG,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC;QAC5E,MAAM,OAAO,GAAG,WAAW,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;QAE/C,GAAG,CAAC,IAAI,GAAG;YACT,EAAE,EAAE,OAAO,CAAC,MAAM;YAClB,KAAK,EAAE,OAAO,CAAC,KAAK;YACpB,gBAAgB,EAAE,OAAO,CAAC,gBAAgB;YAC1C,UAAU,EAAE,OAAO,CAAC,UAAU;SAC/B,CAAC;QAEF,IAAI,EAAE,CAAC;IACT,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,IAAI,CAAC,KAAK,CAAC,CAAC;IACd,CAAC;AACH,CAAC,CAAC;AAhBW,QAAA,YAAY,gBAgBvB;AAGK,MAAM,YAAY,GAAG,CAAC,GAAY,EAAE,IAAc,EAAE,IAAkB,EAAE,EAAE;IAC/E,IAAI,CAAC;QACH,MAAM,UAAU,GAAG,GAAG,CAAC,OAAO,CAAC,aAAa,CAAC;QAC7C,IAAI,UAAU,EAAE,CAAC;YACf,MAAM,KAAK,GAAG,WAAW,CAAC,sBAAsB,CAAC,UAAU,CAAC,CAAC;YAC7D,MAAM,OAAO,GAAG,WAAW,CAAC,WAAW,CAAC,KAAK,CAAC,CAAC;YAE/C,GAAG,CAAC,IAAI,GAAG;gBACT,EAAE,EAAE,OAAO,CAAC,MAAM;gBAClB,KAAK,EAAE,OAAO,CAAC,KAAK;gBACpB,gBAAgB,EAAE,OAAO,CAAC,gBAAgB;gBAC1C,UAAU,EAAE,OAAO,CAAC,UAAU;aAC/B,CAAC;QACJ,CAAC;QACD,IAAI,EAAE,CAAC;IACT,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QAEf,IAAI,EAAE,CAAC;IACT,CAAC;AACH,CAAC,CAAC;AAnBW,QAAA,YAAY,gBAmBvB;AAGK,MAAM,SAAS,GAAG,CAAC,gBAA0B,CAAC,MAAM,EAAE,SAAS,EAAE,YAAY,CAAC,EAAE,EAAE;IACvF,OAAO,CAAC,GAAY,EAAE,IAAc,EAAE,IAAkB,EAAE,EAAE;QAC1D,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC;YACd,MAAM,IAAI,kCAAmB,CAAC,yBAAyB,CAAC,CAAC;QAC3D,CAAC;QAED,IAAI,CAAC,aAAa,CAAC,QAAQ,CAAC,GAAG,CAAC,IAAI,CAAC,gBAAgB,CAAC,EAAE,CAAC;YACvD,MAAM,IAAI,iCAAkB,CAAC,qDAAqD,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;QAChH,CAAC;QAED,IAAI,EAAE,CAAC;IACT,CAAC,CAAC;AACJ,CAAC,CAAC;AAZW,QAAA,SAAS,aAYpB;AAGK,MAAM,mBAAmB,GAAG,CAAC,GAAY,EAAE,IAAc,EAAE,IAAkB,EAAE,EAAE;IACtF,IAAI,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC;QACd,MAAM,IAAI,kCAAmB,CAAC,yBAAyB,CAAC,CAAC;IAC3D,CAAC;IAED,IAAI,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC;QACzB,MAAM,IAAI,iCAAkB,CAAC,6BAA6B,CAAC,CAAC;IAC9D,CAAC;IAED,IAAI,EAAE,CAAC;AACT,CAAC,CAAC;AAVW,QAAA,mBAAmB,uBAU9B;AAGK,MAAM,aAAa,GAAG,CAAC,WAAmB,EAAE,QAAgB,EAAE,EAAE;IACrE,MAAM,YAAY,GAAG,IAAI,GAAG,EAAgD,CAAC;IAE7E,OAAO,CAAC,GAAY,EAAE,GAAa,EAAE,IAAkB,EAAE,EAAE;QACzD,MAAM,MAAM,GAAG,GAAG,CAAC,IAAI,EAAE,EAAE,IAAI,GAAG,CAAC,EAAE,IAAI,WAAW,CAAC;QACrD,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAEvB,MAAM,SAAS,GAAG,YAAY,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;QAE3C,IAAI,CAAC,SAAS,IAAI,GAAG,GAAG,SAAS,CAAC,SAAS,EAAE,CAAC;YAE5C,YAAY,CAAC,GAAG,CAAC,MAAM,EAAE;gBACvB,KAAK,EAAE,CAAC;gBACR,SAAS,EAAE,GAAG,GAAG,QAAQ;aAC1B,CAAC,CAAC;YACH,IAAI,EAAE,CAAC;QACT,CAAC;aAAM,IAAI,SAAS,CAAC,KAAK,GAAG,WAAW,EAAE,CAAC;YAEzC,SAAS,CAAC,KAAK,EAAE,CAAC;YAClB,IAAI,EAAE,CAAC;QACT,CAAC;aAAM,CAAC;YAEN,MAAM,OAAO,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,SAAS,CAAC,SAAS,GAAG,GAAG,CAAC,GAAG,IAAI,CAAC,CAAC;YAC9D,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;gBACnB,OAAO,EAAE,KAAK;gBACd,KAAK,EAAE;oBACL,OAAO,EAAE,qCAAqC,OAAO,WAAW;oBAChE,IAAI,EAAE,gBAAgB;iBACvB;gBACD,UAAU,EAAE,OAAO;aACpB,CAAC,CAAC;QACL,CAAC;IACH,CAAC,CAAC;AACJ,CAAC,CAAC;AAjCW,QAAA,aAAa,iBAiCxB"}