"""
PowerPoint to PDF conversion tool.
"""
import os
import tempfile
from typing import List, Dict, Any, Optional
import structlog

from .base_tool import <PERSON><PERSON><PERSON>ool, ProcessingError, ValidationError

logger = structlog.get_logger()


class PowerPointToPDFTool(BasePDFTool):
    """Tool for converting PowerPoint presentations to PDF format."""
    
    def __init__(self):
        super().__init__("powerpoint_to_pdf")
        
        # Supported input formats
        self.supported_formats = {
            ".pptx": "PowerPoint Presentation (Office 2007+)",
            ".ppt": "PowerPoint Presentation (Legacy)",
            ".odp": "OpenDocument Presentation"
        }
    
    def validate_input_files(self, input_files: List[str]) -> bool:
        """Validate that input files are PowerPoint presentations."""
        for file_path in input_files:
            if not os.path.exists(file_path):
                raise FileNotFoundError(f"Input file not found: {file_path}")
            
            if not os.access(file_path, os.R_OK):
                raise PermissionError(f"Cannot read input file: {file_path}")
            
            # Check if file is a PowerPoint presentation
            file_ext = os.path.splitext(file_path)[1].lower()
            if file_ext not in self.supported_formats:
                raise ValidationError(f"Unsupported PowerPoint format: {file_ext}")
        
        return True
    
    async def process(
        self,
        input_files: List[str],
        output_path: str,
        parameters: Optional[Dict[str, Any]] = None
    ) -> List[str]:
        """
        Convert PowerPoint presentations to PDF format.
        
        Args:
            input_files: List of PowerPoint file paths to convert
            output_path: Output directory for PDF files
            parameters: Conversion parameters (quality, layout, etc.)
            
        Returns:
            List containing paths to converted PDF files
        """
        # Validate inputs
        if len(input_files) == 0:
            raise ValidationError("At least 1 PowerPoint file is required for conversion")
        
        self.validate_input_files(input_files)
        
        # Prepare parameters
        params = parameters or {}
        quality = params.get("quality", "high")
        include_notes = params.get("include_notes", False)
        slides_per_page = params.get("slides_per_page", 1)
        
        # Validate parameters
        if quality not in ["low", "medium", "high"]:
            raise ValidationError("Quality must be 'low', 'medium', or 'high'")
        
        if slides_per_page not in [1, 2, 4, 6, 9]:
            raise ValidationError("Slides per page must be 1, 2, 4, 6, or 9")
        
        # Ensure output directory exists
        self.ensure_output_directory(output_path)
        
        output_files = []
        
        try:
            self.logger.info(
                "Starting PowerPoint to PDF conversion",
                input_count=len(input_files),
                quality=quality,
                include_notes=include_notes,
                slides_per_page=slides_per_page
            )
            
            for i, input_file in enumerate(input_files):
                try:
                    self.logger.debug(f"Converting file {i+1}/{len(input_files)}", file_path=input_file)
                    
                    # Generate output filename
                    base_name = os.path.splitext(os.path.basename(input_file))[0]
                    output_filename = f"{base_name}.pdf"
                    output_file = os.path.join(output_path, output_filename)
                    
                    # Convert the PowerPoint file
                    await self._convert_powerpoint_to_pdf(
                        input_file,
                        output_file,
                        quality,
                        include_notes,
                        slides_per_page
                    )
                    
                    # Verify output file was created
                    if not os.path.exists(output_file):
                        raise ProcessingError("Failed to create PDF file")
                    
                    output_size = self.get_file_size_mb(output_file)
                    input_size = self.get_file_size_mb(input_file)
                    
                    self.logger.info(
                        f"File {i+1} converted successfully",
                        input_file=input_file,
                        output_file=output_file,
                        input_size_mb=round(input_size, 2),
                        output_size_mb=round(output_size, 2)
                    )
                    
                    output_files.append(output_file)
                    
                except Exception as e:
                    self.logger.error(f"Failed to convert file {i+1}", file_path=input_file, error=str(e))
                    # Clean up any partial output files
                    self.cleanup_files(output_files)
                    raise ProcessingError(f"Failed to convert {os.path.basename(input_file)}: {str(e)}")
            
            total_input_size = sum(self.get_file_size_mb(f) for f in input_files)
            total_output_size = sum(self.get_file_size_mb(f) for f in output_files)
            
            self.logger.info(
                "PowerPoint to PDF conversion completed successfully",
                input_files=len(input_files),
                output_files=len(output_files),
                total_input_size_mb=round(total_input_size, 2),
                total_output_size_mb=round(total_output_size, 2)
            )
            
            return output_files
            
        except Exception as e:
            # Clean up any output files on error
            self.cleanup_files(output_files)
            
            if isinstance(e, (ValidationError, ProcessingError)):
                raise e
            else:
                raise ProcessingError(f"Unexpected error during PowerPoint to PDF conversion: {str(e)}")
    
    async def _convert_powerpoint_to_pdf(
        self,
        input_file: str,
        output_file: str,
        quality: str,
        include_notes: bool,
        slides_per_page: int
    ):
        """Convert a single PowerPoint file to PDF."""
        try:
            # Try different conversion methods based on availability
            
            # Method 1: Try LibreOffice headless conversion (most reliable)
            try:
                await self._convert_with_libreoffice(input_file, output_file)
                return
            except Exception as e:
                self.logger.warning(f"LibreOffice conversion failed: {str(e)}")
            
            # Method 2: Try python-pptx + reportlab (for .pptx files)
            if input_file.lower().endswith('.pptx'):
                try:
                    await self._convert_pptx_with_python_pptx(
                        input_file, output_file, quality, include_notes, slides_per_page
                    )
                    return
                except Exception as e:
                    self.logger.warning(f"python-pptx conversion failed: {str(e)}")
            
            # If all methods fail
            raise ProcessingError("No suitable conversion method available. Please install LibreOffice.")
            
        except Exception as e:
            raise ProcessingError(f"Failed to convert PowerPoint file: {str(e)}")
    
    async def _convert_with_libreoffice(self, input_file: str, output_file: str):
        """Convert using LibreOffice headless mode."""
        import subprocess
        import shutil
        
        # Check if LibreOffice is available
        libreoffice_cmd = None
        for cmd in ['libreoffice', 'soffice']:
            if shutil.which(cmd):
                libreoffice_cmd = cmd
                break
        
        if not libreoffice_cmd:
            raise ProcessingError("LibreOffice not found in system PATH")
        
        # Create temporary directory for output
        with tempfile.TemporaryDirectory() as temp_dir:
            # Run LibreOffice conversion
            cmd = [
                libreoffice_cmd,
                '--headless',
                '--convert-to', 'pdf',
                '--outdir', temp_dir,
                input_file
            ]
            
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=60)
            
            if result.returncode != 0:
                raise ProcessingError(f"LibreOffice conversion failed: {result.stderr}")
            
            # Find the generated PDF file
            base_name = os.path.splitext(os.path.basename(input_file))[0]
            temp_pdf = os.path.join(temp_dir, f"{base_name}.pdf")
            
            if not os.path.exists(temp_pdf):
                raise ProcessingError("LibreOffice did not generate expected PDF file")
            
            # Move to final location
            shutil.move(temp_pdf, output_file)
    
    async def _convert_pptx_with_python_pptx(
        self,
        input_file: str,
        output_file: str,
        quality: str,
        include_notes: bool,
        slides_per_page: int
    ):
        """Convert PPTX using python-pptx + reportlab (basic conversion)."""
        try:
            from pptx import Presentation
            from reportlab.pdfgen import canvas
            from reportlab.lib.pagesizes import letter, landscape
            from reportlab.lib.units import inch
            
            # Load presentation
            prs = Presentation(input_file)
            
            # Determine page size based on slides per page
            if slides_per_page == 1:
                pagesize = landscape(letter)  # Landscape for single slide
            else:
                pagesize = letter  # Portrait for multiple slides
            
            # Create PDF
            c = canvas.Canvas(output_file, pagesize=pagesize)
            page_width, page_height = pagesize
            
            slides_processed = 0
            current_page_slides = 0
            
            # Calculate slide dimensions for multiple slides per page
            if slides_per_page == 1:
                slide_width = page_width - 2 * inch
                slide_height = page_height - 2 * inch
                slides_per_row = 1
                slides_per_col = 1
            elif slides_per_page == 2:
                slide_width = (page_width - 3 * inch) / 2
                slide_height = page_height - 2 * inch
                slides_per_row = 2
                slides_per_col = 1
            elif slides_per_page == 4:
                slide_width = (page_width - 3 * inch) / 2
                slide_height = (page_height - 3 * inch) / 2
                slides_per_row = 2
                slides_per_col = 2
            elif slides_per_page == 6:
                slide_width = (page_width - 4 * inch) / 3
                slide_height = (page_height - 3 * inch) / 2
                slides_per_row = 3
                slides_per_col = 2
            else:  # 9 slides per page
                slide_width = (page_width - 4 * inch) / 3
                slide_height = (page_height - 4 * inch) / 3
                slides_per_row = 3
                slides_per_col = 3
            
            for slide_idx, slide in enumerate(prs.slides):
                # Calculate position on page
                row = (current_page_slides // slides_per_row) % slides_per_col
                col = current_page_slides % slides_per_row
                
                x = inch + col * (slide_width + inch / 2)
                y = page_height - inch - (row + 1) * (slide_height + inch / 2)
                
                # Draw slide border
                c.rect(x, y, slide_width, slide_height)
                
                # Add slide number
                c.setFont("Helvetica", 8)
                c.drawString(x + 5, y + slide_height - 15, f"Slide {slide_idx + 1}")
                
                # Extract and add text content (simplified)
                text_y = y + slide_height - 30
                c.setFont("Helvetica", 6)
                
                for shape in slide.shapes:
                    if hasattr(shape, "text") and shape.text.strip():
                        # Truncate long text
                        text = shape.text.strip()
                        if len(text) > 50:
                            text = text[:47] + "..."
                        
                        if text_y > y + 10:
                            c.drawString(x + 5, text_y, text)
                            text_y -= 10
                
                # Add notes if requested
                if include_notes and slide.notes_slide.notes_text_frame.text.strip():
                    notes_text = slide.notes_slide.notes_text_frame.text.strip()
                    if len(notes_text) > 30:
                        notes_text = notes_text[:27] + "..."
                    
                    c.setFont("Helvetica", 5)
                    c.drawString(x + 5, y + 5, f"Notes: {notes_text}")
                
                slides_processed += 1
                current_page_slides += 1
                
                # Start new page if needed
                if current_page_slides >= slides_per_page:
                    c.showPage()
                    current_page_slides = 0
            
            # Finish the last page if it has content
            if current_page_slides > 0:
                c.showPage()
            
            c.save()
            
        except ImportError:
            raise ProcessingError("python-pptx and reportlab are required for PPTX conversion")
        except Exception as e:
            raise ProcessingError(f"Failed to convert PPTX with python-pptx: {str(e)}")
    
    def get_conversion_options(self) -> Dict[str, Dict[str, Any]]:
        """Get available conversion options."""
        return {
            "quality": {
                "description": "PDF output quality",
                "type": "string",
                "options": ["low", "medium", "high"],
                "default": "high"
            },
            "include_notes": {
                "description": "Include speaker notes in PDF",
                "type": "boolean",
                "default": False
            },
            "slides_per_page": {
                "description": "Number of slides per PDF page",
                "type": "integer",
                "options": [1, 2, 4, 6, 9],
                "default": 1
            }
        }
    
    def get_supported_formats(self) -> Dict[str, str]:
        """Get supported input formats."""
        return self.supported_formats.copy()
    
    async def check_conversion_support(self) -> Dict[str, Any]:
        """Check what conversion methods are available."""
        support_info = {
            "libreoffice": False,
            "python_pptx": False,
            "recommended_method": None,
            "installation_notes": []
        }
        
        # Check LibreOffice
        import shutil
        if shutil.which('libreoffice') or shutil.which('soffice'):
            support_info["libreoffice"] = True
        else:
            support_info["installation_notes"].append("Install LibreOffice for best compatibility")
        
        # Check python-pptx
        try:
            import pptx
            import reportlab
            support_info["python_pptx"] = True
        except ImportError:
            support_info["installation_notes"].append("Install python-pptx and reportlab: pip install python-pptx reportlab")
        
        # Determine recommended method
        if support_info["libreoffice"]:
            support_info["recommended_method"] = "libreoffice"
        elif support_info["python_pptx"]:
            support_info["recommended_method"] = "python_pptx"
        else:
            support_info["recommended_method"] = "none"
        
        return support_info


# Create tool instance
powerpoint_to_pdf_tool = PowerPointToPDFTool()
