"""
PDF redaction tool for removing sensitive information from PDFs.
"""
import os
import re
from typing import List, Dict, Any, Optional, Pattern
import fitz  # PyMuPDF
from PyPDF2 import PdfWriter, PdfReader
import structlog

from .base_tool import BasePDFTool, ProcessingError, ValidationError

logger = structlog.get_logger()


class PDFRedactTool(BasePDFTool):
    """Tool for redacting sensitive information from PDF files."""
    
    def __init__(self):
        super().__init__("redact")
        
        # Predefined redaction patterns
        self.redaction_patterns = {
            "ssn": {
                "pattern": r"\b\d{3}-\d{2}-\d{4}\b|\b\d{9}\b",
                "description": "Social Security Numbers (XXX-XX-XXXX or XXXXXXXXX)"
            },
            "phone": {
                "pattern": r"\b\d{3}-\d{3}-\d{4}\b|\b\(\d{3}\)\s*\d{3}-\d{4}\b|\b\d{10}\b",
                "description": "Phone numbers (various formats)"
            },
            "email": {
                "pattern": r"\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b",
                "description": "Email addresses"
            },
            "credit_card": {
                "pattern": r"\b\d{4}[\s-]?\d{4}[\s-]?\d{4}[\s-]?\d{4}\b",
                "description": "Credit card numbers (16 digits)"
            },
            "date": {
                "pattern": r"\b\d{1,2}[/-]\d{1,2}[/-]\d{2,4}\b|\b\d{4}[/-]\d{1,2}[/-]\d{1,2}\b",
                "description": "Dates (MM/DD/YYYY, DD/MM/YYYY, YYYY/MM/DD)"
            },
            "address": {
                "pattern": r"\b\d+\s+[A-Za-z\s]+(?:Street|St|Avenue|Ave|Road|Rd|Boulevard|Blvd|Lane|Ln|Drive|Dr|Court|Ct|Place|Pl)\b",
                "description": "Street addresses"
            },
            "ip_address": {
                "pattern": r"\b(?:\d{1,3}\.){3}\d{1,3}\b",
                "description": "IP addresses"
            }
        }
        
        # Redaction methods
        self.redaction_methods = {
            "black_box": "Cover with black rectangle",
            "white_box": "Cover with white rectangle",
            "blur": "Blur the text (if supported)",
            "remove": "Remove text completely"
        }
    
    async def process(
        self,
        input_files: List[str],
        output_path: str,
        parameters: Optional[Dict[str, Any]] = None
    ) -> List[str]:
        """
        Redact sensitive information from PDF files.
        
        Args:
            input_files: List of PDF file paths to redact
            output_path: Output directory for redacted PDFs
            parameters: Redaction parameters (patterns, method, etc.)
            
        Returns:
            List containing paths to redacted PDF files
        """
        # Validate inputs
        if len(input_files) == 0:
            raise ValidationError("At least 1 PDF file is required for redaction")
        
        self.validate_input_files(input_files)
        self.validate_pdf_files(input_files)
        
        # Prepare parameters
        params = parameters or {}
        redaction_patterns = params.get("patterns", ["ssn", "phone", "email"])
        custom_patterns = params.get("custom_patterns", [])
        redaction_method = params.get("method", "black_box")
        pages = params.get("pages", "all")
        case_sensitive = params.get("case_sensitive", False)
        
        # Validate parameters
        for pattern in redaction_patterns:
            if pattern not in self.redaction_patterns:
                raise ValidationError(f"Invalid redaction pattern: {pattern}. Available: {list(self.redaction_patterns.keys())}")
        
        if redaction_method not in self.redaction_methods:
            raise ValidationError(f"Invalid redaction method: {redaction_method}. Available: {list(self.redaction_methods.keys())}")
        
        # Ensure output directory exists
        self.ensure_output_directory(output_path)
        
        output_files = []
        redaction_reports = []
        
        try:
            self.logger.info(
                "Starting PDF redaction",
                input_count=len(input_files),
                patterns=redaction_patterns,
                custom_patterns=len(custom_patterns),
                method=redaction_method,
                pages=pages
            )
            
            for i, input_file in enumerate(input_files):
                try:
                    self.logger.debug(f"Redacting file {i+1}/{len(input_files)}", file_path=input_file)
                    
                    # Generate output filename
                    base_name = os.path.splitext(os.path.basename(input_file))[0]
                    output_filename = f"{base_name}_redacted.pdf"
                    output_file = os.path.join(output_path, output_filename)
                    
                    # Redact the PDF
                    redaction_result = await self._redact_pdf(
                        input_file,
                        output_file,
                        redaction_patterns,
                        custom_patterns,
                        redaction_method,
                        pages,
                        case_sensitive
                    )
                    
                    # Verify output file was created
                    if not os.path.exists(output_file):
                        raise ProcessingError("Failed to create redacted PDF file")
                    
                    output_size = self.get_file_size_mb(output_file)
                    input_size = self.get_file_size_mb(input_file)
                    
                    redaction_reports.append({
                        "input_file": os.path.basename(input_file),
                        "output_file": os.path.basename(output_file),
                        "redactions_made": redaction_result["total_redactions"],
                        "pages_processed": redaction_result["pages_processed"],
                        "patterns_found": redaction_result["patterns_found"],
                        "input_size_mb": round(input_size, 2),
                        "output_size_mb": round(output_size, 2)
                    })
                    
                    self.logger.info(
                        f"File {i+1} redacted successfully",
                        input_file=input_file,
                        output_file=output_file,
                        redactions_made=redaction_result["total_redactions"],
                        pages_processed=redaction_result["pages_processed"]
                    )
                    
                    output_files.append(output_file)
                    
                except Exception as e:
                    self.logger.error(f"Failed to redact file {i+1}", file_path=input_file, error=str(e))
                    redaction_reports.append({
                        "input_file": os.path.basename(input_file),
                        "error": str(e),
                        "redaction_success": False
                    })
            
            # Save redaction report
            if redaction_reports:
                report_file = os.path.join(output_path, "redaction_report.json")
                await self._save_redaction_report(report_file, redaction_reports)
                output_files.append(report_file)
            
            total_input_size = sum(self.get_file_size_mb(f) for f in input_files)
            total_output_size = sum(self.get_file_size_mb(f) for f in output_files if f.endswith('.pdf'))
            
            self.logger.info(
                "PDF redaction completed",
                input_files=len(input_files),
                successful_redactions=len([r for r in redaction_reports if r.get("redaction_success", True)]),
                total_redactions=sum(r.get("redactions_made", 0) for r in redaction_reports),
                total_input_size_mb=round(total_input_size, 2),
                total_output_size_mb=round(total_output_size, 2)
            )
            
            return output_files
            
        except Exception as e:
            # Clean up any output files on error
            self.cleanup_files(output_files)
            
            if isinstance(e, (ValidationError, ProcessingError)):
                raise e
            else:
                raise ProcessingError(f"Unexpected error during PDF redaction: {str(e)}")
    
    async def _redact_pdf(
        self,
        input_file: str,
        output_file: str,
        redaction_patterns: List[str],
        custom_patterns: List[str],
        redaction_method: str,
        pages: str,
        case_sensitive: bool
    ) -> Dict[str, Any]:
        """Redact a PDF file using PyMuPDF."""
        try:
            # Open PDF with PyMuPDF
            doc = fitz.open(input_file)
            
            if doc.needs_pass:
                doc.close()
                raise ProcessingError("Cannot redact password-protected PDF")
            
            total_pages = len(doc)
            
            # Determine which pages to process
            pages_to_process = self._parse_pages_parameter(pages, total_pages)
            
            # Compile all patterns
            compiled_patterns = []
            
            # Add predefined patterns
            for pattern_name in redaction_patterns:
                if pattern_name in self.redaction_patterns:
                    pattern_info = self.redaction_patterns[pattern_name]
                    flags = 0 if case_sensitive else re.IGNORECASE
                    compiled_patterns.append({
                        "name": pattern_name,
                        "pattern": re.compile(pattern_info["pattern"], flags),
                        "description": pattern_info["description"]
                    })
            
            # Add custom patterns
            for i, custom_pattern in enumerate(custom_patterns):
                try:
                    flags = 0 if case_sensitive else re.IGNORECASE
                    compiled_patterns.append({
                        "name": f"custom_{i+1}",
                        "pattern": re.compile(custom_pattern, flags),
                        "description": f"Custom pattern: {custom_pattern}"
                    })
                except re.error as e:
                    self.logger.warning(f"Invalid custom pattern '{custom_pattern}': {str(e)}")
            
            total_redactions = 0
            pages_processed = 0
            patterns_found = {}
            
            # Process each page
            for page_num in range(total_pages):
                if (page_num + 1) not in pages_to_process:
                    continue
                
                page = doc[page_num]
                page_redactions = 0
                
                # Get text content with positions
                text_dict = page.get_text("dict")
                
                if text_dict and "blocks" in text_dict:
                    for block in text_dict["blocks"]:
                        if "lines" not in block:
                            continue
                        
                        for line in block["lines"]:
                            if "spans" not in line:
                                continue
                            
                            for span in line["spans"]:
                                if "text" not in span:
                                    continue
                                
                                text = span["text"]
                                bbox = span["bbox"]  # [x0, y0, x1, y1]
                                
                                # Check each pattern
                                for pattern_info in compiled_patterns:
                                    matches = pattern_info["pattern"].finditer(text)
                                    
                                    for match in matches:
                                        # Calculate position of match within the span
                                        start_pos = match.start()
                                        end_pos = match.end()
                                        
                                        # Estimate character positions (simplified)
                                        char_width = (bbox[2] - bbox[0]) / len(text) if text else 0
                                        match_x0 = bbox[0] + start_pos * char_width
                                        match_x1 = bbox[0] + end_pos * char_width
                                        match_bbox = fitz.Rect(match_x0, bbox[1], match_x1, bbox[3])
                                        
                                        # Apply redaction
                                        if redaction_method == "black_box":
                                            page.add_redact_annot(match_bbox, fill=(0, 0, 0))
                                        elif redaction_method == "white_box":
                                            page.add_redact_annot(match_bbox, fill=(1, 1, 1))
                                        else:  # remove or other methods
                                            page.add_redact_annot(match_bbox)
                                        
                                        page_redactions += 1
                                        
                                        # Track pattern usage
                                        pattern_name = pattern_info["name"]
                                        patterns_found[pattern_name] = patterns_found.get(pattern_name, 0) + 1
                                        
                                        self.logger.debug(
                                            f"Redacted '{match.group()}' on page {page_num + 1}",
                                            pattern=pattern_name
                                        )
                
                # Apply all redactions on this page
                if page_redactions > 0:
                    page.apply_redactions()
                    total_redactions += page_redactions
                    pages_processed += 1
            
            # Save the redacted PDF
            doc.save(output_file, garbage=4, deflate=True, clean=True)
            doc.close()
            
            return {
                "total_redactions": total_redactions,
                "pages_processed": pages_processed,
                "total_pages": total_pages,
                "patterns_found": patterns_found,
                "redaction_method": redaction_method
            }
            
        except Exception as e:
            raise ProcessingError(f"Failed to redact PDF: {str(e)}")
    
    def _parse_pages_parameter(self, pages: str, total_pages: int) -> set:
        """Parse the pages parameter to get set of page numbers to process."""
        if pages == "all":
            return set(range(1, total_pages + 1))
        elif pages == "odd":
            return set(range(1, total_pages + 1, 2))
        elif pages == "even":
            return set(range(2, total_pages + 1, 2))
        else:
            # Parse specific pages (e.g., "1,3,5-7,10")
            page_numbers = set()
            
            for part in pages.split(','):
                part = part.strip()
                
                if '-' in part:
                    # Range like "5-7"
                    start_str, end_str = part.split('-', 1)
                    start = int(start_str.strip())
                    end = int(end_str.strip())
                    
                    if start < 1 or end > total_pages or start > end:
                        raise ValidationError(f"Invalid page range: {part}")
                    
                    page_numbers.update(range(start, end + 1))
                else:
                    # Single page like "1"
                    page = int(part)
                    
                    if page < 1 or page > total_pages:
                        raise ValidationError(f"Invalid page number: {page}")
                    
                    page_numbers.add(page)
            
            return page_numbers

    async def _save_redaction_report(self, report_file: str, redaction_reports: List[Dict[str, Any]]):
        """Save redaction report to JSON file."""
        try:
            import json

            report_data = {
                "redaction_session": {
                    "total_files": len(redaction_reports),
                    "successful_redactions": len([r for r in redaction_reports if r.get("redaction_success", True)]),
                    "total_redactions_made": sum(r.get("redactions_made", 0) for r in redaction_reports)
                },
                "file_reports": redaction_reports
            }

            with open(report_file, 'w', encoding='utf-8') as f:
                json.dump(report_data, f, indent=2, ensure_ascii=False)

        except Exception as e:
            self.logger.error("Failed to save redaction report", error=str(e))

    def get_redaction_options(self) -> Dict[str, Dict[str, Any]]:
        """Get available redaction options."""
        return {
            "patterns": {
                "description": "Predefined patterns to redact",
                "type": "array",
                "options": list(self.redaction_patterns.keys()),
                "default": ["ssn", "phone", "email"],
                "pattern_descriptions": {k: v["description"] for k, v in self.redaction_patterns.items()}
            },
            "custom_patterns": {
                "description": "Custom regex patterns to redact",
                "type": "array",
                "default": [],
                "example": ["\\b[A-Z]{2}\\d{6}\\b"]
            },
            "method": {
                "description": "Redaction method",
                "type": "string",
                "options": list(self.redaction_methods.keys()),
                "default": "black_box",
                "method_descriptions": self.redaction_methods
            },
            "pages": {
                "description": "Pages to process",
                "type": "string",
                "options": ["all", "odd", "even", "specific (e.g., 1,3,5-7)"],
                "default": "all"
            },
            "case_sensitive": {
                "description": "Whether pattern matching should be case sensitive",
                "type": "boolean",
                "default": False
            }
        }

    def get_redaction_patterns(self) -> Dict[str, Dict[str, str]]:
        """Get available redaction patterns."""
        return self.redaction_patterns.copy()

    def get_redaction_methods(self) -> Dict[str, str]:
        """Get available redaction methods."""
        return self.redaction_methods.copy()

    async def preview_redaction(
        self,
        text_sample: str,
        patterns: List[str],
        custom_patterns: List[str] = None,
        case_sensitive: bool = False
    ) -> Dict[str, Any]:
        """Preview what would be redacted in a text sample."""
        try:
            custom_patterns = custom_patterns or []

            # Compile patterns
            compiled_patterns = []

            # Add predefined patterns
            for pattern_name in patterns:
                if pattern_name in self.redaction_patterns:
                    pattern_info = self.redaction_patterns[pattern_name]
                    flags = 0 if case_sensitive else re.IGNORECASE
                    compiled_patterns.append({
                        "name": pattern_name,
                        "pattern": re.compile(pattern_info["pattern"], flags),
                        "description": pattern_info["description"]
                    })

            # Add custom patterns
            for i, custom_pattern in enumerate(custom_patterns):
                try:
                    flags = 0 if case_sensitive else re.IGNORECASE
                    compiled_patterns.append({
                        "name": f"custom_{i+1}",
                        "pattern": re.compile(custom_pattern, flags),
                        "description": f"Custom pattern: {custom_pattern}"
                    })
                except re.error as e:
                    return {
                        "error": f"Invalid custom pattern '{custom_pattern}': {str(e)}",
                        "is_valid": False
                    }

            # Find matches
            matches_found = []
            redacted_text = text_sample

            for pattern_info in compiled_patterns:
                matches = list(pattern_info["pattern"].finditer(text_sample))

                for match in matches:
                    matches_found.append({
                        "pattern": pattern_info["name"],
                        "text": match.group(),
                        "start": match.start(),
                        "end": match.end(),
                        "description": pattern_info["description"]
                    })

            # Sort matches by position (reverse order for replacement)
            matches_found.sort(key=lambda x: x["start"], reverse=True)

            # Create redacted version
            for match in matches_found:
                redaction_text = "[REDACTED]"
                redacted_text = (
                    redacted_text[:match["start"]] +
                    redaction_text +
                    redacted_text[match["end"]:]
                )

            # Group matches by pattern
            pattern_counts = {}
            for match in matches_found:
                pattern_name = match["pattern"]
                pattern_counts[pattern_name] = pattern_counts.get(pattern_name, 0) + 1

            return {
                "original_text": text_sample,
                "redacted_text": redacted_text,
                "matches_found": len(matches_found),
                "pattern_counts": pattern_counts,
                "match_details": sorted(matches_found, key=lambda x: x["start"]),
                "is_valid": True
            }

        except Exception as e:
            return {
                "error": str(e),
                "is_valid": False
            }

    def validate_custom_pattern(self, pattern: str) -> Dict[str, Any]:
        """Validate a custom regex pattern."""
        try:
            compiled_pattern = re.compile(pattern)

            # Test with some sample text
            test_text = "Test 123-45-6789 <EMAIL> (555) 123-4567"
            matches = list(compiled_pattern.finditer(test_text))

            return {
                "pattern": pattern,
                "is_valid": True,
                "test_matches": [match.group() for match in matches],
                "message": "Pattern is valid"
            }

        except re.error as e:
            return {
                "pattern": pattern,
                "is_valid": False,
                "error": str(e),
                "message": f"Invalid regex pattern: {str(e)}"
            }

    async def scan_for_sensitive_data(self, file_path: str, max_pages: int = 5) -> Dict[str, Any]:
        """Scan PDF for potential sensitive data without redacting."""
        try:
            doc = fitz.open(file_path)

            if doc.needs_pass:
                doc.close()
                return {
                    "filename": os.path.basename(file_path),
                    "error": "PDF is password protected"
                }

            total_pages = len(doc)
            pages_to_scan = min(total_pages, max_pages)

            # Compile all predefined patterns
            compiled_patterns = []
            for pattern_name, pattern_info in self.redaction_patterns.items():
                compiled_patterns.append({
                    "name": pattern_name,
                    "pattern": re.compile(pattern_info["pattern"], re.IGNORECASE),
                    "description": pattern_info["description"]
                })

            findings = {}
            total_matches = 0

            # Scan pages
            for page_num in range(pages_to_scan):
                page = doc[page_num]
                text = page.get_text()

                for pattern_info in compiled_patterns:
                    matches = list(pattern_info["pattern"].finditer(text))

                    if matches:
                        pattern_name = pattern_info["name"]
                        if pattern_name not in findings:
                            findings[pattern_name] = {
                                "description": pattern_info["description"],
                                "count": 0,
                                "pages": [],
                                "samples": []
                            }

                        findings[pattern_name]["count"] += len(matches)
                        findings[pattern_name]["pages"].append(page_num + 1)

                        # Add sample matches (first 3)
                        for match in matches[:3]:
                            if match.group() not in findings[pattern_name]["samples"]:
                                findings[pattern_name]["samples"].append(match.group())

                        total_matches += len(matches)

            doc.close()

            return {
                "filename": os.path.basename(file_path),
                "total_pages": total_pages,
                "pages_scanned": pages_to_scan,
                "total_matches": total_matches,
                "patterns_found": len(findings),
                "findings": findings,
                "scan_complete": pages_to_scan == total_pages
            }

        except Exception as e:
            return {
                "filename": os.path.basename(file_path),
                "error": str(e)
            }


# Create tool instance
redact_pdf_tool = PDFRedactTool()
