import React, { useState } from 'react';
import { FileImage, Download, ArrowRight } from 'lucide-react';
import ToolLayout from '../components/ToolLayout';
import FileUpload from '../components/FileUpload';

const JPGToPDF = () => {
  const [files, setFiles] = useState<File[]>([]);
  const [isProcessing, setIsProcessing] = useState(false);
  const [pageSize, setPageSize] = useState<'A4' | 'Letter' | 'Auto'>('A4');
  const [orientation, setOrientation] = useState<'portrait' | 'landscape'>('portrait');

  const handleFileSelect = (selectedFiles: File[]) => {
    setFiles(selectedFiles);
  };

  const handleConvert = () => {
    if (files.length === 0) return;
    
    setIsProcessing(true);
    // Simulate processing
    setTimeout(() => {
      setIsProcessing(false);
      alert('Images converties en PDF avec succès!');
    }, 2000);
  };

  return (
    <ToolLayout
      title="JPG en PDF"
      description="Convertissez vos images en PDF. Ajustez l'orientation et les marges"
      icon={<FileImage className="w-8 h-8" />}
      color="from-amber-500 to-amber-600"
    >
      <div className="space-y-6">
        <FileUpload
          onFileSelect={handleFileSelect}
          accept=".jpg,.jpeg,.png,.gif,.bmp,.tiff"
          multiple={true}
          maxFiles={20}
          title="Sélectionnez vos images"
          description="Glissez-déposez vos images ici ou cliquez pour sélectionner (max 20 fichiers)"
        />

        {files.length > 0 && (
          <div className="bg-slate-50 p-6 rounded-xl">
            <h3 className="text-lg font-semibold text-slate-700 mb-4">
              Options de conversion
            </h3>
            
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-slate-700 mb-2">
                  Taille de page
                </label>
                <div className="space-y-2">
                  <label className="flex items-center space-x-3">
                    <input
                      type="radio"
                      name="pageSize"
                      value="A4"
                      checked={pageSize === 'A4'}
                      onChange={(e) => setPageSize(e.target.value as 'A4')}
                      className="text-amber-600"
                    />
                    <span className="text-slate-700">A4 (210 × 297 mm)</span>
                  </label>
                  <label className="flex items-center space-x-3">
                    <input
                      type="radio"
                      name="pageSize"
                      value="Letter"
                      checked={pageSize === 'Letter'}
                      onChange={(e) => setPageSize(e.target.value as 'Letter')}
                      className="text-amber-600"
                    />
                    <span className="text-slate-700">Letter (8.5 × 11 in)</span>
                  </label>
                  <label className="flex items-center space-x-3">
                    <input
                      type="radio"
                      name="pageSize"
                      value="Auto"
                      checked={pageSize === 'Auto'}
                      onChange={(e) => setPageSize(e.target.value as 'Auto')}
                      className="text-amber-600"
                    />
                    <span className="text-slate-700">Automatique (s'adapte à l'image)</span>
                  </label>
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-slate-700 mb-2">
                  Orientation
                </label>
                <div className="space-y-2">
                  <label className="flex items-center space-x-3">
                    <input
                      type="radio"
                      name="orientation"
                      value="portrait"
                      checked={orientation === 'portrait'}
                      onChange={(e) => setOrientation(e.target.value as 'portrait')}
                      className="text-amber-600"
                    />
                    <span className="text-slate-700">Portrait</span>
                  </label>
                  <label className="flex items-center space-x-3">
                    <input
                      type="radio"
                      name="orientation"
                      value="landscape"
                      checked={orientation === 'landscape'}
                      onChange={(e) => setOrientation(e.target.value as 'landscape')}
                      className="text-amber-600"
                    />
                    <span className="text-slate-700">Paysage</span>
                  </label>
                </div>
              </div>

              <div className="flex items-center space-x-3">
                <input
                  type="checkbox"
                  id="fit-page"
                  defaultChecked
                  className="text-amber-600 rounded"
                />
                <label htmlFor="fit-page" className="text-slate-700">
                  Ajuster l'image à la page
                </label>
              </div>
            </div>
          </div>
        )}

        {files.length > 0 && (
          <div className="flex justify-center">
            <button
              onClick={handleConvert}
              disabled={isProcessing}
              className="bg-gradient-to-r from-amber-600 to-orange-600 text-white px-8 py-4 rounded-xl text-lg font-medium hover:shadow-xl transition-all duration-200 transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2"
            >
              {isProcessing ? (
                <>
                  <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white"></div>
                  <span>Conversion en cours...</span>
                </>
              ) : (
                <>
                  <span>Convertir en PDF</span>
                  <ArrowRight className="w-5 h-5" />
                </>
              )}
            </button>
          </div>
        )}
      </div>
    </ToolLayout>
  );
};

export default JPGToPDF;