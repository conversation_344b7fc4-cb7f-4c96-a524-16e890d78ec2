import React from 'react';
import { Link } from 'react-router-dom';
import { useLanguage } from '../contexts/LanguageContext';
import SEOHead from '../components/SEOHead';
import { 
  FileText, 
  Split, 
  Minimize2, 
  FileImage, 
  Edit, 
  PenTool,
  RotateCw,
  Globe,
  Lock,
  Unlock,
  FolderOpen,
  Wrench,
  Hash,
  Smartphone,
  Search,
  Eye,
  Scissors,
  Crop,
  ArrowRight,
  Crown
} from 'lucide-react';

const ToolsPage = () => {
  const { t, language } = useLanguage();

  const allTools = [
    {
      title: t('tool.merge.title'),
      description: t('tool.merge.description'),
      icon: <FileText className="w-6 h-6" />,
      link: "/fusionner-pdf",
      color: "from-blue-500 to-blue-600",
      category: "Manipulation"
    },
    {
      title: t('tool.split.title'),
      description: t('tool.split.description'),
      icon: <Split className="w-6 h-6" />,
      link: "/diviser-pdf",
      color: "from-purple-500 to-purple-600",
      category: "Manipulation"
    },
    {
      title: t('tool.compress.title'),
      description: t('tool.compress.description'),
      icon: <Minimize2 className="w-6 h-6" />,
      link: "/compresser-pdf",
      color: "from-green-500 to-green-600",
      category: "Optimisation"
    },
    {
      title: t('tool.pdfToWord.title'),
      description: t('tool.pdfToWord.description'),
      icon: <FileText className="w-6 h-6" />,
      link: "/pdf-vers-word",
      color: "from-indigo-500 to-indigo-600",
      category: "Conversion"
    },
    {
      title: "PDF en PowerPoint",
      description: "Transformez vos fichiers PDF en présentations PPT et PPTX faciles à éditer.",
      icon: <FileText className="w-6 h-6" />,
      link: "/pdf-vers-powerpoint",
      color: "from-orange-500 to-orange-600",
      category: "Conversion"
    },
    {
      title: "PDF en Excel",
      description: "Transférez les données de fichiers PDF vers des feuilles de calcul Excel en quelques secondes.",
      icon: <FileText className="w-6 h-6" />,
      link: "/pdf-vers-excel",
      color: "from-emerald-500 to-emerald-600",
      category: "Conversion"
    },
    {
      title: t('tool.wordToPdf.title'),
      description: t('tool.wordToPdf.description'),
      icon: <FileText className="w-6 h-6" />,
      link: "/word-vers-pdf",
      color: "from-sky-500 to-sky-600",
      category: "Conversion"
    },
    {
      title: "PowerPoint en PDF",
      description: "Facilitez la lecture de vos présentations PPT et PPTX en les convertissant en PDF.",
      icon: <FileText className="w-6 h-6" />,
      link: "/powerpoint-vers-pdf",
      color: "from-rose-500 to-rose-600",
      category: "Conversion"
    },
    {
      title: "Excel en PDF",
      description: "Facilitez la lecture de vos feuilles de calcul EXCEL en les convertissant en PDF.",
      icon: <FileText className="w-6 h-6" />,
      link: "/excel-vers-pdf",
      color: "from-teal-500 to-teal-600",
      category: "Conversion"
    },
    {
      title: "Modifier PDF",
      description: "Ajouter du texte, des images, des formes ou des annotations manuscrites à un document PDF.",
      icon: <Edit className="w-6 h-6" />,
      link: "/modifier-pdf",
      color: "from-violet-500 to-violet-600",
      category: "Édition"
    },
    {
      title: "PDF en JPG",
      description: "Extraire toutes les images contenues dans un fichier PDF ou convertir chaque page dans un fichier JPG.",
      icon: <FileImage className="w-6 h-6" />,
      link: "/pdf-vers-jpg",
      color: "from-pink-500 to-pink-600",
      category: "Conversion"
    },
    {
      title: "JPG en PDF",
      description: "Convertissez vos images en PDF. Ajustez l'orientation et les marges.",
      icon: <FileImage className="w-6 h-6" />,
      link: "/jpg-vers-pdf",
      color: "from-amber-500 to-amber-600",
      category: "Conversion"
    },
    {
      title: "Signer PDF",
      description: "Signez vous-même ou demandez des signatures électroniques à des tiers.",
      icon: <PenTool className="w-6 h-6" />,
      link: "/signer-pdf",
      color: "from-cyan-500 to-cyan-600",
      category: "Sécurité"
    },
    {
      title: "Filigrane",
      description: "Choisissez une image ou un texte à appliquer sur votre PDF. Sélectionnez l'emplacement, la transparence et la typographie.",
      icon: <FileText className="w-6 h-6" />,
      link: "/filigrane-pdf",
      color: "from-lime-500 to-lime-600",
      category: "Édition"
    },
    {
      title: "Faire pivoter PDF",
      description: "Faites pivoter votre PDF comme vous le souhaitez. Tournez plusieurs fichiers PDF à la fois!",
      icon: <RotateCw className="w-6 h-6" />,
      link: "/pivoter-pdf",
      color: "from-red-500 to-red-600",
      category: "Manipulation"
    },
    {
      title: "HTML en PDF",
      description: "Convertissez des pages web HTML en PDF. Copiez-collez l'URL de la page qui vous intéresse.",
      icon: <Globe className="w-6 h-6" />,
      link: "/html-vers-pdf",
      color: "from-slate-500 to-slate-600",
      category: "Conversion"
    },
    {
      title: "Déverrouiller PDF",
      description: "Retirez le mot de passe de sécurité du PDF, de sorte à ce que vous puissiez l'utiliser comme vous le souhaitez.",
      icon: <Unlock className="w-6 h-6" />,
      link: "/deverrouiller-pdf",
      color: "from-yellow-500 to-yellow-600",
      category: "Sécurité"
    },
    {
      title: "Protéger PDF",
      description: "Protégez les fichiers PDF avec un mot de passe. Chiffrez les documents PDF afin d'éviter des accès non autorisés.",
      icon: <Lock className="w-6 h-6" />,
      link: "/proteger-pdf",
      color: "from-gray-500 to-gray-600",
      category: "Sécurité",
      isPremium: true
    },
    {
      title: "Organiser PDF",
      description: "Triez les pages de votre fichier PDF comme bon vous semble. Supprimez ou ajoutez des pages PDF à votre document.",
      icon: <FolderOpen className="w-6 h-6" />,
      link: "/organiser-pdf",
      color: "from-stone-500 to-stone-600",
      category: "Manipulation"
    },
    {
      title: "PDF en PDF/A",
      description: "Transformez votre PDF en PDF/A, la version au standard ISO des PDF, pour un archivage à long-terme.",
      icon: <FileText className="w-6 h-6" />,
      link: "/pdf-vers-pdfa",
      color: "from-zinc-500 to-zinc-600",
      category: "Conversion"
    },
    {
      title: "Réparer PDF",
      description: "Réparez un PDF endommagé et restaurez les données d'un PDF corrompu.",
      icon: <Wrench className="w-6 h-6" />,
      link: "/reparer-pdf",
      color: "from-neutral-500 to-neutral-600",
      category: "Optimisation"
    },
    {
      title: "Numéros de pages",
      description: "Insérez des numéros de pages dans les documents PDF, en toute simplicité.",
      icon: <Hash className="w-6 h-6" />,
      link: "/numeros-pages-pdf",
      color: "from-fuchsia-500 to-fuchsia-600",
      category: "Édition"
    },
    {
      title: "Numériser au format PDF",
      description: "Numérisez des documents avec votre mobile et envoyez-les vers votre navigateur en un clin d'oeil.",
      icon: <Smartphone className="w-6 h-6" />,
      link: "/scanner-vers-pdf",
      color: "from-purple-500 to-purple-600",
      category: "Création"
    },
    {
      title: "OCR PDF",
      description: "Convertissez en toute simplicité vos PDF numérisés en documents indexables et modifiables.",
      icon: <Search className="w-6 h-6" />,
      link: "/ocr-pdf",
      color: "from-blue-500 to-blue-600",
      category: "Optimisation"
    },
    {
      title: "Comparer PDF",
      description: "Permet de comparer des documents côte à côte, en mettant facilement en évidence les changements.",
      icon: <Eye className="w-6 h-6" />,
      link: "/comparer-pdf",
      color: "from-green-500 to-green-600",
      category: "Analyse"
    },
    {
      title: "Censurer PDF",
      description: "Censurez le texte et les graphiques pour supprimer définitivement les informations sensibles d'un PDF.",
      icon: <Scissors className="w-6 h-6" />,
      link: "/censurer-pdf",
      color: "from-red-500 to-red-600",
      category: "Sécurité"
    },
    {
      title: "Rogner PDF",
      description: "Réduisez les marges de vos documents PDF ou sélectionnez une zone à rogner.",
      icon: <Crop className="w-6 h-6" />,
      link: "/rogner-pdf",
      color: "from-orange-500 to-orange-600",
      category: "Édition"
    }
  ];

  const categories = [
    { name: "Tous", value: "all" },
    { name: "Conversion", value: "Conversion" },
    { name: "Manipulation", value: "Manipulation" },
    { name: "Édition", value: "Édition" },
    { name: "Sécurité", value: "Sécurité" },
    { name: "Optimisation", value: "Optimisation" },
    { name: "Analyse", value: "Analyse" },
    { name: "Création", value: "Création" }
  ];

  const [selectedCategory, setSelectedCategory] = React.useState("all");

  const filteredTools = selectedCategory === "all" 
    ? allTools 
    : allTools.filter(tool => tool.category === selectedCategory);

  return (
    <div className="min-h-screen py-8 px-4 sm:px-6 lg:px-8 bg-gradient-to-br from-slate-50 to-blue-50">
      <SEOHead
        title={t('tools.seo.title')}
        description={t('tools.seo.description')}
        keywords={t('tools.seo.keywords')}
        canonicalUrl="/tools"
        structuredData={{
          "@context": "https://schema.org",
          "@type": "CollectionPage",
          "name": "PDF Tools Collection - PDFTools Pro",
          "description": t('tools.seo.description'),
          "numberOfItems": allTools.length
        }}
      />
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="text-center mb-12">
          <h1 className="text-4xl sm:text-5xl font-bold text-slate-800 mb-6">
            {t('nav.tools')} PDF
          </h1>
          <p className="text-xl text-slate-600 max-w-3xl mx-auto">
            {language === 'fr' ? 'Découvrez notre collection complète de plus de 25 outils PDF gratuits pour tous vos besoins' :
             language === 'es' ? 'Descubre nuestra colección completa de más de 25 herramientas PDF gratuitas para todas tus necesidades' :
             language === 'de' ? 'Entdecken Sie unsere vollständige Sammlung von über 25 kostenlosen PDF-Tools für alle Ihre Bedürfnisse' :
             language === 'pt' ? 'Descubra nossa coleção completa de mais de 25 ferramentas PDF gratuitas para todas as suas necessidades' :
             'Discover our complete collection of over 25 free PDF tools for all your needs'}
          </p>
        </div>

        {/* Category Filter */}
        <div className="flex flex-wrap justify-center gap-3 mb-12">
          {categories.map((category) => (
            <button
              key={category.value}
              onClick={() => setSelectedCategory(category.value)}
              className={`px-6 py-3 rounded-full text-sm font-medium transition-all duration-200 ${
                selectedCategory === category.value
                  ? 'bg-gradient-to-r from-blue-600 to-purple-600 text-white shadow-lg'
                  : 'bg-white text-slate-700 hover:bg-slate-50 border border-slate-200'
              }`}
            >
              {language === 'fr' ? category.name :
               language === 'es' ? (category.value === 'all' ? 'Todos' :
                                   category.value === 'Conversion' ? 'Conversión' :
                                   category.value === 'Manipulation' ? 'Manipulación' :
                                   category.value === 'Édition' ? 'Edición' :
                                   category.value === 'Sécurité' ? 'Seguridad' :
                                   category.value === 'Optimisation' ? 'Optimización' :
                                   category.value === 'Analyse' ? 'Análisis' :
                                   category.value === 'Création' ? 'Creación' : category.name) :
               language === 'de' ? (category.value === 'all' ? 'Alle' :
                                   category.value === 'Conversion' ? 'Konvertierung' :
                                   category.value === 'Manipulation' ? 'Bearbeitung' :
                                   category.value === 'Édition' ? 'Edition' :
                                   category.value === 'Sécurité' ? 'Sicherheit' :
                                   category.value === 'Optimisation' ? 'Optimierung' :
                                   category.value === 'Analyse' ? 'Analyse' :
                                   category.value === 'Création' ? 'Erstellung' : category.name) :
               language === 'pt' ? (category.value === 'all' ? 'Todos' :
                                   category.value === 'Conversion' ? 'Conversão' :
                                   category.value === 'Manipulation' ? 'Manipulação' :
                                   category.value === 'Édition' ? 'Edição' :
                                   category.value === 'Sécurité' ? 'Segurança' :
                                   category.value === 'Optimisation' ? 'Otimização' :
                                   category.value === 'Analyse' ? 'Análise' :
                                   category.value === 'Création' ? 'Criação' : category.name) :
               (category.value === 'all' ? 'All' :
                category.value === 'Conversion' ? 'Conversion' :
                category.value === 'Manipulation' ? 'Manipulation' :
                category.value === 'Édition' ? 'Editing' :
                category.value === 'Sécurité' ? 'Security' :
                category.value === 'Optimisation' ? 'Optimization' :
                category.value === 'Analyse' ? 'Analysis' :
                category.value === 'Création' ? 'Creation' : category.name)}
            </button>
          ))}
        </div>

        {/* Tools Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
          {filteredTools.map((tool, index) => (
            <Link
              key={index}
              to={tool.link}
              className="group bg-white rounded-2xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2 border border-slate-100 hover:border-blue-200"
            >
              <div className="flex items-center justify-between mb-4">
                <div className={`w-12 h-12 rounded-xl bg-gradient-to-r ${tool.color} flex items-center justify-center text-white group-hover:scale-110 transition-transform`}>
                  {tool.icon}
                </div>
                <div className="flex items-center space-x-2">
                  {tool.isPremium && (
                    <div className="bg-gradient-to-r from-yellow-400 to-orange-500 text-white px-2 py-1 rounded-full text-xs font-medium flex items-center space-x-1">
                      <Crown className="w-3 h-3" />
                      <span>Premium</span>
                    </div>
                  )}
                  <span className="text-xs font-medium text-slate-500 bg-slate-100 px-2 py-1 rounded-full">
                    {tool.category}
                  </span>
                </div>
              </div>
              
              <h3 className="text-lg font-semibold text-slate-800 mb-2 group-hover:text-blue-600 transition-colors">
                {tool.title}
              </h3>
              
              <p className="text-slate-600 text-sm leading-relaxed line-clamp-3 mb-4">
                {tool.description}
              </p>
              
              <div className="flex items-center text-blue-600 text-sm font-medium opacity-0 group-hover:opacity-100 transition-opacity">
                {language === 'fr' ? 'Utiliser cet outil' :
                 language === 'es' ? 'Usar esta herramienta' :
                 language === 'de' ? 'Tool verwenden' :
                 language === 'pt' ? 'Usar esta ferramenta' :
                 'Use this tool'}
                <ArrowRight className="w-4 h-4 ml-1 group-hover:translate-x-1 transition-transform" />
              </div>
            </Link>
          ))}
        </div>

        {/* Stats */}
        <div className="mt-16 text-center">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-8 max-w-4xl mx-auto">
            <div>
              <div className="text-3xl font-bold text-slate-800 mb-2">{allTools.length}+</div>
              <div className="text-slate-600">
                {language === 'fr' ? 'Outils disponibles' :
                 language === 'es' ? 'Herramientas disponibles' :
                 language === 'de' ? 'Verfügbare Tools' :
                 language === 'pt' ? 'Ferramentas disponíveis' :
                 'Available tools'}
              </div>
            </div>
            <div>
              <div className="text-3xl font-bold text-slate-800 mb-2">100%</div>
              <div className="text-slate-600">
                {language === 'fr' ? 'Gratuit' :
                 language === 'es' ? 'Gratis' :
                 language === 'de' ? 'Kostenlos' :
                 language === 'pt' ? 'Grátis' :
                 'Free'}
              </div>
            </div>
            <div>
              <div className="text-3xl font-bold text-slate-800 mb-2">0</div>
              <div className="text-slate-600">
                {language === 'fr' ? 'Inscription requise' :
                 language === 'es' ? 'Registro requerido' :
                 language === 'de' ? 'Registrierung erforderlich' :
                 language === 'pt' ? 'Cadastro necessário' :
                 'Registration required'}
              </div>
            </div>
            <div>
              <div className="text-3xl font-bold text-slate-800 mb-2">∞</div>
              <div className="text-slate-600">
                {language === 'fr' ? 'Utilisations' :
                 language === 'es' ? 'Usos' :
                 language === 'de' ? 'Nutzungen' :
                 language === 'pt' ? 'Usos' :
                 'Uses'}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ToolsPage;