import React from 'react';
import { Link } from 'react-router-dom';
import { FileText, Menu, X, User, Settings, LogOut, Crown } from 'lucide-react';
import { useLanguage } from '../contexts/LanguageContext';
import { useAuth } from '../contexts/AuthContext';
import { useUsage } from '../contexts/UsageContext';
import LanguageSelector from './LanguageSelector';
import SettingsModal from './SettingsModal';

const Navbar = () => {
  const [isMenuOpen, setIsMenuOpen] = React.useState(false);
  const [isUserMenuOpen, setIsUserMenuOpen] = React.useState(false);
  const [isSettingsOpen, setIsSettingsOpen] = React.useState(false);
  const [settingsTab, setSettingsTab] = React.useState<'profile' | 'usage' | 'billing'>('profile');
  const { t } = useLanguage();
  const { user, isAuthenticated, logout } = useAuth();
  const { getRemainingUsage } = useUsage();

  return (
    <nav className="bg-white/80 border-b border-slate-200 sticky top-0 z-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center h-16">
          <Link to="/" className="flex items-center space-x-2 group">
            <div className="p-2 bg-gradient-to-r from-blue-600 to-purple-600 rounded-lg group-hover:scale-105 transition-transform">
              <FileText className="w-6 h-6 text-white" />
            </div>
            <span className="text-xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
              {t('nav.brand')}
            </span>
          </Link>

          <div className="hidden md:flex items-center space-x-8">
            <Link to="/" className="text-slate-700 hover:text-blue-600 transition-colors font-medium">
              {t('nav.home')}
            </Link>
            <Link to="/tools" className="text-slate-700 hover:text-blue-600 transition-colors font-medium">
              {t('nav.tools')}
            </Link>
            <LanguageSelector />
            
            {isAuthenticated ? (
              <div className="relative">
                <button
                  onClick={() => setIsUserMenuOpen(!isUserMenuOpen)}
                  className="flex items-center space-x-2 bg-slate-100 hover:bg-slate-200 px-4 py-2 rounded-lg transition-colors"
                >
                  <User className="w-4 h-4 text-slate-600" />
                  <span className="text-slate-700 font-medium">{user?.firstName} {user?.lastName}</span>
                  {user?.subscriptionPlan === 'premium' && <Crown className="w-4 h-4 text-yellow-500" />}
                </button>
                
                {isUserMenuOpen && (
                  <div className="absolute right-0 top-full mt-2 w-64 bg-white rounded-lg shadow-lg border border-slate-200 py-2 z-50">
                    <div className="px-4 py-2 border-b border-slate-200">
                      <p className="text-sm font-medium text-slate-800">{user?.firstName} {user?.lastName}</p>
                      <p className="text-xs text-slate-500">{user?.email}</p>
                      <div className="flex items-center justify-between mt-1">
                        <span className="text-xs text-slate-500">
                          Plan {user?.subscriptionPlan === 'premium' ? 'Premium' : 'Gratuit'}
                        </span>
                      </div>
                    </div>
                    
                    <button
                      onClick={() => {
                        setSettingsTab('profile');
                        setIsSettingsOpen(true);
                        setIsUserMenuOpen(false);
                      }}
                      className="w-full flex items-center space-x-2 px-4 py-2 text-left hover:bg-slate-50 transition-colors"
                    >
                      <Settings className="w-4 h-4 text-slate-500" />
                      <span className="text-slate-700">Paramètres</span>
                    </button>
                    
                    <button
                      onClick={() => {
                        setSettingsTab('usage');
                        setIsSettingsOpen(true);
                        setIsUserMenuOpen(false);
                      }}
                      className="w-full flex items-center space-x-2 px-4 py-2 text-left hover:bg-slate-50 transition-colors"
                    >
                      <User className="w-4 h-4 text-slate-500" />
                      <span className="text-slate-700">Utilisation</span>
                    </button>
                    
                    {user?.subscriptionPlan === 'free' && (
                      <button
                        onClick={() => {
                          setSettingsTab('billing');
                          setIsSettingsOpen(true);
                          setIsUserMenuOpen(false);
                        }}
                        className="w-full flex items-center space-x-2 px-4 py-2 text-left hover:bg-slate-50 transition-colors"
                      >
                        <Crown className="w-4 h-4 text-yellow-500" />
                        <span className="text-slate-700">Passer Premium</span>
                      </button>
                    )}
                    
                    <div className="border-t border-slate-200 mt-2 pt-2">
                      <button
                        onClick={() => {
                          logout();
                          setIsUserMenuOpen(false);
                        }}
                        className="w-full flex items-center space-x-2 px-4 py-2 text-left hover:bg-slate-50 transition-colors text-red-600"
                      >
                        <LogOut className="w-4 h-4" />
                        <span>Se déconnecter</span>
                      </button>
                    </div>
                  </div>
                )}
              </div>
            ) : (
              <div className="flex items-center space-x-4">
                <Link 
                  to="/signin"
                  className="text-slate-700 hover:text-blue-600 transition-colors font-medium"
                >
                  {t('auth.signIn')}
                </Link>
                <Link 
                  to="/signup"
                  className="bg-gradient-to-r from-blue-600 to-purple-600 text-white px-6 py-2 rounded-lg hover:shadow-lg transition-all duration-200 font-medium"
                >
                  {t('nav.getStarted')}
                </Link>
              </div>
            )}
          </div>

          <button
            onClick={() => setIsMenuOpen(!isMenuOpen)}
            className="md:hidden p-2 rounded-lg hover:bg-slate-100 transition-colors"
          >
            {isMenuOpen ? <X className="w-5 h-5" /> : <Menu className="w-5 h-5" />}
          </button>
        </div>

        {isMenuOpen && (
          <div className="md:hidden py-4 border-t border-slate-200 bg-white/95">
            <div className="flex flex-col space-y-3">
              <Link 
                to="/" 
                className="text-slate-700 hover:text-blue-600 transition-colors font-medium px-2"
                onClick={() => setIsMenuOpen(false)}
              >
                {t('nav.home')}
              </Link>
              <Link 
                to="/tools" 
                className="text-slate-700 hover:text-blue-600 transition-colors font-medium px-2"
                onClick={() => setIsMenuOpen(false)}
              >
                {t('nav.tools')}
              </Link>
              <div className="px-2">
                <LanguageSelector />
              </div>
              
              {isAuthenticated ? (
                <div className="mx-2 space-y-3">
                  <div className="bg-slate-100 p-3 rounded-lg">
                    <p className="text-sm font-medium text-slate-800">{user?.firstName} {user?.lastName}</p>
                    <p className="text-xs text-slate-500">{user?.email}</p>
                    <div className="flex items-center justify-between mt-1">
                      <span className="text-xs text-slate-500">
                        Plan {user?.subscriptionPlan === 'premium' ? 'Premium' : 'Gratuit'}
                      </span>
                      {!isAuthenticated && (
                        <span className="text-xs bg-blue-100 text-blue-700 px-2 py-1 rounded">
                          {getRemainingUsage()}/2
                        </span>
                      )}
                    </div>
                  </div>
                  
                  <button
                    onClick={() => {
                      setSettingsTab('profile');
                      setIsSettingsOpen(true);
                      setIsMenuOpen(false);
                    }}
                    className="w-full flex items-center space-x-2 px-3 py-2 text-left hover:bg-slate-100 rounded-lg transition-colors"
                  >
                    <Settings className="w-4 h-4 text-slate-500" />
                    <span className="text-slate-700">Paramètres</span>
                  </button>
                  
                  <button
                    onClick={() => {
                      logout();
                      setIsMenuOpen(false);
                    }}
                    className="w-full flex items-center space-x-2 px-3 py-2 text-left hover:bg-slate-100 rounded-lg transition-colors text-red-600"
                  >
                    <LogOut className="w-4 h-4" />
                    <span>Se déconnecter</span>
                  </button>
                </div>
              ) : (
                <div className="mx-2 space-y-3">
                  <Link 
                    to="/signin"
                    className="block text-center border border-slate-300 text-slate-700 px-6 py-2 rounded-lg hover:bg-slate-50 transition-all duration-200 font-medium"
                    onClick={() => setIsMenuOpen(false)}
                  >
                    {t('auth.signIn')}
                  </Link>
                  <Link 
                    to="/signup"
                    className="block text-center bg-gradient-to-r from-blue-600 to-purple-600 text-white px-6 py-2 rounded-lg hover:shadow-lg transition-all duration-200 font-medium"
                    onClick={() => setIsMenuOpen(false)}
                  >
                    {t('nav.getStarted')}
                  </Link>
                </div>
              )}
            </div>
          </div>
        )}
        
        <SettingsModal 
          isOpen={isSettingsOpen} 
          onClose={() => setIsSettingsOpen(false)}
          activeTab={settingsTab as 'profile' | 'usage' | 'billing'}
        />
      </div>
    </nav>
  );
};

export default Navbar;