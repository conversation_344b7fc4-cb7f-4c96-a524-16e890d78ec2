"""
PDF comparison tool for finding differences between two PDF files.
"""
import os
from typing import List, Dict, Any, Optional, Tuple
import fitz  # PyMuPDF
from PIL import Image, ImageChops, ImageDraw
import structlog

from .base_tool import BasePDFTool, ProcessingError, ValidationError

logger = structlog.get_logger()


class PDFCompareTool(BasePDFTool):
    """Tool for comparing two PDF files and highlighting differences."""
    
    def __init__(self):
        super().__init__("compare")
        
        # Comparison methods
        self.comparison_methods = {
            "text": "Compare text content only",
            "visual": "Compare visual appearance of pages",
            "both": "Compare both text and visual content"
        }
        
        # Difference highlighting colors
        self.highlight_colors = {
            "red": (255, 0, 0),
            "blue": (0, 0, 255),
            "green": (0, 255, 0),
            "yellow": (255, 255, 0),
            "magenta": (255, 0, 255)
        }
    
    async def process(
        self,
        input_files: List[str],
        output_path: str,
        parameters: Optional[Dict[str, Any]] = None
    ) -> List[str]:
        """
        Compare two PDF files and generate difference report.
        
        Args:
            input_files: List containing exactly 2 PDF file paths to compare
            output_path: Output directory for comparison results
            parameters: Comparison parameters (method, sensitivity, etc.)
            
        Returns:
            List containing paths to comparison output files
        """
        # Validate inputs
        if len(input_files) != 2:
            raise ValidationError("PDF comparison requires exactly 2 input files")
        
        self.validate_input_files(input_files)
        self.validate_pdf_files(input_files)
        
        # Prepare parameters
        params = parameters or {}
        comparison_method = params.get("method", "both")
        sensitivity = params.get("sensitivity", "medium")  # low, medium, high
        highlight_color = params.get("highlight_color", "red")
        generate_report = params.get("generate_report", True)
        
        # Validate parameters
        if comparison_method not in self.comparison_methods:
            raise ValidationError(f"Invalid comparison method: {comparison_method}. Available: {list(self.comparison_methods.keys())}")
        
        if sensitivity not in ["low", "medium", "high"]:
            raise ValidationError("Sensitivity must be 'low', 'medium', or 'high'")
        
        if highlight_color not in self.highlight_colors:
            raise ValidationError(f"Invalid highlight color: {highlight_color}. Available: {list(self.highlight_colors.keys())}")
        
        # Ensure output directory exists
        self.ensure_output_directory(output_path)
        
        try:
            file1, file2 = input_files
            
            self.logger.info(
                "Starting PDF comparison",
                file1=os.path.basename(file1),
                file2=os.path.basename(file2),
                method=comparison_method,
                sensitivity=sensitivity
            )
            
            # Perform comparison
            comparison_result = await self._compare_pdfs(
                file1,
                file2,
                output_path,
                comparison_method,
                sensitivity,
                highlight_color,
                generate_report
            )
            
            output_files = comparison_result["output_files"]
            
            self.logger.info(
                "PDF comparison completed successfully",
                differences_found=comparison_result["differences_found"],
                pages_compared=comparison_result["pages_compared"],
                output_files=len(output_files)
            )
            
            return output_files
            
        except Exception as e:
            if isinstance(e, (ValidationError, ProcessingError)):
                raise e
            else:
                raise ProcessingError(f"Unexpected error during PDF comparison: {str(e)}")
    
    async def _compare_pdfs(
        self,
        file1: str,
        file2: str,
        output_path: str,
        comparison_method: str,
        sensitivity: str,
        highlight_color: str,
        generate_report: bool
    ) -> Dict[str, Any]:
        """Compare two PDF files and generate results."""
        try:
            # Open both PDFs
            doc1 = fitz.open(file1)
            doc2 = fitz.open(file2)
            
            # Check for encryption
            if doc1.needs_pass or doc2.needs_pass:
                doc1.close()
                doc2.close()
                raise ProcessingError("Cannot compare encrypted PDFs")
            
            pages1 = len(doc1)
            pages2 = len(doc2)
            max_pages = max(pages1, pages2)
            
            # Initialize comparison results
            comparison_data = {
                "file1": os.path.basename(file1),
                "file2": os.path.basename(file2),
                "pages1": pages1,
                "pages2": pages2,
                "method": comparison_method,
                "sensitivity": sensitivity,
                "differences": [],
                "summary": {
                    "total_differences": 0,
                    "pages_with_differences": 0,
                    "identical_pages": 0
                }
            }
            
            output_files = []
            
            # Compare each page
            for page_num in range(max_pages):
                page_diff = await self._compare_page(
                    doc1, doc2, page_num, comparison_method, sensitivity, highlight_color, output_path
                )
                
                if page_diff:
                    comparison_data["differences"].append(page_diff)
                    if page_diff["has_differences"]:
                        comparison_data["summary"]["pages_with_differences"] += 1
                        comparison_data["summary"]["total_differences"] += page_diff["difference_count"]
                    else:
                        comparison_data["summary"]["identical_pages"] += 1
                    
                    # Add visual diff file if created
                    if "visual_diff_file" in page_diff:
                        output_files.append(page_diff["visual_diff_file"])
            
            # Generate comparison report
            if generate_report:
                report_file = await self._generate_comparison_report(comparison_data, output_path)
                output_files.append(report_file)
            
            # Generate summary PDF with all differences
            if comparison_data["summary"]["pages_with_differences"] > 0:
                summary_pdf = await self._create_summary_pdf(comparison_data, output_path)
                output_files.append(summary_pdf)
            
            doc1.close()
            doc2.close()
            
            return {
                "output_files": output_files,
                "differences_found": comparison_data["summary"]["total_differences"],
                "pages_compared": max_pages,
                "comparison_data": comparison_data
            }
            
        except Exception as e:
            raise ProcessingError(f"Failed to compare PDFs: {str(e)}")
    
    async def _compare_page(
        self,
        doc1: fitz.Document,
        doc2: fitz.Document,
        page_num: int,
        comparison_method: str,
        sensitivity: str,
        highlight_color: str,
        output_path: str
    ) -> Optional[Dict[str, Any]]:
        """Compare a single page between two documents."""
        try:
            page_diff = {
                "page_number": page_num + 1,
                "has_differences": False,
                "difference_count": 0,
                "text_differences": [],
                "visual_differences": []
            }
            
            # Check if pages exist
            page1_exists = page_num < len(doc1)
            page2_exists = page_num < len(doc2)
            
            if not page1_exists and not page2_exists:
                return None
            
            if not page1_exists:
                page_diff["has_differences"] = True
                page_diff["difference_count"] = 1
                page_diff["text_differences"].append("Page exists only in second document")
                return page_diff
            
            if not page2_exists:
                page_diff["has_differences"] = True
                page_diff["difference_count"] = 1
                page_diff["text_differences"].append("Page exists only in first document")
                return page_diff
            
            page1 = doc1[page_num]
            page2 = doc2[page_num]
            
            # Text comparison
            if comparison_method in ["text", "both"]:
                text_diff = await self._compare_page_text(page1, page2, sensitivity)
                if text_diff["differences"]:
                    page_diff["has_differences"] = True
                    page_diff["text_differences"] = text_diff["differences"]
                    page_diff["difference_count"] += len(text_diff["differences"])
            
            # Visual comparison
            if comparison_method in ["visual", "both"]:
                visual_diff = await self._compare_page_visual(
                    page1, page2, page_num, sensitivity, highlight_color, output_path
                )
                if visual_diff["has_differences"]:
                    page_diff["has_differences"] = True
                    page_diff["visual_differences"] = visual_diff["differences"]
                    page_diff["difference_count"] += visual_diff["difference_count"]
                    page_diff["visual_diff_file"] = visual_diff["diff_image_path"]
            
            return page_diff
            
        except Exception as e:
            self.logger.warning(f"Failed to compare page {page_num + 1}: {str(e)}")
            return None
    
    async def _compare_page_text(self, page1, page2, sensitivity: str) -> Dict[str, Any]:
        """Compare text content of two pages."""
        try:
            text1 = page1.get_text()
            text2 = page2.get_text()
            
            differences = []
            
            # Simple text comparison
            if text1 != text2:
                # Split into lines for more detailed comparison
                lines1 = text1.split('\n')
                lines2 = text2.split('\n')
                
                max_lines = max(len(lines1), len(lines2))
                
                for i in range(max_lines):
                    line1 = lines1[i] if i < len(lines1) else ""
                    line2 = lines2[i] if i < len(lines2) else ""
                    
                    if line1 != line2:
                        differences.append({
                            "line_number": i + 1,
                            "text1": line1,
                            "text2": line2,
                            "type": "text_change"
                        })
            
            return {
                "differences": differences,
                "text1_length": len(text1),
                "text2_length": len(text2)
            }
            
        except Exception as e:
            self.logger.warning(f"Failed to compare page text: {str(e)}")
            return {"differences": []}
    
    async def _compare_page_visual(
        self,
        page1,
        page2,
        page_num: int,
        sensitivity: str,
        highlight_color: str,
        output_path: str
    ) -> Dict[str, Any]:
        """Compare visual appearance of two pages."""
        try:
            # Set DPI based on sensitivity
            dpi_map = {"low": 72, "medium": 150, "high": 300}
            dpi = dpi_map[sensitivity]
            
            # Convert pages to images
            mat = fitz.Matrix(dpi / 72, dpi / 72)
            pix1 = page1.get_pixmap(matrix=mat)
            pix2 = page2.get_pixmap(matrix=mat)
            
            # Convert to PIL Images
            img1 = Image.frombytes("RGB", [pix1.width, pix1.height], pix1.samples)
            img2 = Image.frombytes("RGB", [pix2.width, pix2.height], pix2.samples)
            
            # Resize images to same size if different
            if img1.size != img2.size:
                max_width = max(img1.width, img2.width)
                max_height = max(img1.height, img2.height)
                
                img1_resized = Image.new("RGB", (max_width, max_height), "white")
                img2_resized = Image.new("RGB", (max_width, max_height), "white")
                
                img1_resized.paste(img1, (0, 0))
                img2_resized.paste(img2, (0, 0))
                
                img1, img2 = img1_resized, img2_resized
            
            # Calculate difference
            diff = ImageChops.difference(img1, img2)
            
            # Check if there are differences
            bbox = diff.getbbox()
            has_differences = bbox is not None
            
            visual_result = {
                "has_differences": has_differences,
                "difference_count": 0,
                "differences": []
            }
            
            if has_differences:
                # Create highlighted difference image
                diff_image = img1.copy()
                
                # Highlight differences
                diff_pixels = diff.load()
                diff_img_pixels = diff_image.load()
                highlight_rgb = self.highlight_colors[highlight_color]
                
                difference_regions = []
                pixel_differences = 0
                
                for y in range(diff.height):
                    for x in range(diff.width):
                        pixel_diff = diff_pixels[x, y]
                        # Check if pixel is different (sum of RGB differences > threshold)
                        if sum(pixel_diff) > 30:  # Threshold for sensitivity
                            diff_img_pixels[x, y] = highlight_rgb
                            pixel_differences += 1
                
                # Save difference image
                diff_filename = f"page_{page_num + 1:03d}_diff.png"
                diff_path = os.path.join(output_path, diff_filename)
                diff_image.save(diff_path)
                
                visual_result.update({
                    "difference_count": pixel_differences,
                    "diff_image_path": diff_path,
                    "differences": [{
                        "type": "visual_change",
                        "pixel_differences": pixel_differences,
                        "bounding_box": bbox
                    }]
                })
            
            return visual_result
            
        except Exception as e:
            self.logger.warning(f"Failed to compare page visually: {str(e)}")
            return {"has_differences": False, "difference_count": 0, "differences": []}

    async def _generate_comparison_report(self, comparison_data: Dict[str, Any], output_path: str) -> str:
        """Generate a detailed comparison report in JSON format."""
        try:
            import json

            report_file = os.path.join(output_path, "comparison_report.json")

            with open(report_file, 'w', encoding='utf-8') as f:
                json.dump(comparison_data, f, indent=2, ensure_ascii=False)

            return report_file

        except Exception as e:
            self.logger.error("Failed to generate comparison report", error=str(e))
            raise ProcessingError(f"Failed to generate comparison report: {str(e)}")

    async def _create_summary_pdf(self, comparison_data: Dict[str, Any], output_path: str) -> str:
        """Create a summary PDF with all differences highlighted."""
        try:
            from reportlab.pdfgen import canvas
            from reportlab.lib.pagesizes import letter
            from reportlab.lib.units import inch

            summary_file = os.path.join(output_path, "comparison_summary.pdf")

            c = canvas.Canvas(summary_file, pagesize=letter)
            width, height = letter

            # Title
            c.setFont("Helvetica-Bold", 16)
            c.drawString(inch, height - inch, "PDF Comparison Summary")

            # Summary statistics
            y_pos = height - inch * 1.5
            c.setFont("Helvetica", 12)

            summary = comparison_data["summary"]
            c.drawString(inch, y_pos, f"File 1: {comparison_data['file1']}")
            y_pos -= 20
            c.drawString(inch, y_pos, f"File 2: {comparison_data['file2']}")
            y_pos -= 20
            c.drawString(inch, y_pos, f"Pages in File 1: {comparison_data['pages1']}")
            y_pos -= 20
            c.drawString(inch, y_pos, f"Pages in File 2: {comparison_data['pages2']}")
            y_pos -= 20
            c.drawString(inch, y_pos, f"Total Differences: {summary['total_differences']}")
            y_pos -= 20
            c.drawString(inch, y_pos, f"Pages with Differences: {summary['pages_with_differences']}")
            y_pos -= 20
            c.drawString(inch, y_pos, f"Identical Pages: {summary['identical_pages']}")
            y_pos -= 40

            # Differences details
            c.setFont("Helvetica-Bold", 14)
            c.drawString(inch, y_pos, "Differences by Page:")
            y_pos -= 30

            c.setFont("Helvetica", 10)

            for diff in comparison_data["differences"]:
                if diff["has_differences"]:
                    if y_pos < inch * 2:
                        c.showPage()
                        y_pos = height - inch

                    c.drawString(inch, y_pos, f"Page {diff['page_number']}:")
                    y_pos -= 15

                    # Text differences
                    for text_diff in diff.get("text_differences", []):
                        if y_pos < inch * 2:
                            c.showPage()
                            y_pos = height - inch

                        c.drawString(inch * 1.2, y_pos, f"• Text: {text_diff}")
                        y_pos -= 12

                    # Visual differences
                    for visual_diff in diff.get("visual_differences", []):
                        if y_pos < inch * 2:
                            c.showPage()
                            y_pos = height - inch

                        c.drawString(inch * 1.2, y_pos, f"• Visual: {visual_diff['pixel_differences']} pixels changed")
                        y_pos -= 12

                    y_pos -= 10

            c.save()
            return summary_file

        except Exception as e:
            self.logger.error("Failed to create summary PDF", error=str(e))
            raise ProcessingError(f"Failed to create summary PDF: {str(e)}")

    def get_comparison_options(self) -> Dict[str, Dict[str, Any]]:
        """Get available comparison options."""
        return {
            "method": {
                "description": "Comparison method",
                "type": "string",
                "options": list(self.comparison_methods.keys()),
                "default": "both",
                "method_descriptions": self.comparison_methods
            },
            "sensitivity": {
                "description": "Comparison sensitivity",
                "type": "string",
                "options": ["low", "medium", "high"],
                "default": "medium",
                "descriptions": {
                    "low": "Less sensitive, faster processing",
                    "medium": "Balanced sensitivity and speed",
                    "high": "Most sensitive, slower processing"
                }
            },
            "highlight_color": {
                "description": "Color for highlighting differences",
                "type": "string",
                "options": list(self.highlight_colors.keys()),
                "default": "red"
            },
            "generate_report": {
                "description": "Generate detailed JSON report",
                "type": "boolean",
                "default": True
            }
        }

    def get_comparison_methods(self) -> Dict[str, str]:
        """Get available comparison methods."""
        return self.comparison_methods.copy()

    def get_highlight_colors(self) -> List[str]:
        """Get available highlight colors."""
        return list(self.highlight_colors.keys())

    async def quick_compare(self, file1: str, file2: str) -> Dict[str, Any]:
        """Perform a quick comparison without generating output files."""
        try:
            doc1 = fitz.open(file1)
            doc2 = fitz.open(file2)

            if doc1.needs_pass or doc2.needs_pass:
                doc1.close()
                doc2.close()
                return {"error": "Cannot compare encrypted PDFs"}

            pages1 = len(doc1)
            pages2 = len(doc2)

            # Quick metadata comparison
            metadata1 = doc1.metadata
            metadata2 = doc2.metadata

            # Quick text comparison (first 5 pages)
            text_differences = 0
            pages_to_check = min(5, pages1, pages2)

            for page_num in range(pages_to_check):
                text1 = doc1[page_num].get_text()
                text2 = doc2[page_num].get_text()
                if text1 != text2:
                    text_differences += 1

            doc1.close()
            doc2.close()

            return {
                "file1": os.path.basename(file1),
                "file2": os.path.basename(file2),
                "pages1": pages1,
                "pages2": pages2,
                "page_count_match": pages1 == pages2,
                "metadata_match": metadata1 == metadata2,
                "text_differences_in_sample": text_differences,
                "sample_pages_checked": pages_to_check,
                "likely_identical": (
                    pages1 == pages2 and
                    metadata1 == metadata2 and
                    text_differences == 0
                )
            }

        except Exception as e:
            return {"error": str(e)}


# Create tool instance
compare_pdf_tool = PDFCompareTool()
