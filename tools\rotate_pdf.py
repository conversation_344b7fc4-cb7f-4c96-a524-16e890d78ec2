"""
PDF rotation tool for rotating pages in PDF documents.
"""
import os
from typing import List, Dict, Any, Optional, Set
from PyPDF2 import PdfWriter, PdfReader
import structlog

from .base_tool import BasePDFTool, ProcessingError, ValidationError

logger = structlog.get_logger()


class PDFRotateTool(BasePDFTool):
    """Tool for rotating pages in PDF files."""
    
    def __init__(self):
        super().__init__("rotate")
        
        # Valid rotation angles
        self.valid_angles = [0, 90, 180, 270, -90, -180, -270]
    
    async def process(
        self,
        input_files: List[str],
        output_path: str,
        parameters: Optional[Dict[str, Any]] = None
    ) -> List[str]:
        """
        Rotate pages in PDF files.
        
        Args:
            input_files: List of PDF file paths to rotate
            output_path: Output directory for rotated PDFs
            parameters: Rotation parameters (angle, pages, etc.)
            
        Returns:
            List containing paths to rotated PDF files
        """
        # Validate inputs
        if len(input_files) == 0:
            raise ValidationError("At least 1 PDF file is required for rotation")
        
        self.validate_input_files(input_files)
        self.validate_pdf_files(input_files)
        
        # Prepare parameters
        params = parameters or {}
        angle = params.get("angle", 90)
        pages = params.get("pages", "all")  # "all", "odd", "even", or specific pages like "1,3,5-7"
        
        # Validate parameters
        if angle not in self.valid_angles:
            raise ValidationError(f"Invalid rotation angle: {angle}. Valid angles: {self.valid_angles}")
        
        # Ensure output directory exists
        self.ensure_output_directory(output_path)
        
        output_files = []
        
        try:
            self.logger.info(
                "Starting PDF rotation",
                input_count=len(input_files),
                angle=angle,
                pages=pages
            )
            
            for i, input_file in enumerate(input_files):
                try:
                    self.logger.debug(f"Rotating file {i+1}/{len(input_files)}", file_path=input_file)
                    
                    # Generate output filename
                    base_name = os.path.splitext(os.path.basename(input_file))[0]
                    output_filename = f"{base_name}_rotated_{angle}deg.pdf"
                    output_file = os.path.join(output_path, output_filename)
                    
                    # Rotate the PDF
                    await self._rotate_pdf(input_file, output_file, angle, pages)
                    
                    # Verify output file was created
                    if not os.path.exists(output_file):
                        raise ProcessingError("Failed to create rotated PDF file")
                    
                    output_size = self.get_file_size_mb(output_file)
                    input_size = self.get_file_size_mb(input_file)
                    
                    self.logger.info(
                        f"File {i+1} rotated successfully",
                        input_file=input_file,
                        output_file=output_file,
                        input_size_mb=round(input_size, 2),
                        output_size_mb=round(output_size, 2)
                    )
                    
                    output_files.append(output_file)
                    
                except Exception as e:
                    self.logger.error(f"Failed to rotate file {i+1}", file_path=input_file, error=str(e))
                    # Clean up any partial output files
                    self.cleanup_files(output_files)
                    raise ProcessingError(f"Failed to rotate {os.path.basename(input_file)}: {str(e)}")
            
            total_input_size = sum(self.get_file_size_mb(f) for f in input_files)
            total_output_size = sum(self.get_file_size_mb(f) for f in output_files)
            
            self.logger.info(
                "PDF rotation completed successfully",
                output_files=len(output_files),
                total_input_size_mb=round(total_input_size, 2),
                total_output_size_mb=round(total_output_size, 2)
            )
            
            return output_files
            
        except Exception as e:
            # Clean up any output files on error
            self.cleanup_files(output_files)
            
            if isinstance(e, (ValidationError, ProcessingError)):
                raise e
            else:
                raise ProcessingError(f"Unexpected error during PDF rotation: {str(e)}")
    
    async def _rotate_pdf(self, input_file: str, output_file: str, angle: int, pages: str):
        """Rotate pages in a single PDF file."""
        try:
            # Read the input PDF
            with open(input_file, 'rb') as pdf_file:
                pdf_reader = PdfReader(pdf_file)
                
                # Check if PDF is encrypted
                if pdf_reader.is_encrypted:
                    raise ProcessingError("Cannot rotate encrypted PDF")
                
                total_pages = len(pdf_reader.pages)
                
                # Determine which pages to rotate
                pages_to_rotate = self._parse_pages_parameter(pages, total_pages)
                
                # Create PDF writer
                pdf_writer = PdfWriter()
                
                # Process each page
                for page_num in range(total_pages):
                    page = pdf_reader.pages[page_num]
                    
                    # Rotate page if it's in the list
                    if (page_num + 1) in pages_to_rotate:
                        page.rotate(angle)
                        self.logger.debug(f"Rotated page {page_num + 1} by {angle} degrees")
                    
                    # Add the page to the writer
                    pdf_writer.add_page(page)
                
                # Copy metadata if available
                if pdf_reader.metadata:
                    pdf_writer.add_metadata(pdf_reader.metadata)
                
                # Write the rotated PDF
                with open(output_file, 'wb') as output_pdf:
                    pdf_writer.write(output_pdf)
                
                self.logger.debug(
                    "PDF rotation completed",
                    input_file=input_file,
                    output_file=output_file,
                    total_pages=total_pages,
                    rotated_pages=len(pages_to_rotate),
                    angle=angle
                )
                
        except Exception as e:
            raise ProcessingError(f"Failed to rotate PDF: {str(e)}")
    
    def _parse_pages_parameter(self, pages: str, total_pages: int) -> Set[int]:
        """Parse the pages parameter to get set of page numbers to rotate."""
        if pages == "all":
            return set(range(1, total_pages + 1))
        elif pages == "odd":
            return set(range(1, total_pages + 1, 2))
        elif pages == "even":
            return set(range(2, total_pages + 1, 2))
        else:
            # Parse specific pages (e.g., "1,3,5-7,10")
            page_numbers = set()
            
            for part in pages.split(','):
                part = part.strip()
                
                if '-' in part:
                    # Range like "5-7"
                    start_str, end_str = part.split('-', 1)
                    start = int(start_str.strip())
                    end = int(end_str.strip())
                    
                    if start < 1 or end > total_pages or start > end:
                        raise ValidationError(f"Invalid page range: {part}")
                    
                    page_numbers.update(range(start, end + 1))
                else:
                    # Single page like "1"
                    page = int(part)
                    
                    if page < 1 or page > total_pages:
                        raise ValidationError(f"Invalid page number: {page}")
                    
                    page_numbers.add(page)
            
            return page_numbers
    
    def get_rotation_options(self) -> Dict[str, Dict[str, Any]]:
        """Get available rotation options."""
        return {
            "angle": {
                "description": "Rotation angle in degrees",
                "type": "integer",
                "options": self.valid_angles,
                "default": 90,
                "note": "Positive values rotate clockwise, negative values rotate counter-clockwise"
            },
            "pages": {
                "description": "Pages to rotate",
                "type": "string",
                "options": ["all", "odd", "even", "specific (e.g., 1,3,5-7)"],
                "default": "all",
                "examples": [
                    "all - rotate all pages",
                    "odd - rotate odd-numbered pages (1, 3, 5, ...)",
                    "even - rotate even-numbered pages (2, 4, 6, ...)",
                    "1,3,5 - rotate specific pages",
                    "1-5,10 - rotate pages 1 through 5 and page 10"
                ]
            }
        }
    
    def get_valid_angles(self) -> List[int]:
        """Get list of valid rotation angles."""
        return self.valid_angles.copy()
    
    async def get_pdf_page_info(self, file_path: str) -> Dict[str, Any]:
        """Get information about PDF pages for rotation planning."""
        try:
            with open(file_path, 'rb') as pdf_file:
                pdf_reader = PdfReader(pdf_file)
                
                if pdf_reader.is_encrypted:
                    return {
                        "filename": os.path.basename(file_path),
                        "error": "PDF is encrypted and cannot be analyzed"
                    }
                
                total_pages = len(pdf_reader.pages)
                page_info = []
                
                # Get information about each page
                for page_num in range(min(total_pages, 10)):  # Limit to first 10 pages for performance
                    page = pdf_reader.pages[page_num]
                    
                    # Get page dimensions
                    width = float(page.mediabox.width)
                    height = float(page.mediabox.height)
                    
                    # Determine current orientation
                    if width > height:
                        orientation = "landscape"
                    elif height > width:
                        orientation = "portrait"
                    else:
                        orientation = "square"
                    
                    page_info.append({
                        "page_number": page_num + 1,
                        "width": round(width, 2),
                        "height": round(height, 2),
                        "orientation": orientation
                    })
                
                return {
                    "filename": os.path.basename(file_path),
                    "total_pages": total_pages,
                    "size_mb": round(self.get_file_size_mb(file_path), 2),
                    "page_info": page_info,
                    "sample_note": f"Showing first {len(page_info)} pages" if total_pages > 10 else None
                }
                
        except Exception as e:
            self.logger.error("Failed to get PDF page info", file_path=file_path, error=str(e))
            return {
                "filename": os.path.basename(file_path),
                "error": str(e)
            }
    
    async def preview_rotation(self, pages_param: str, total_pages: int, angle: int) -> Dict[str, Any]:
        """Preview which pages will be rotated without actually rotating them."""
        try:
            pages_to_rotate = self._parse_pages_parameter(pages_param, total_pages)
            
            return {
                "total_pages": total_pages,
                "pages_parameter": pages_param,
                "rotation_angle": angle,
                "pages_to_rotate": sorted(list(pages_to_rotate)),
                "pages_count": len(pages_to_rotate),
                "pages_unchanged": total_pages - len(pages_to_rotate),
                "is_valid": True
            }
            
        except Exception as e:
            return {
                "total_pages": total_pages,
                "pages_parameter": pages_param,
                "rotation_angle": angle,
                "error": str(e),
                "is_valid": False
            }
    
    def normalize_angle(self, angle: int) -> int:
        """Normalize rotation angle to 0-270 range."""
        # Convert negative angles to positive equivalents
        while angle < 0:
            angle += 360
        
        # Reduce to 0-270 range
        angle = angle % 360
        
        # Round to nearest valid angle
        valid_positive_angles = [0, 90, 180, 270]
        return min(valid_positive_angles, key=lambda x: abs(x - angle))


# Create tool instance
rotate_pdf_tool = PDFRotateTool()
