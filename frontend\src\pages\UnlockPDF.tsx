import React, { useState } from 'react';
import { Unlock, Download, ArrowRight } from 'lucide-react';
import ToolLayout from '../components/ToolLayout';
import FileUpload from '../components/FileUpload';

const UnlockPDF = () => {
  const [files, setFiles] = useState<File[]>([]);
  const [isProcessing, setIsProcessing] = useState(false);
  const [password, setPassword] = useState('');

  const handleFileSelect = (selectedFiles: File[]) => {
    setFiles(selectedFiles);
  };

  const handleUnlock = () => {
    if (files.length === 0) return;
    
    setIsProcessing(true);
    // Simulate processing
    setTimeout(() => {
      setIsProcessing(false);
      alert('PDF déverrouillé avec succès!');
    }, 2000);
  };

  return (
    <ToolLayout
      title="Déverrouiller PDF"
      description="Retirez le mot de passe de sécurité du PDF, de sorte à ce que vous puissiez l'utiliser comme vous le souhaitez"
      icon={<Unlock className="w-8 h-8" />}
      color="from-yellow-500 to-yellow-600"
    >
      <div className="space-y-6">
        <FileUpload
          onFileSelect={handleFileSelect}
          accept=".pdf"
          multiple={false}
          maxFiles={1}
          title="Sélectionnez votre fichier PDF protégé"
          description="Glissez-déposez un fichier PDF protégé par mot de passe ici ou cliquez pour sélectionner"
        />

        {files.length > 0 && (
          <div className="bg-slate-50 p-6 rounded-xl">
            <h3 className="text-lg font-semibold text-slate-700 mb-4">
              Mot de passe requis
            </h3>
            
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-slate-700 mb-2">
                  Mot de passe du PDF
                </label>
                <input
                  type="password"
                  placeholder="Entrez le mot de passe"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  className="w-full p-3 border border-slate-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-yellow-500"
                />
              </div>

              <div className="bg-yellow-50 p-4 rounded-lg">
                <div className="flex items-center space-x-2">
                  <div className="w-2 h-2 bg-yellow-500 rounded-full"></div>
                  <span className="text-sm text-yellow-800 font-medium">Information importante</span>
                </div>
                <p className="text-sm text-yellow-700 mt-1">
                  Le mot de passe est nécessaire pour déverrouiller votre fichier PDF. 
                  Votre fichier sera traité de manière sécurisée et supprimé après traitement.
                </p>
              </div>
            </div>
          </div>
        )}

        {files.length > 0 && password && (
          <div className="flex justify-center">
            <button
              onClick={handleUnlock}
              disabled={isProcessing}
              className="bg-gradient-to-r from-yellow-600 to-orange-600 text-white px-8 py-4 rounded-xl text-lg font-medium hover:shadow-xl transition-all duration-200 transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2"
            >
              {isProcessing ? (
                <>
                  <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white"></div>
                  <span>Déverrouillage en cours...</span>
                </>
              ) : (
                <>
                  <span>Déverrouiller le PDF</span>
                  <ArrowRight className="w-5 h-5" />
                </>
              )}
            </button>
          </div>
        )}
      </div>
    </ToolLayout>
  );
};

export default UnlockPDF;