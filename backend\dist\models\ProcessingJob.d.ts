import { Model, Optional } from 'sequelize';
import { User } from './User';
export interface ProcessingJobAttributes {
    id: string;
    user_id: string;
    tool_name: string;
    status: 'pending' | 'processing' | 'completed' | 'failed' | 'cancelled';
    input_files: string[];
    output_files: string[];
    parameters?: object;
    error_message?: string;
    processing_time_seconds?: number;
    file_size_mb?: number;
    started_at?: Date;
    completed_at?: Date;
    created_at?: Date;
    updated_at?: Date;
}
export interface ProcessingJobCreationAttributes extends Optional<ProcessingJobAttributes, 'id' | 'status' | 'output_files' | 'error_message' | 'processing_time_seconds' | 'file_size_mb' | 'started_at' | 'completed_at' | 'created_at' | 'updated_at'> {
}
export declare class ProcessingJob extends Model<ProcessingJobAttributes, ProcessingJobCreationAttributes> implements ProcessingJobAttributes {
    id: string;
    user_id: string;
    tool_name: string;
    status: 'pending' | 'processing' | 'completed' | 'failed' | 'cancelled';
    input_files: string[];
    output_files: string[];
    parameters?: object;
    error_message?: string;
    processing_time_seconds?: number;
    file_size_mb?: number;
    started_at?: Date;
    completed_at?: Date;
    readonly created_at: Date;
    readonly updated_at: Date;
    readonly user?: User;
    markAsStarted(): void;
    markAsCompleted(outputFiles: string[], processingTimeSeconds: number): void;
    markAsFailed(errorMessage: string): void;
    getDuration(): number | null;
}
export default ProcessingJob;
//# sourceMappingURL=ProcessingJob.d.ts.map