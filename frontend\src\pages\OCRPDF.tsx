import React, { useState } from 'react';
import { Search, Download, ArrowRight } from 'lucide-react';
import ToolLayout from '../components/ToolLayout';
import FileUpload from '../components/FileUpload';

const OCRPDF = () => {
  const [files, setFiles] = useState<File[]>([]);
  const [isProcessing, setIsProcessing] = useState(false);
  const [language, setLanguage] = useState<'fr' | 'en' | 'es' | 'de' | 'auto'>('auto');

  const handleFileSelect = (selectedFiles: File[]) => {
    setFiles(selectedFiles);
  };

  const handleOCR = () => {
    if (files.length === 0) return;
    
    setIsProcessing(true);
    // Simulate processing
    setTimeout(() => {
      setIsProcessing(false);
      alert('OCR appliqué avec succès!');
    }, 3000);
  };

  return (
    <ToolLayout
      title="OCR PDF"
      description="Convertissez en toute simplicité vos PDF numérisés en documents indexables et modifiables"
      icon={<Search className="w-8 h-8" />}
      color="from-blue-500 to-blue-600"
    >
      <div className="space-y-6">
        <FileUpload
          onFileSelect={handleFileSelect}
          accept=".pdf"
          multiple={false}
          maxFiles={1}
          title="Sélectionnez votre PDF numérisé"
          description="Glissez-déposez un fichier PDF numérisé ici ou cliquez pour sélectionner"
        />

        {files.length > 0 && (
          <div className="bg-slate-50 p-6 rounded-xl">
            <h3 className="text-lg font-semibold text-slate-700 mb-4">
              Options de reconnaissance
            </h3>
            
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-slate-700 mb-2">
                  Langue du document
                </label>
                <select 
                  value={language}
                  onChange={(e) => setLanguage(e.target.value as 'fr' | 'en' | 'es' | 'de' | 'auto')}
                  className="w-full p-3 border border-slate-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="auto">Détection automatique</option>
                  <option value="fr">Français</option>
                  <option value="en">English</option>
                  <option value="es">Español</option>
                  <option value="de">Deutsch</option>
                </select>
              </div>

              <div className="space-y-3">
                <div className="flex items-center space-x-3">
                  <input
                    type="checkbox"
                    id="preserve-layout"
                    defaultChecked
                    className="text-blue-600 rounded"
                  />
                  <label htmlFor="preserve-layout" className="text-slate-700">
                    Préserver la mise en page originale
                  </label>
                </div>

                <div className="flex items-center space-x-3">
                  <input
                    type="checkbox"
                    id="extract-tables"
                    defaultChecked
                    className="text-blue-600 rounded"
                  />
                  <label htmlFor="extract-tables" className="text-slate-700">
                    Détecter et extraire les tableaux
                  </label>
                </div>

                <div className="flex items-center space-x-3">
                  <input
                    type="checkbox"
                    id="searchable-text"
                    defaultChecked
                    className="text-blue-600 rounded"
                  />
                  <label htmlFor="searchable-text" className="text-slate-700">
                    Rendre le texte consultable
                  </label>
                </div>

                <div className="flex items-center space-x-3">
                  <input
                    type="checkbox"
                    id="editable-text"
                    className="text-blue-600 rounded"
                  />
                  <label htmlFor="editable-text" className="text-slate-700">
                    Rendre le texte modifiable
                  </label>
                </div>
              </div>

              <div className="bg-green-50 p-4 rounded-lg">
                <div className="flex items-center space-x-2">
                  <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                  <span className="text-sm text-green-800 font-medium">Fonctionnalités OCR</span>
                </div>
                <ul className="text-sm text-green-700 mt-1 space-y-1">
                  <li>• Reconnaissance de texte avec précision élevée</li>
                  <li>• Support de multiples langues</li>
                  <li>• Préservation de la mise en page</li>
                  <li>• Extraction de tableaux et formulaires</li>
                  <li>• Texte sélectionnable et copiable</li>
                </ul>
              </div>
            </div>
          </div>
        )}

        {files.length > 0 && (
          <div className="flex justify-center">
            <button
              onClick={handleOCR}
              disabled={isProcessing}
              className="bg-gradient-to-r from-blue-600 to-indigo-600 text-white px-8 py-4 rounded-xl text-lg font-medium hover:shadow-xl transition-all duration-200 transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2"
            >
              {isProcessing ? (
                <>
                  <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white"></div>
                  <span>Reconnaissance en cours...</span>
                </>
              ) : (
                <>
                  <span>Appliquer l'OCR</span>
                  <ArrowRight className="w-5 h-5" />
                </>
              )}
            </button>
          </div>
        )}
      </div>
    </ToolLayout>
  );
};

export default OCRPDF;