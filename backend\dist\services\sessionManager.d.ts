declare const PLAN_LIMITS: {
    free: {
        maxFilesPerDay: number;
        maxFileSizeMB: number;
        maxProcessingTimePerDay: number;
        allowedTools: string[];
    };
    premium: {
        maxFilesPerDay: number;
        maxFileSizeMB: number;
        maxProcessingTimePerDay: number;
        allowedTools: string[];
    };
    enterprise: {
        maxFilesPerDay: number;
        maxFileSizeMB: number;
        maxProcessingTimePerDay: number;
        allowedTools: string[];
    };
};
export interface QuotaCheckResult {
    canProcess: boolean;
    reason?: string;
    message?: string;
    upgradeRequired?: boolean;
    sessionToken?: string;
    remainingQuota?: {
        files: number;
        processingTimeSeconds: number;
    };
}
export declare class SessionManager {
    static canStartNewSession(userId: string, subscriptionPlan: keyof typeof PLAN_LIMITS, toolName: string): Promise<QuotaCheckResult>;
    static canContinueSession(sessionToken: string, userId: string): Promise<QuotaCheckResult>;
    static recordProcessing(sessionToken: string, userId: string, toolName: string, fileSizeMB: number, processingTimeSeconds: number, inputFileCount: number, outputFileCount: number, success: boolean, errorMessage?: string): Promise<void>;
    static completeSession(sessionToken: string, userId: string): Promise<void>;
    static canDownload(sessionToken: string, userId: string): Promise<boolean>;
    static cleanupExpiredSessions(): Promise<void>;
}
export {};
//# sourceMappingURL=sessionManager.d.ts.map