import React, { createContext, useContext, useState, useEffect } from 'react';
import { ApiService } from '../services/apiClient';
import { useUsage } from './UsageContext';

export interface User {
  id: string;
  email: string;
  name: string;
  plan: 'free' | 'premium';
  dailyUsage: number;
  lastUsageDate: string;
  createdAt: string;
  premiumExpiresAt?: string;
}

interface AuthContextType {
  user: User | null;
  isAuthenticated: boolean;
  login: (email: string, password: string) => Promise<boolean>;
  signup: (email: string, password: string, name: string) => Promise<boolean>;
  logout: () => void;
  updateUser: (updates: Partial<User>) => void;
  getRemainingUsage: () => number;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

interface AuthProviderProps {
  children: React.ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  
  // We'll get this from context after it's created
  const getUsageContext = () => {
    try {
      return useUsage();
    } catch {
      return null;
    }
  };

  // Load user from localStorage on mount
  useEffect(() => {
    const savedUser = localStorage.getItem('currentUser');
    if (savedUser) {
      const userData = JSON.parse(savedUser);
      // Reset daily usage if it's a new day
      const today = new Date().toDateString();
      if (userData.lastUsageDate !== today) {
        userData.dailyUsage = 0;
        userData.lastUsageDate = today;
        localStorage.setItem('currentUser', JSON.stringify(userData));
      }
      setUser(userData);
    }
  }, []);

  const loadDatabase = (): User[] => {
    const db = localStorage.getItem('usersDatabase');
    return db ? JSON.parse(db) : [];
  };

  const saveDatabase = (users: User[]) => {
    localStorage.setItem('usersDatabase', JSON.stringify(users));
  };

  const login = async (email: string, password: string): Promise<boolean> => {
    try {
      const response = await ApiService.login(email, password);

      if (response.success) {
        const userData = response.data.user;
        const tokens = response.data.tokens;

        // Convert backend user format to frontend format
        const frontendUser: User = {
          id: userData.id,
          email: userData.email,
          name: `${userData.firstName} ${userData.lastName}`,
          plan: userData.subscriptionPlan,
          dailyUsage: 0, // Will be fetched separately
          lastUsageDate: new Date().toDateString(),
          createdAt: userData.createdAt || new Date().toISOString()
        };

        setUser(frontendUser);

        // Store tokens and user data
        localStorage.setItem('authToken', tokens.accessToken);
        localStorage.setItem('refreshToken', tokens.refreshToken);
        localStorage.setItem('currentUser', JSON.stringify(frontendUser));

        return true;
      }

      return false;
    } catch (error) {
      console.error('Login failed:', error);

      // Fallback to local storage for development
      const users = loadDatabase();
      const user = users.find(u => u.email === email);

      if (user) {
        const savedPassword = localStorage.getItem(`password_${email}`);
        if (savedPassword === password) {
          const today = new Date().toDateString();
          if (user.lastUsageDate !== today) {
            user.dailyUsage = 0;
            user.lastUsageDate = today;
          }

          setUser(user);
          localStorage.setItem('currentUser', JSON.stringify(user));
          return true;
        }
      }
      return false;
    }
  };

  const signup = async (email: string, password: string, name: string): Promise<boolean> => {
    const users = loadDatabase();
    
    // Check if user already exists
    if (users.find(u => u.email === email)) {
      return false;
    }

    const newUser: User = {
      id: Date.now().toString(),
      email,
      name,
      plan: 'free',
      dailyUsage: 0,
      lastUsageDate: new Date().toDateString(),
      createdAt: new Date().toISOString()
    };

    users.push(newUser);
    saveDatabase(users);
    
    // Save password separately (in a real app, this would be hashed)
    localStorage.setItem(`password_${email}`, password);
    
    setUser(newUser);
    localStorage.setItem('currentUser', JSON.stringify(newUser));
    return true;
  };

  const logout = () => {
    setUser(null);
    localStorage.removeItem('currentUser');
  };

  const updateUser = (updates: Partial<User>) => {
    if (!user) return;
    
    const updatedUser = { ...user, ...updates };
    setUser(updatedUser);
    localStorage.setItem('currentUser', JSON.stringify(updatedUser));
    
    // Update in database
    const users = loadDatabase();
    const userIndex = users.findIndex(u => u.id === user.id);
    if (userIndex !== -1) {
      users[userIndex] = updatedUser;
      saveDatabase(users);
    }
  };


  const getRemainingUsage = (): number => {
    if (!user) return 0;
    if (user.plan === 'premium') return 999;
    return Math.max(0, 3 - user.dailyUsage);
  };

  const isAuthenticated = !!user;

  return (
    <AuthContext.Provider value={{
      user,
      isAuthenticated,
      login,
      signup,
      logout,
      updateUser,
      getRemainingUsage
    }}>
      {children}
    </AuthContext.Provider>
  );
};