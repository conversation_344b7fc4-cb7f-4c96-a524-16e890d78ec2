import React, { createContext, useContext, useState, useEffect } from 'react';
import { ApiService } from '../services/apiClient';

export interface User {
  id: string;
  email: string;
  firstName: string;
  lastName: string;
  subscriptionPlan: 'free' | 'premium' | 'enterprise';
  isVerified: boolean;
  createdAt: string;
  updatedAt: string;
}

interface AuthContextType {
  user: User | null;
  isAuthenticated: boolean;
  loading: boolean;
  login: (email: string, password: string) => Promise<{ success: boolean; error?: string }>;
  signup: (email: string, password: string, confirmPassword: string, firstName: string, lastName: string) => Promise<{ success: boolean; error?: string }>;
  logout: () => void;
  updateUser: (updates: Partial<User>) => Promise<{ success: boolean; error?: string }>;
  refreshUser: () => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

interface AuthProviderProps {
  children: React.ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);

  // Load user from token on mount
  useEffect(() => {
    const initializeAuth = async () => {
      const token = localStorage.getItem('authToken');
      if (token) {
        try {
          const response = await ApiService.getProfile();
          if (response.success) {
            setUser(response.data);
          } else {
            // Invalid token, clear storage
            localStorage.removeItem('authToken');
            localStorage.removeItem('refreshToken');
          }
        } catch (error) {
          console.error('Failed to load user profile:', error);
          localStorage.removeItem('authToken');
          localStorage.removeItem('refreshToken');
        }
      }
      setLoading(false);
    };

    initializeAuth();
  }, []);

  const login = async (email: string, password: string): Promise<{ success: boolean; error?: string }> => {
    try {
      const response = await ApiService.login(email, password);
      
      if (response.success) {
        const userData = response.data.user;
        const tokens = response.data.tokens;
        
        setUser(userData);
        
        // Store tokens
        localStorage.setItem('authToken', tokens.accessToken);
        localStorage.setItem('refreshToken', tokens.refreshToken);
        
        return { success: true };
      }
      
      return { success: false, error: response.error?.message || 'Login failed' };
    } catch (error: any) {
      console.error('Login failed:', error);
      
      const errorMessage = error.response?.data?.error?.message || 'Network error';
      return { success: false, error: errorMessage };
    }
  };

  const signup = async (
    email: string, 
    password: string, 
    confirmPassword: string, 
    firstName: string, 
    lastName: string
  ): Promise<{ success: boolean; error?: string }> => {
    try {
      const response = await ApiService.register(email, password, confirmPassword, firstName, lastName);
      
      if (response.success) {
        const userData = response.data.user;
        const tokens = response.data.tokens;
        
        setUser(userData);
        
        // Store tokens
        localStorage.setItem('authToken', tokens.accessToken);
        localStorage.setItem('refreshToken', tokens.refreshToken);
        
        return { success: true };
      }
      
      return { success: false, error: response.error?.message || 'Registration failed' };
    } catch (error: any) {
      console.error('Signup failed:', error);
      
      const errorMessage = error.response?.data?.error?.message || 'Network error';
      return { success: false, error: errorMessage };
    }
  };

  const logout = () => {
    setUser(null);
    localStorage.removeItem('authToken');
    localStorage.removeItem('refreshToken');
  };

  const updateUser = async (updates: Partial<User>): Promise<{ success: boolean; error?: string }> => {
    try {
      const response = await ApiService.updateProfile(updates);
      
      if (response.success) {
        setUser(response.data);
        return { success: true };
      }
      
      return { success: false, error: response.error?.message || 'Update failed' };
    } catch (error: any) {
      console.error('Update user failed:', error);
      
      const errorMessage = error.response?.data?.error?.message || 'Network error';
      return { success: false, error: errorMessage };
    }
  };

  const refreshUser = async (): Promise<void> => {
    try {
      const response = await ApiService.getProfile();
      if (response.success) {
        setUser(response.data);
      }
    } catch (error) {
      console.error('Failed to refresh user:', error);
    }
  };

  const isAuthenticated = !!user;

  return (
    <AuthContext.Provider value={{
      user,
      isAuthenticated,
      loading,
      login,
      signup,
      logout,
      updateUser,
      refreshUser
    }}>
      {children}
    </AuthContext.Provider>
  );
};

export default AuthContext;
