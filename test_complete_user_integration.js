// Complete integration test for user management with real database
const axios = require('axios');
const FormData = require('form-data');

const BASE_URL = 'http://localhost:3001/api';
const FRONTEND_URL = 'http://localhost:5173';

async function testCompleteUserIntegration() {
  console.log('🧪 Testing Complete User Management Integration...\n');

  try {
    // Test 1: Backend Health Check
    console.log('1. Testing backend health...');
    const healthResponse = await axios.get(`${BASE_URL}/health`);
    console.log('✅ Backend is healthy:', healthResponse.data.status);

    // Test 2: User Registration
    console.log('\n2. Testing user registration...');
    const testUser = {
      email: `test-${Date.now()}@example.com`,
      password: 'TestPassword123!',
      confirmPassword: 'TestPassword123!',
      firstName: 'Test',
      lastName: 'User'
    };

    try {
      const registerResponse = await axios.post(`${BASE_URL}/auth/register`, testUser);
      
      if (registerResponse.data.success) {
        console.log('✅ User registration successful');
        console.log(`   User ID: ${registerResponse.data.data.user.id}`);
        console.log(`   Email: ${registerResponse.data.data.user.email}`);
        console.log(`   Plan: ${registerResponse.data.data.user.subscriptionPlan}`);
        
        const authToken = registerResponse.data.data.tokens.accessToken;
        const userId = registerResponse.data.data.user.id;

        // Test 3: User Login
        console.log('\n3. Testing user login...');
        const loginResponse = await axios.post(`${BASE_URL}/auth/login`, {
          email: testUser.email,
          password: testUser.password
        });

        if (loginResponse.data.success) {
          console.log('✅ User login successful');
          console.log(`   Access token received: ${loginResponse.data.data.tokens.accessToken.substring(0, 20)}...`);
        }

        // Test 4: Get User Profile
        console.log('\n4. Testing get user profile...');
        const profileResponse = await axios.get(`${BASE_URL}/auth/profile`, {
          headers: { Authorization: `Bearer ${authToken}` }
        });

        if (profileResponse.data.success) {
          console.log('✅ Profile retrieval successful');
          const userData = profileResponse.data.data.user || profileResponse.data.data;
          console.log(`   Name: ${userData.firstName} ${userData.lastName}`);
          console.log(`   Email: ${userData.email}`);
          console.log(`   Plan: ${userData.subscriptionPlan}`);
          console.log(`   Verified: ${userData.isVerified}`);
        }

        // Test 5: Update User Profile
        console.log('\n5. Testing update user profile...');
        const updateResponse = await axios.put(`${BASE_URL}/auth/profile`, {
          firstName: 'Updated',
          lastName: 'Name'
        }, {
          headers: { Authorization: `Bearer ${authToken}` }
        });

        if (updateResponse.data.success) {
          console.log('✅ Profile update successful');
          console.log(`   Updated name: ${updateResponse.data.data.user.firstName} ${updateResponse.data.data.user.lastName}`);
        }

        // Test 6: Get Usage Statistics
        console.log('\n6. Testing usage statistics...');
        const usageResponse = await axios.get(`${BASE_URL}/user/usage`, {
          headers: { Authorization: `Bearer ${authToken}` }
        });

        if (usageResponse.data.success) {
          console.log('✅ Usage statistics retrieved');
          console.log(`   Daily usage: ${usageResponse.data.data.today.files} files`);
          console.log(`   Daily limit: ${usageResponse.data.data.limits.maxFilesPerDay} files`);
          console.log(`   Remaining: ${usageResponse.data.data.remaining.files} files`);
        }

        // Test 7: Test Quota System
        console.log('\n7. Testing quota system...');
        const quotaResponse = await axios.post(`${BASE_URL}/pdf-tools/check-quota`, {
          toolName: 'repair_pdf'
        }, {
          headers: { Authorization: `Bearer ${authToken}` }
        });

        if (quotaResponse.data.success) {
          console.log('✅ Quota check successful');
          console.log(`   Session token: ${quotaResponse.data.data.sessionToken.substring(0, 8)}...`);
          console.log(`   Remaining quota: ${quotaResponse.data.data.remainingQuota.files} files`);

          // Test 8: Test PDF Processing with Session
          console.log('\n8. Testing PDF processing with session...');
          const testPdfContent = Buffer.from('%PDF-1.4\n1 0 obj\n<<\n/Type /Catalog\n/Pages 2 0 R\n>>\nendobj\n2 0 obj\n<<\n/Type /Pages\n/Kids [3 0 R]\n/Count 1\n>>\nendobj\n3 0 obj\n<<\n/Type /Page\n/Parent 2 0 R\n/MediaBox [0 0 612 792]\n>>\nendobj\nxref\n0 4\n0000000000 65535 f \n0000000009 00000 n \n0000000074 00000 n \n0000000120 00000 n \ntrailer\n<<\n/Size 4\n/Root 1 0 R\n>>\nstartxref\n179\n%%EOF');
          
          const formData = new FormData();
          formData.append('files', testPdfContent, {
            filename: 'test.pdf',
            contentType: 'application/pdf'
          });
          formData.append('toolName', 'repair_pdf');
          formData.append('sessionToken', quotaResponse.data.data.sessionToken);
          formData.append('parameters', '{}');

          const processResponse = await axios.post(`${BASE_URL}/pdf-tools/process`, formData, {
            headers: {
              ...formData.getHeaders(),
              Authorization: `Bearer ${authToken}`
            }
          });

          if (processResponse.data.success) {
            console.log('✅ PDF processing successful');
            console.log(`   Output files: ${processResponse.data.data.outputFiles.length}`);
            console.log(`   Processing time: ${processResponse.data.data.processingTime}ms`);
            console.log(`   Quota consumed: ${processResponse.data.data.quotaConsumed}`);

            // Test 9: Complete Session
            console.log('\n9. Testing session completion...');
            const completeResponse = await axios.post(`${BASE_URL}/pdf-tools/complete-session`, {
              sessionToken: quotaResponse.data.data.sessionToken
            }, {
              headers: { Authorization: `Bearer ${authToken}` }
            });

            if (completeResponse.data.success) {
              console.log('✅ Session completion successful');
            }
          }
        }

        // Test 10: Get Updated Usage After Processing
        console.log('\n10. Testing updated usage statistics...');
        const updatedUsageResponse = await axios.get(`${BASE_URL}/user/usage`, {
          headers: { Authorization: `Bearer ${authToken}` }
        });

        if (updatedUsageResponse.data.success) {
          console.log('✅ Updated usage statistics retrieved');
          console.log(`   Daily usage: ${updatedUsageResponse.data.data.today.files} files`);
          console.log(`   Remaining: ${updatedUsageResponse.data.data.remaining.files} files`);
        }

        // Test 11: Test Usage History
        console.log('\n11. Testing usage history...');
        const historyResponse = await axios.get(`${BASE_URL}/user/usage/history?days=7`, {
          headers: { Authorization: `Bearer ${authToken}` }
        });

        if (historyResponse.data.success) {
          console.log('✅ Usage history retrieved');
          console.log(`   Records found: ${historyResponse.data.data.records.length}`);
          if (historyResponse.data.data.records.length > 0) {
            const lastRecord = historyResponse.data.data.records[0];
            console.log(`   Last tool used: ${lastRecord.tool_name}`);
            console.log(`   Success: ${lastRecord.success}`);
          }
        }

      } else {
        console.log('❌ User registration failed:', registerResponse.data.error);
      }

    } catch (regError) {
      if (regError.response?.status === 400 && regError.response.data.error.message.includes('already exists')) {
        console.log('ℹ️  User already exists, testing with existing user...');
        
        // Try login with existing user
        const loginResponse = await axios.post(`${BASE_URL}/auth/login`, {
          email: '<EMAIL>',
          password: 'TestPassword123!'
        });

        if (loginResponse.data.success) {
          console.log('✅ Login with existing user successful');
        }
      } else {
        throw regError;
      }
    }

    console.log('\n🎉 Complete User Integration Test Completed!');
    console.log('\n📋 Summary:');
    console.log('   ✅ Backend API: Healthy and responsive');
    console.log('   ✅ User Registration: Working with real database');
    console.log('   ✅ User Authentication: JWT tokens working');
    console.log('   ✅ Profile Management: CRUD operations working');
    console.log('   ✅ Usage Tracking: Real-time database updates');
    console.log('   ✅ Quota System: Session-based enforcement');
    console.log('   ✅ PDF Processing: Integrated with user system');
    console.log('   ✅ Usage History: Persistent storage working');

  } catch (error) {
    console.error('\n❌ Integration test failed:', error.response?.data || error.message);
    console.log('\n🔧 Troubleshooting:');
    console.log('   • Make sure backend is running: cd backend && npm run dev');
    console.log('   • Verify database connection is working');
    console.log('   • Check that all tables are created');
    console.log('   • Ensure frontend dependencies are installed');
  }
}

// Run the test
testCompleteUserIntegration();
