"""
Image to PDF conversion tool.
"""
import os
from typing import List, Dict, Any, Optional
from PIL import Image
from reportlab.pdfgen import canvas
from reportlab.lib.pagesizes import letter, A4, legal
from reportlab.lib.units import inch
import structlog

from .base_tool import BasePDFTool, ProcessingError, ValidationError

logger = structlog.get_logger()


class ImageToPDFTool(BasePDFTool):
    """Tool for converting image files to PDF documents."""
    
    def __init__(self):
        super().__init__("image_to_pdf")
        
        # Supported input formats
        self.supported_formats = {
            ".jpg", ".jpeg", ".png", ".bmp", ".tiff", ".tif", ".gif"
        }
        
        # Page size options
        self.page_sizes = {
            "letter": letter,
            "a4": A4,
            "legal": legal,
            "auto": None  # Auto-fit to image size
        }
    
    def validate_input_files(self, input_files: List[str]) -> bool:
        """Validate that input files are image files."""
        for file_path in input_files:
            if not os.path.exists(file_path):
                raise FileNotFoundError(f"Input file not found: {file_path}")
            
            if not os.access(file_path, os.R_OK):
                raise PermissionError(f"Cannot read input file: {file_path}")
            
            # Check if file is an image
            file_ext = os.path.splitext(file_path)[1].lower()
            if file_ext not in self.supported_formats:
                raise ValidationError(f"Unsupported image format: {file_ext}")
        
        return True
    
    async def process(
        self,
        input_files: List[str],
        output_path: str,
        parameters: Optional[Dict[str, Any]] = None
    ) -> List[str]:
        """
        Convert image files to PDF documents.
        
        Args:
            input_files: List of image file paths to convert
            output_path: Output directory or file path for PDF
            parameters: Conversion parameters (page_size, orientation, etc.)
            
        Returns:
            List containing path to the created PDF file
        """
        # Validate inputs
        if len(input_files) == 0:
            raise ValidationError("At least 1 image file is required for conversion")
        
        self.validate_input_files(input_files)
        
        # Prepare parameters
        params = parameters or {}
        page_size = params.get("page_size", "a4")
        orientation = params.get("orientation", "auto")  # auto, portrait, landscape
        fit_mode = params.get("fit_mode", "fit")  # fit, fill, stretch
        margin = params.get("margin", 0.5)  # inches
        combine_images = params.get("combine_images", True)  # True = one PDF, False = separate PDFs
        
        # Validate parameters
        if page_size not in self.page_sizes:
            raise ValidationError(f"Invalid page size: {page_size}. Available: {list(self.page_sizes.keys())}")
        
        if orientation not in ["auto", "portrait", "landscape"]:
            raise ValidationError("Orientation must be 'auto', 'portrait', or 'landscape'")
        
        if fit_mode not in ["fit", "fill", "stretch"]:
            raise ValidationError("Fit mode must be 'fit', 'fill', or 'stretch'")
        
        # Ensure output directory exists
        if os.path.isdir(output_path):
            output_dir = output_path
        else:
            output_dir = os.path.dirname(output_path)
        
        self.ensure_output_directory(output_dir)
        
        try:
            self.logger.info(
                "Starting image to PDF conversion",
                input_count=len(input_files),
                page_size=page_size,
                orientation=orientation,
                fit_mode=fit_mode,
                combine_images=combine_images
            )
            
            if combine_images:
                # Create single PDF with all images
                output_files = await self._create_combined_pdf(
                    input_files, output_path, page_size, orientation, fit_mode, margin
                )
            else:
                # Create separate PDF for each image
                output_files = await self._create_separate_pdfs(
                    input_files, output_dir, page_size, orientation, fit_mode, margin
                )
            
            total_input_size = sum(self.get_file_size_mb(f) for f in input_files)
            total_output_size = sum(self.get_file_size_mb(f) for f in output_files)
            
            self.logger.info(
                "Image to PDF conversion completed successfully",
                input_images=len(input_files),
                output_pdfs=len(output_files),
                total_input_size_mb=round(total_input_size, 2),
                total_output_size_mb=round(total_output_size, 2)
            )
            
            return output_files
            
        except Exception as e:
            if isinstance(e, (ValidationError, ProcessingError)):
                raise e
            else:
                raise ProcessingError(f"Unexpected error during image to PDF conversion: {str(e)}")
    
    async def _create_combined_pdf(
        self,
        input_files: List[str],
        output_path: str,
        page_size: str,
        orientation: str,
        fit_mode: str,
        margin: float
    ) -> List[str]:
        """Create a single PDF containing all images."""
        try:
            # Determine output filename
            if os.path.isdir(output_path):
                output_filename = "combined_images.pdf"
                output_file = os.path.join(output_path, output_filename)
            else:
                output_file = output_path
            
            # Create PDF canvas
            c = canvas.Canvas(output_file)
            
            for i, image_file in enumerate(input_files):
                try:
                    self.logger.debug(f"Adding image {i+1}/{len(input_files)}", image_file=image_file)
                    
                    # Open and process the image
                    with Image.open(image_file) as img:
                        # Convert to RGB if necessary
                        if img.mode != 'RGB':
                            img = img.convert('RGB')
                        
                        # Determine page dimensions
                        page_width, page_height = self._get_page_dimensions(
                            page_size, orientation, img.size
                        )
                        
                        # Set page size for this page
                        c.setPageSize((page_width, page_height))
                        
                        # Calculate image placement
                        img_x, img_y, img_width, img_height = self._calculate_image_placement(
                            img.size, (page_width, page_height), fit_mode, margin
                        )
                        
                        # Save image temporarily for ReportLab
                        temp_img_path = f"temp_img_{i}.jpg"
                        img.save(temp_img_path, "JPEG", quality=85)
                        
                        # Draw image on PDF
                        c.drawImage(temp_img_path, img_x, img_y, img_width, img_height)
                        
                        # Clean up temporary file
                        os.remove(temp_img_path)
                        
                        # Add new page (except for last image)
                        if i < len(input_files) - 1:
                            c.showPage()
                
                except Exception as e:
                    self.logger.error(f"Failed to process image {i+1}", image_file=image_file, error=str(e))
                    raise ProcessingError(f"Failed to process {os.path.basename(image_file)}: {str(e)}")
            
            # Save the PDF
            c.save()
            
            return [output_file]
            
        except Exception as e:
            raise ProcessingError(f"Failed to create combined PDF: {str(e)}")
    
    async def _create_separate_pdfs(
        self,
        input_files: List[str],
        output_dir: str,
        page_size: str,
        orientation: str,
        fit_mode: str,
        margin: float
    ) -> List[str]:
        """Create separate PDF files for each image."""
        output_files = []
        
        for i, image_file in enumerate(input_files):
            try:
                self.logger.debug(f"Converting image {i+1}/{len(input_files)}", image_file=image_file)
                
                # Generate output filename
                base_name = os.path.splitext(os.path.basename(image_file))[0]
                output_filename = f"{base_name}.pdf"
                output_file = os.path.join(output_dir, output_filename)
                
                # Create single-image PDF
                await self._create_single_image_pdf(
                    image_file, output_file, page_size, orientation, fit_mode, margin
                )
                
                output_files.append(output_file)
                
            except Exception as e:
                self.logger.error(f"Failed to convert image {i+1}", image_file=image_file, error=str(e))
                # Clean up any partial output files
                self.cleanup_files(output_files)
                raise ProcessingError(f"Failed to convert {os.path.basename(image_file)}: {str(e)}")
        
        return output_files
    
    async def _create_single_image_pdf(
        self,
        image_file: str,
        output_file: str,
        page_size: str,
        orientation: str,
        fit_mode: str,
        margin: float
    ):
        """Create a PDF file containing a single image."""
        try:
            # Open and process the image
            with Image.open(image_file) as img:
                # Convert to RGB if necessary
                if img.mode != 'RGB':
                    img = img.convert('RGB')
                
                # Determine page dimensions
                page_width, page_height = self._get_page_dimensions(
                    page_size, orientation, img.size
                )
                
                # Create PDF canvas
                c = canvas.Canvas(output_file, pagesize=(page_width, page_height))
                
                # Calculate image placement
                img_x, img_y, img_width, img_height = self._calculate_image_placement(
                    img.size, (page_width, page_height), fit_mode, margin
                )
                
                # Save image temporarily for ReportLab
                temp_img_path = f"temp_single_img.jpg"
                img.save(temp_img_path, "JPEG", quality=85)
                
                # Draw image on PDF
                c.drawImage(temp_img_path, img_x, img_y, img_width, img_height)
                
                # Save the PDF
                c.save()
                
                # Clean up temporary file
                os.remove(temp_img_path)
                
        except Exception as e:
            raise ProcessingError(f"Failed to create PDF for image: {str(e)}")
    
    def _get_page_dimensions(self, page_size: str, orientation: str, image_size: tuple) -> tuple:
        """Get page dimensions based on page size and orientation."""
        if page_size == "auto":
            # Use image dimensions (convert pixels to points, assuming 72 DPI)
            width = image_size[0] * 72 / 96  # Convert from 96 DPI to 72 DPI
            height = image_size[1] * 72 / 96
            return (width, height)
        
        # Get standard page size
        base_size = self.page_sizes[page_size]
        width, height = base_size
        
        if orientation == "auto":
            # Choose orientation based on image aspect ratio
            image_ratio = image_size[0] / image_size[1]
            page_ratio = width / height
            
            if image_ratio > page_ratio:
                # Image is wider - use landscape
                return (max(width, height), min(width, height))
            else:
                # Image is taller - use portrait
                return (min(width, height), max(width, height))
        elif orientation == "landscape":
            return (max(width, height), min(width, height))
        else:  # portrait
            return (min(width, height), max(width, height))
    
    def _calculate_image_placement(
        self, 
        image_size: tuple, 
        page_size: tuple, 
        fit_mode: str, 
        margin: float
    ) -> tuple:
        """Calculate image position and size on the page."""
        page_width, page_height = page_size
        img_width, img_height = image_size
        
        # Convert margin from inches to points
        margin_points = margin * inch
        
        # Available space after margins
        available_width = page_width - (2 * margin_points)
        available_height = page_height - (2 * margin_points)
        
        if fit_mode == "stretch":
            # Stretch to fill available space
            final_width = available_width
            final_height = available_height
        elif fit_mode == "fill":
            # Scale to fill available space, may crop
            scale_x = available_width / img_width
            scale_y = available_height / img_height
            scale = max(scale_x, scale_y)
            
            final_width = img_width * scale
            final_height = img_height * scale
        else:  # fit
            # Scale to fit within available space, maintain aspect ratio
            scale_x = available_width / img_width
            scale_y = available_height / img_height
            scale = min(scale_x, scale_y)
            
            final_width = img_width * scale
            final_height = img_height * scale
        
        # Center the image
        x = margin_points + (available_width - final_width) / 2
        y = margin_points + (available_height - final_height) / 2
        
        return (x, y, final_width, final_height)
    
    def get_conversion_options(self) -> Dict[str, Dict[str, Any]]:
        """Get available conversion options."""
        return {
            "page_size": {
                "description": "PDF page size",
                "type": "string",
                "options": list(self.page_sizes.keys()),
                "default": "a4"
            },
            "orientation": {
                "description": "Page orientation",
                "type": "string",
                "options": ["auto", "portrait", "landscape"],
                "default": "auto"
            },
            "fit_mode": {
                "description": "How to fit image on page",
                "type": "string",
                "options": ["fit", "fill", "stretch"],
                "default": "fit"
            },
            "margin": {
                "description": "Page margin in inches",
                "type": "number",
                "min": 0,
                "max": 2,
                "default": 0.5
            },
            "combine_images": {
                "description": "Combine all images into one PDF",
                "type": "boolean",
                "default": True
            }
        }
    
    def get_supported_formats(self) -> List[str]:
        """Get list of supported input image formats."""
        return list(self.supported_formats)


# Create tool instance
image_to_pdf_tool = ImageToPDFTool()
