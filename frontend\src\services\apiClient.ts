import axios, { AxiosInstance, AxiosResponse } from 'axios';

// API Configuration
const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:3001/api';

// Create axios instance
const apiClient: AxiosInstance = axios.create({
  baseURL: API_BASE_URL,
  timeout: 30000, // 30 seconds
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor to add auth token
apiClient.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('authToken');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor for error handling
apiClient.interceptors.response.use(
  (response: AxiosResponse) => {
    return response;
  },
  (error) => {
    if (error.response?.status === 401) {
      // Token expired or invalid
      localStorage.removeItem('authToken');
      localStorage.removeItem('refreshToken');
      window.location.href = '/signin';
    }
    return Promise.reject(error);
  }
);

// API Service Class
export class ApiService {
  
  // Authentication
  static async login(email: string, password: string) {
    const response = await apiClient.post('/auth/login', { email, password });
    return response.data;
  }

  static async register(email: string, password: string, confirmPassword: string, firstName: string, lastName: string) {
    const response = await apiClient.post('/auth/register', {
      email,
      password,
      confirmPassword,
      firstName,
      lastName
    });
    return response.data;
  }

  static async getProfile() {
    const response = await apiClient.get('/auth/profile');
    return response.data;
  }

  static async updateProfile(data: any) {
    const response = await apiClient.put('/auth/profile', data);
    return response.data;
  }

  // PDF Tools
  static async getAvailableTools() {
    const response = await apiClient.get('/pdf-tools/tools');
    return response.data;
  }

  static async checkQuota(toolName: string) {
    const response = await apiClient.post('/pdf-tools/check-quota', { toolName });
    return response.data;
  }

  static async processFiles(files: File[], toolName: string, sessionToken: string, parameters: any = {}) {
    const formData = new FormData();
    
    files.forEach(file => {
      formData.append('files', file);
    });
    
    formData.append('toolName', toolName);
    formData.append('sessionToken', sessionToken);
    formData.append('parameters', JSON.stringify(parameters));

    const response = await apiClient.post('/pdf-tools/process', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    
    return response.data;
  }

  static async completeSession(sessionToken: string) {
    const response = await apiClient.post('/pdf-tools/complete-session', { sessionToken });
    return response.data;
  }

  // User Usage
  static async getUsage() {
    const response = await apiClient.get('/user/usage');
    return response.data;
  }

  static async getUsageHistory(days: number = 7, page: number = 1, limit: number = 50) {
    const response = await apiClient.get('/user/usage/history', {
      params: { days, page, limit }
    });
    return response.data;
  }

  static async getSubscriptionPlans() {
    const response = await apiClient.get('/user/plans');
    return response.data;
  }

  // Health Check
  static async healthCheck() {
    const response = await apiClient.get('/health');
    return response.data;
  }
}

export default apiClient;
