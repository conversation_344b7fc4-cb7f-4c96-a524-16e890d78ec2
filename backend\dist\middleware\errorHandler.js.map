{"version": 3, "file": "errorHandler.js", "sourceRoot": "", "sources": ["../../src/middleware/errorHandler.ts"], "names": [], "mappings": ";;;AACA,4CAAyC;AAOzC,MAAa,eAAgB,SAAQ,KAAK;IAIxC,YAAY,OAAe;QACzB,KAAK,CAAC,OAAO,CAAC,CAAC;QAJjB,eAAU,GAAG,GAAG,CAAC;QACjB,kBAAa,GAAG,IAAI,CAAC;QAInB,IAAI,CAAC,IAAI,GAAG,iBAAiB,CAAC;IAChC,CAAC;CACF;AARD,0CAQC;AAED,MAAa,mBAAoB,SAAQ,KAAK;IAI5C,YAAY,UAAkB,yBAAyB;QACrD,KAAK,CAAC,OAAO,CAAC,CAAC;QAJjB,eAAU,GAAG,GAAG,CAAC;QACjB,kBAAa,GAAG,IAAI,CAAC;QAInB,IAAI,CAAC,IAAI,GAAG,qBAAqB,CAAC;IACpC,CAAC;CACF;AARD,kDAQC;AAED,MAAa,kBAAmB,SAAQ,KAAK;IAI3C,YAAY,UAAkB,0BAA0B;QACtD,KAAK,CAAC,OAAO,CAAC,CAAC;QAJjB,eAAU,GAAG,GAAG,CAAC;QACjB,kBAAa,GAAG,IAAI,CAAC;QAInB,IAAI,CAAC,IAAI,GAAG,oBAAoB,CAAC;IACnC,CAAC;CACF;AARD,gDAQC;AAED,MAAa,aAAc,SAAQ,KAAK;IAItC,YAAY,UAAkB,oBAAoB;QAChD,KAAK,CAAC,OAAO,CAAC,CAAC;QAJjB,eAAU,GAAG,GAAG,CAAC;QACjB,kBAAa,GAAG,IAAI,CAAC;QAInB,IAAI,CAAC,IAAI,GAAG,eAAe,CAAC;IAC9B,CAAC;CACF;AARD,sCAQC;AAED,MAAa,eAAgB,SAAQ,KAAK;IAIxC,YAAY,OAAe;QACzB,KAAK,CAAC,OAAO,CAAC,CAAC;QAJjB,eAAU,GAAG,GAAG,CAAC;QACjB,kBAAa,GAAG,IAAI,CAAC;QAInB,IAAI,CAAC,IAAI,GAAG,iBAAiB,CAAC;IAChC,CAAC;CACF;AARD,0CAQC;AAED,MAAa,cAAe,SAAQ,KAAK;IAIvC,YAAY,UAAkB,qBAAqB;QACjD,KAAK,CAAC,OAAO,CAAC,CAAC;QAJjB,eAAU,GAAG,GAAG,CAAC;QACjB,kBAAa,GAAG,IAAI,CAAC;QAInB,IAAI,CAAC,IAAI,GAAG,gBAAgB,CAAC;IAC/B,CAAC;CACF;AARD,wCAQC;AAEM,MAAM,YAAY,GAAG,CAC1B,KAAe,EACf,GAAY,EACZ,GAAa,EACb,IAAkB,EACZ,EAAE;IACR,MAAM,UAAU,GAAG,KAAK,CAAC,UAAU,IAAI,GAAG,CAAC;IAC3C,MAAM,aAAa,GAAG,KAAK,CAAC,aAAa,IAAI,KAAK,CAAC;IAGnD,eAAM,CAAC,KAAK,CAAC,eAAe,EAAE;QAC5B,KAAK,EAAE,KAAK,CAAC,OAAO;QACpB,KAAK,EAAE,KAAK,CAAC,KAAK;QAClB,UAAU;QACV,aAAa;QACb,MAAM,EAAE,GAAG,CAAC,MAAM;QAClB,GAAG,EAAE,GAAG,CAAC,GAAG;QACZ,SAAS,EAAE,GAAG,CAAC,GAAG,CAAC,YAAY,CAAC;QAChC,EAAE,EAAE,GAAG,CAAC,EAAE;KACX,CAAC,CAAC;IAGH,MAAM,OAAO,GAAG,aAAa,IAAI,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,YAAY;QACpE,CAAC,CAAC,KAAK,CAAC,OAAO;QACf,CAAC,CAAC,uBAAuB,CAAC;IAE5B,GAAG,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC,IAAI,CAAC;QAC1B,OAAO,EAAE,KAAK;QACd,KAAK,EAAE;YACL,OAAO;YACP,IAAI,EAAE,KAAK,CAAC,IAAI;YAChB,GAAG,CAAC,OAAO,CAAC,GAAG,CAAC,QAAQ,KAAK,YAAY,IAAI,CAAC,aAAa,IAAI;gBAC7D,KAAK,EAAE,KAAK,CAAC,KAAK;aACnB,CAAC;SACH;QACD,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;KACpC,CAAC,CAAC;AACL,CAAC,CAAC;AArCW,QAAA,YAAY,gBAqCvB;AAEK,MAAM,YAAY,GAAG,CAAC,EAAY,EAAE,EAAE;IAC3C,OAAO,CAAC,GAAY,EAAE,GAAa,EAAE,IAAkB,EAAE,EAAE;QACzD,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC,GAAG,EAAE,GAAG,EAAE,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;IAClD,CAAC,CAAC;AACJ,CAAC,CAAC;AAJW,QAAA,YAAY,gBAIvB"}