import { v4 as uuidv4 } from 'uuid';
import { Op } from 'sequelize';
import { ProcessingSession } from '../models/ProcessingSession';
import { UsageRecord } from '../models/UsageRecord';
import { logger } from '../utils/logger';

// Guest usage tracking (in-memory store)
interface GuestUsage {
  files: number;
  processingTime: number;
  lastReset: Date;
}

const guestUsageStore = new Map<string, GuestUsage>();

// Helper function to get guest identifier (IP-based for now)
const getGuestId = (req: any): string => {
  const ip = req.ip || req.connection.remoteAddress || 'unknown';
  return `guest_${ip}`;
};

// Helper function to reset daily usage if needed
const resetGuestUsageIfNeeded = (usage: GuestUsage): GuestUsage => {
  const now = new Date();
  const lastReset = new Date(usage.lastReset);

  // Reset if it's a new day
  if (now.getDate() !== lastReset.getDate() ||
      now.getMonth() !== lastReset.getMonth() ||
      now.getFullYear() !== lastReset.getFullYear()) {
    return {
      files: 0,
      processingTime: 0,
      lastReset: now
    };
  }

  return usage;
};

// Subscription plan limits
const PLAN_LIMITS = {
  guest: {
    maxFilesPerDay: 3,
    maxFileSizeMB: 5,
    maxProcessingTimePerDay: 60, // 1 minute
    allowedTools: ['repair_pdf', 'merge_pdf', 'compress_pdf', 'split_pdf']
  },
  free: {
    maxFilesPerDay: 10,
    maxFileSizeMB: 10,
    maxProcessingTimePerDay: 300, // 5 minutes
    allowedTools: ['repair_pdf', 'merge_pdf', 'compress_pdf', 'split_pdf']
  },
  premium: {
    maxFilesPerDay: 100,
    maxFileSizeMB: 50,
    maxProcessingTimePerDay: 3600, // 1 hour
    allowedTools: ['*'] // All tools
  },
  enterprise: {
    maxFilesPerDay: 1000,
    maxFileSizeMB: 100,
    maxProcessingTimePerDay: 36000, // 10 hours
    allowedTools: ['*'] // All tools
  }
};

export interface QuotaCheckResult {
  canProcess: boolean;
  reason?: string;
  message?: string;
  upgradeRequired?: boolean;
  sessionToken?: string;
  remainingQuota?: {
    files: number;
    processingTimeSeconds: number;
  };
}

export class SessionManager {
  
  /**
   * Check if user can start a new processing session
   */
  static async canStartNewSession(
    userId: string | undefined,
    subscriptionPlan: keyof typeof PLAN_LIMITS,
    toolName: string,
    req?: any
  ): Promise<QuotaCheckResult> {
    try {
      const planLimits = PLAN_LIMITS[subscriptionPlan];

      // Check if tool is allowed for this plan
      const toolAllowed = planLimits.allowedTools.includes('*') ||
                         planLimits.allowedTools.includes(toolName);

      if (!toolAllowed) {
        return {
          canProcess: false,
          reason: 'tool_not_allowed',
          message: `Tool ${toolName} is not available for ${subscriptionPlan} plan`,
          upgradeRequired: true
        };
      }

      let totalFilesToday = 0;
      let totalProcessingTimeToday = 0;

      // Handle guest users differently
      if (!userId || subscriptionPlan === 'guest') {
        // For guest users, use in-memory tracking
        const guestId = req ? getGuestId(req) : 'anonymous';
        let guestUsage = guestUsageStore.get(guestId) || {
          files: 0,
          processingTime: 0,
          lastReset: new Date()
        };

        // Reset if needed
        guestUsage = resetGuestUsageIfNeeded(guestUsage);
        guestUsageStore.set(guestId, guestUsage);

        totalFilesToday = guestUsage.files;
        totalProcessingTimeToday = guestUsage.processingTime;
      } else {
        // For authenticated users, use database
        const today = new Date();
        today.setHours(0, 0, 0, 0);

        const todayUsage = await UsageRecord.findAll({
          where: {
            user_id: userId,
            created_at: {
              [Op.gte]: today
            }
          }
        });

        totalFilesToday = todayUsage.length;
        totalProcessingTimeToday = todayUsage.reduce(
          (sum, record) => sum + record.processing_time_seconds, 0
        );
      }

      // Check daily limits
      if (totalFilesToday >= planLimits.maxFilesPerDay) {
        return {
          canProcess: false,
          reason: 'daily_file_limit',
          message: `Daily file limit of ${planLimits.maxFilesPerDay} reached`,
          upgradeRequired: subscriptionPlan === 'free'
        };
      }

      if (totalProcessingTimeToday >= planLimits.maxProcessingTimePerDay) {
        return {
          canProcess: false,
          reason: 'daily_processing_limit',
          message: `Daily processing time limit reached`,
          upgradeRequired: subscriptionPlan === 'free'
        };
      }

      // Create new session
      const sessionToken = uuidv4();
      const expiresAt = new Date();
      expiresAt.setHours(expiresAt.getHours() + 2); // 2 hour session

      // Only create database session for authenticated users
      if (userId) {
        await ProcessingSession.create({
          user_id: userId,
          session_token: sessionToken,
          expires_at: expiresAt
        });
      }

      return {
        canProcess: true,
        sessionToken,
        remainingQuota: {
          files: planLimits.maxFilesPerDay - totalFilesToday,
          processingTimeSeconds: planLimits.maxProcessingTimePerDay - totalProcessingTimeToday
        }
      };

    } catch (error) {
      logger.error('Error checking session quota', { userId, error });
      return {
        canProcess: false,
        reason: 'system_error',
        message: 'Unable to check quota at this time'
      };
    }
  }

  /**
   * Check if user can continue processing in existing session
   */
  static async canContinueSession(
    sessionToken: string,
    userId: string
  ): Promise<QuotaCheckResult> {
    try {
      const session = await ProcessingSession.findOne({
        where: {
          session_token: sessionToken,
          user_id: userId
        }
      });

      if (!session) {
        return {
          canProcess: false,
          reason: 'session_not_found',
          message: 'Processing session not found'
        };
      }

      if (!session.isActive()) {
        return {
          canProcess: false,
          reason: 'session_expired',
          message: 'Processing session has expired'
        };
      }

      return {
        canProcess: true,
        sessionToken
      };

    } catch (error) {
      logger.error('Error checking session continuation', { sessionToken, userId, error });
      return {
        canProcess: false,
        reason: 'system_error',
        message: 'Unable to verify session'
      };
    }
  }

  /**
   * Record processing in session
   */
  static async recordProcessing(
    sessionToken: string,
    userId: string | undefined,
    toolName: string,
    fileSizeMB: number,
    processingTimeSeconds: number,
    inputFileCount: number,
    outputFileCount: number,
    success: boolean,
    errorMessage?: string,
    req?: any
  ): Promise<void> {
    try {
      if (userId) {
        // Handle authenticated users
        const session = await ProcessingSession.findOne({
          where: {
            session_token: sessionToken,
            user_id: userId
          }
        });

        if (session && session.isActive()) {
          // Update session
          session.incrementUsage();
          await session.save();

          // Record usage in database
          await UsageRecord.create({
            user_id: userId,
            tool_name: toolName,
            file_size_mb: fileSizeMB,
            processing_time_seconds: processingTimeSeconds,
            input_file_count: inputFileCount,
            output_file_count: outputFileCount,
            success,
            error_message: errorMessage
          });
        }
      } else {
        // Handle guest users - update in-memory store
        const guestId = req ? getGuestId(req) : 'anonymous';
        let guestUsage = guestUsageStore.get(guestId) || {
          files: 0,
          processingTime: 0,
          lastReset: new Date()
        };

        // Reset if needed
        guestUsage = resetGuestUsageIfNeeded(guestUsage);

        // Update usage
        if (success) {
          guestUsage.files += inputFileCount;
          guestUsage.processingTime += processingTimeSeconds;
        }

        guestUsageStore.set(guestId, guestUsage);
      }

      logger.info('Processing recorded in session', {
        sessionToken,
        userId: userId || 'guest',
        toolName,
        success
      });
    } catch (error) {
      logger.error('Error recording processing in session', { 
        sessionToken, 
        userId, 
        error 
      });
    }
  }

  /**
   * Complete session (allow downloads but prevent new processing)
   */
  static async completeSession(sessionToken: string, userId: string): Promise<void> {
    try {
      const session = await ProcessingSession.findOne({
        where: {
          session_token: sessionToken,
          user_id: userId
        }
      });

      if (session) {
        session.markCompleted();
        await session.save();

        logger.info('Session completed', { sessionToken, userId });
      }
    } catch (error) {
      logger.error('Error completing session', { sessionToken, userId, error });
    }
  }

  /**
   * Check if user can download from session
   */
  static async canDownload(sessionToken: string, userId: string): Promise<boolean> {
    try {
      const session = await ProcessingSession.findOne({
        where: {
          session_token: sessionToken,
          user_id: userId
        }
      });

      return session ? session.can_download : false;
    } catch (error) {
      logger.error('Error checking download permission', { sessionToken, userId, error });
      return false;
    }
  }

  /**
   * Cleanup expired sessions
   */
  static async cleanupExpiredSessions(): Promise<void> {
    try {
      const now = new Date();
      
      await ProcessingSession.update(
        { status: 'expired' },
        {
          where: {
            expires_at: { [Op.lt]: now },
            status: 'active'
          }
        }
      );

      logger.info('Expired sessions cleaned up');
    } catch (error) {
      logger.error('Error cleaning up expired sessions', { error });
    }
  }
}

// Run cleanup every hour
setInterval(() => {
  SessionManager.cleanupExpiredSessions();
}, 60 * 60 * 1000);
