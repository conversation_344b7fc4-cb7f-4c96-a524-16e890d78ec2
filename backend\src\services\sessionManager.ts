import { v4 as uuidv4 } from 'uuid';
import { Op } from 'sequelize';
import { ProcessingSession } from '../models/ProcessingSession';
import { UsageRecord } from '../models/UsageRecord';
import { logger } from '../utils/logger';

// Subscription plan limits
const PLAN_LIMITS = {
  free: {
    maxFilesPerDay: 10,
    maxFileSizeMB: 10,
    maxProcessingTimePerDay: 300, // 5 minutes
    allowedTools: ['repair_pdf', 'merge_pdf', 'compress_pdf', 'split_pdf']
  },
  premium: {
    maxFilesPerDay: 100,
    maxFileSizeMB: 50,
    maxProcessingTimePerDay: 3600, // 1 hour
    allowedTools: ['*'] // All tools
  },
  enterprise: {
    maxFilesPerDay: 1000,
    maxFileSizeMB: 100,
    maxProcessingTimePerDay: 36000, // 10 hours
    allowedTools: ['*'] // All tools
  }
};

export interface QuotaCheckResult {
  canProcess: boolean;
  reason?: string;
  message?: string;
  upgradeRequired?: boolean;
  sessionToken?: string;
  remainingQuota?: {
    files: number;
    processingTimeSeconds: number;
  };
}

export class SessionManager {
  
  /**
   * Check if user can start a new processing session
   */
  static async canStartNewSession(
    userId: string, 
    subscriptionPlan: keyof typeof PLAN_LIMITS,
    toolName: string
  ): Promise<QuotaCheckResult> {
    try {
      const planLimits = PLAN_LIMITS[subscriptionPlan];
      
      // Check if tool is allowed for this plan
      const toolAllowed = planLimits.allowedTools.includes('*') || 
                         planLimits.allowedTools.includes(toolName);
      
      if (!toolAllowed) {
        return {
          canProcess: false,
          reason: 'tool_not_allowed',
          message: `Tool ${toolName} is not available for ${subscriptionPlan} plan`,
          upgradeRequired: true
        };
      }

      // Calculate today's usage (excluding active sessions)
      const today = new Date();
      today.setHours(0, 0, 0, 0);
      
      const todayUsage = await UsageRecord.findAll({
        where: {
          user_id: userId,
          created_at: {
            [Op.gte]: today
          }
        }
      });

      const totalFilesToday = todayUsage.length;
      const totalProcessingTimeToday = todayUsage.reduce(
        (sum, record) => sum + record.processing_time_seconds, 0
      );

      // Check daily limits
      if (totalFilesToday >= planLimits.maxFilesPerDay) {
        return {
          canProcess: false,
          reason: 'daily_file_limit',
          message: `Daily file limit of ${planLimits.maxFilesPerDay} reached`,
          upgradeRequired: subscriptionPlan === 'free'
        };
      }

      if (totalProcessingTimeToday >= planLimits.maxProcessingTimePerDay) {
        return {
          canProcess: false,
          reason: 'daily_processing_limit',
          message: `Daily processing time limit reached`,
          upgradeRequired: subscriptionPlan === 'free'
        };
      }

      // Create new session
      const sessionToken = uuidv4();
      const expiresAt = new Date();
      expiresAt.setHours(expiresAt.getHours() + 2); // 2 hour session

      await ProcessingSession.create({
        user_id: userId,
        session_token: sessionToken,
        expires_at: expiresAt
      });

      return {
        canProcess: true,
        sessionToken,
        remainingQuota: {
          files: planLimits.maxFilesPerDay - totalFilesToday,
          processingTimeSeconds: planLimits.maxProcessingTimePerDay - totalProcessingTimeToday
        }
      };

    } catch (error) {
      logger.error('Error checking session quota', { userId, error });
      return {
        canProcess: false,
        reason: 'system_error',
        message: 'Unable to check quota at this time'
      };
    }
  }

  /**
   * Check if user can continue processing in existing session
   */
  static async canContinueSession(
    sessionToken: string,
    userId: string
  ): Promise<QuotaCheckResult> {
    try {
      const session = await ProcessingSession.findOne({
        where: {
          session_token: sessionToken,
          user_id: userId
        }
      });

      if (!session) {
        return {
          canProcess: false,
          reason: 'session_not_found',
          message: 'Processing session not found'
        };
      }

      if (!session.isActive()) {
        return {
          canProcess: false,
          reason: 'session_expired',
          message: 'Processing session has expired'
        };
      }

      return {
        canProcess: true,
        sessionToken
      };

    } catch (error) {
      logger.error('Error checking session continuation', { sessionToken, userId, error });
      return {
        canProcess: false,
        reason: 'system_error',
        message: 'Unable to verify session'
      };
    }
  }

  /**
   * Record processing in session
   */
  static async recordProcessing(
    sessionToken: string,
    userId: string,
    toolName: string,
    fileSizeMB: number,
    processingTimeSeconds: number,
    inputFileCount: number,
    outputFileCount: number,
    success: boolean,
    errorMessage?: string
  ): Promise<void> {
    try {
      const session = await ProcessingSession.findOne({
        where: {
          session_token: sessionToken,
          user_id: userId
        }
      });

      if (session && session.isActive()) {
        // Update session
        session.incrementUsage();
        await session.save();

        // Record usage
        await UsageRecord.create({
          user_id: userId,
          tool_name: toolName,
          file_size_mb: fileSizeMB,
          processing_time_seconds: processingTimeSeconds,
          input_file_count: inputFileCount,
          output_file_count: outputFileCount,
          success,
          error_message: errorMessage
        });

        logger.info('Processing recorded in session', {
          sessionToken,
          userId,
          toolName,
          success
        });
      }
    } catch (error) {
      logger.error('Error recording processing in session', { 
        sessionToken, 
        userId, 
        error 
      });
    }
  }

  /**
   * Complete session (allow downloads but prevent new processing)
   */
  static async completeSession(sessionToken: string, userId: string): Promise<void> {
    try {
      const session = await ProcessingSession.findOne({
        where: {
          session_token: sessionToken,
          user_id: userId
        }
      });

      if (session) {
        session.markCompleted();
        await session.save();

        logger.info('Session completed', { sessionToken, userId });
      }
    } catch (error) {
      logger.error('Error completing session', { sessionToken, userId, error });
    }
  }

  /**
   * Check if user can download from session
   */
  static async canDownload(sessionToken: string, userId: string): Promise<boolean> {
    try {
      const session = await ProcessingSession.findOne({
        where: {
          session_token: sessionToken,
          user_id: userId
        }
      });

      return session ? session.can_download : false;
    } catch (error) {
      logger.error('Error checking download permission', { sessionToken, userId, error });
      return false;
    }
  }

  /**
   * Cleanup expired sessions
   */
  static async cleanupExpiredSessions(): Promise<void> {
    try {
      const now = new Date();
      
      await ProcessingSession.update(
        { status: 'expired' },
        {
          where: {
            expires_at: { [Op.lt]: now },
            status: 'active'
          }
        }
      );

      logger.info('Expired sessions cleaned up');
    } catch (error) {
      logger.error('Error cleaning up expired sessions', { error });
    }
  }
}

// Run cleanup every hour
setInterval(() => {
  SessionManager.cleanupExpiredSessions();
}, 60 * 60 * 1000);
