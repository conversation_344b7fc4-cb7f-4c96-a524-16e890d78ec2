import express from 'express';
import { Op } from 'sequelize';
import { asyncHand<PERSON> } from '../middleware/errorHandler';
import { authenticate, authorize, userRateLimit } from '../middleware/auth';
import { logger } from '../utils/logger';
import { UsageRecord } from '../models/UsageRecord';

const router = express.Router();

// Subscription plan limits
const PLAN_LIMITS = {
  free: {
    maxFilesPerDay: 10,
    maxFileSizeMB: 10,
    maxProcessingTimePerDay: 300, // 5 minutes
    allowedTools: ['repair_pdf', 'merge_pdf', 'compress_pdf', 'split_pdf']
  },
  premium: {
    maxFilesPerDay: 100,
    maxFileSizeMB: 50,
    maxProcessingTimePerDay: 3600, // 1 hour
    allowedTools: ['*'] // All tools
  },
  enterprise: {
    maxFilesPerDay: 1000,
    maxFileSizeMB: 100,
    maxProcessingTimePerDay: 36000, // 10 hours
    allowedTools: ['*'] // All tools
  }
};

// Get user usage statistics
router.get('/usage',
  authenticate,
  asyncHandler(async (req, res) => {
    const userId = req.user!.id;

    // Calculate today's usage
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    const todayUsage = await UsageRecord.findAll({
      where: {
        user_id: userId,
        created_at: {
          [Op.gte]: today
        }
      }
    });

    const totalFilesToday = todayUsage.length;
    const totalProcessingTimeToday = todayUsage.reduce(
      (sum, record) => sum + record.processing_time_seconds, 0
    );
    const totalFileSizeToday = todayUsage.reduce(
      (sum, record) => sum + Number(record.file_size_mb), 0
    );

    // Get plan limits
    const planLimits = PLAN_LIMITS[req.user!.subscriptionPlan as keyof typeof PLAN_LIMITS];

    // Calculate usage by tool
    const toolUsage = todayUsage.reduce((acc, record) => {
      acc[record.tool_name] = (acc[record.tool_name] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    res.json({
      success: true,
      data: {
        today: {
          files: totalFilesToday,
          processingTimeSeconds: totalProcessingTimeToday,
          fileSizeMB: Math.round(totalFileSizeToday * 100) / 100,
          toolUsage
        },
        limits: {
          maxFilesPerDay: planLimits.maxFilesPerDay,
          maxFileSizeMB: planLimits.maxFileSizeMB,
          maxProcessingTimePerDay: planLimits.maxProcessingTimePerDay
        },
        remaining: {
          files: Math.max(0, planLimits.maxFilesPerDay - totalFilesToday),
          processingTimeSeconds: Math.max(0, planLimits.maxProcessingTimePerDay - totalProcessingTimeToday)
        },
        subscriptionPlan: req.user!.subscriptionPlan
      }
    });
  })
);

// Get usage history
router.get('/usage/history',
  authenticate,
  asyncHandler(async (req, res) => {
    const userId = req.user!.id;
    const { days = 7, page = 1, limit = 50 } = req.query;

    // Filter by date range
    const daysAgo = new Date();
    daysAgo.setDate(daysAgo.getDate() - Number(days));

    const offset = (Number(page) - 1) * Number(limit);

    const { count, rows: usageHistory } = await UsageRecord.findAndCountAll({
      where: {
        user_id: userId,
        created_at: {
          [Op.gte]: daysAgo
        }
      },
      order: [['created_at', 'DESC']],
      limit: Number(limit),
      offset: offset
    });

    res.json({
      success: true,
      data: {
        usage: usageHistory.map(record => ({
          id: record.id,
          toolName: record.tool_name,
          fileSizeMB: Number(record.file_size_mb),
          processingTimeSeconds: record.processing_time_seconds,
          inputFileCount: record.input_file_count,
          outputFileCount: record.output_file_count,
          success: record.success,
          timestamp: record.created_at
        })),
        pagination: {
          page: Number(page),
          limit: Number(limit),
          total: count,
          pages: Math.ceil(count / Number(limit))
        }
      }
    });
  })
);

// Check if user can use a specific tool
router.get('/can-use/:toolName',
  authenticate,
  asyncHandler(async (req, res) => {
    const { toolName } = req.params;
    const userId = req.user!.id;
    const userPlan = req.user!.subscriptionPlan as keyof typeof PLAN_LIMITS;
    
    const planLimits = PLAN_LIMITS[userPlan];
    // Check if tool is allowed for this plan
    const toolAllowed = planLimits.allowedTools.includes('*') ||
                       planLimits.allowedTools.includes(toolName);

    if (!toolAllowed) {
      return res.json({
        success: true,
        data: {
          canUse: false,
          reason: 'tool_not_allowed',
          message: `Tool ${toolName} is not available for ${userPlan} plan`,
          upgradeRequired: true
        }
      });
    }

    // Calculate today's usage
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    const todayUsage = await UsageRecord.findAll({
      where: {
        user_id: userId,
        created_at: {
          [Op.gte]: today
        }
      }
    });

    const totalFilesToday = todayUsage.length;
    const totalProcessingTimeToday = todayUsage.reduce(
      (sum, record) => sum + record.processing_time_seconds, 0
    );

    // Check daily limits
    if (totalFilesToday >= planLimits.maxFilesPerDay) {
      return res.json({
        success: true,
        data: {
          canUse: false,
          reason: 'daily_file_limit',
          message: `Daily file limit of ${planLimits.maxFilesPerDay} reached`,
          upgradeRequired: userPlan === 'free'
        }
      });
    }

    if (totalProcessingTimeToday >= planLimits.maxProcessingTimePerDay) {
      return res.json({
        success: true,
        data: {
          canUse: false,
          reason: 'daily_processing_limit',
          message: `Daily processing time limit reached`,
          upgradeRequired: userPlan === 'free'
        }
      });
    }

    res.json({
      success: true,
      data: {
        canUse: true,
        remaining: {
          files: planLimits.maxFilesPerDay - totalFilesToday,
          processingTimeSeconds: planLimits.maxProcessingTimePerDay - totalProcessingTimeToday
        }
      }
    });
  })
);

// Record usage (called internally by PDF tools)
router.post('/usage/record',
  authenticate,
  asyncHandler(async (req, res) => {
    const {
      toolName,
      fileSizeMB,
      processingTimeSeconds,
      inputFileCount = 1,
      outputFileCount = 1,
      success = true,
      errorMessage,
      parameters
    } = req.body;
    const userId = req.user!.id;

    const usageRecord = await UsageRecord.create({
      user_id: userId,
      tool_name: toolName,
      file_size_mb: Number(fileSizeMB),
      processing_time_seconds: Number(processingTimeSeconds),
      input_file_count: Number(inputFileCount),
      output_file_count: Number(outputFileCount),
      success: Boolean(success),
      error_message: errorMessage,
      parameters: parameters
    });

    logger.info('Usage recorded', {
      userId,
      toolName,
      fileSizeMB,
      processingTimeSeconds,
      success
    });

    res.json({
      success: true,
      data: {
        recorded: true,
        usageId: usageRecord.id
      }
    });
  })
);

// Get subscription plans
router.get('/plans',
  asyncHandler(async (req, res) => {
    const plans = [
      {
        id: 'free',
        name: 'Free',
        price: 0,
        currency: 'USD',
        interval: 'month',
        features: [
          '10 files per day',
          'Basic PDF tools',
          '10MB file size limit',
          'Community support'
        ],
        limits: PLAN_LIMITS.free
      },
      {
        id: 'premium',
        name: 'Premium',
        price: 9.99,
        currency: 'USD',
        interval: 'month',
        features: [
          '100 files per day',
          'All PDF tools',
          '50MB file size limit',
          'Priority support',
          'Advanced OCR',
          'Batch processing'
        ],
        limits: PLAN_LIMITS.premium
      },
      {
        id: 'enterprise',
        name: 'Enterprise',
        price: 29.99,
        currency: 'USD',
        interval: 'month',
        features: [
          '1000 files per day',
          'All PDF tools',
          '100MB file size limit',
          'Dedicated support',
          'API access',
          'Custom integrations'
        ],
        limits: PLAN_LIMITS.enterprise
      }
    ];

    res.json({
      success: true,
      data: { plans }
    });
  })
);

export default router;
