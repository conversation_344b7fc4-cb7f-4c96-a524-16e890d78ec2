import { Model, Optional } from 'sequelize';
import { User } from './User';
export interface ProcessingSessionAttributes {
    id: string;
    user_id: string;
    session_token: string;
    status: 'active' | 'completed' | 'expired';
    files_processed: number;
    quota_consumed: number;
    can_download: boolean;
    expires_at: Date;
    created_at?: Date;
    updated_at?: Date;
}
export interface ProcessingSessionCreationAttributes extends Optional<ProcessingSessionAttributes, 'id' | 'status' | 'files_processed' | 'quota_consumed' | 'can_download' | 'created_at' | 'updated_at'> {
}
export declare class ProcessingSession extends Model<ProcessingSessionAttributes, ProcessingSessionCreationAttributes> implements ProcessingSessionAttributes {
    id: string;
    user_id: string;
    session_token: string;
    status: 'active' | 'completed' | 'expired';
    files_processed: number;
    quota_consumed: number;
    can_download: boolean;
    expires_at: Date;
    readonly created_at: Date;
    readonly updated_at: Date;
    readonly user?: User;
    isActive(): boolean;
    markCompleted(): void;
    incrementUsage(): void;
    canProcessMore(userQuota: number): boolean;
}
export default ProcessingSession;
//# sourceMappingURL=ProcessingSession.d.ts.map