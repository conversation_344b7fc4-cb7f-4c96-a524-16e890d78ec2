import React, { useState } from 'react';
import { FolderOpen, Download, ArrowRight } from 'lucide-react';
import ToolLayout from '../components/ToolLayout';
import FileUpload from '../components/FileUpload';

const OrganizePDF = () => {
  const [files, setFiles] = useState<File[]>([]);
  const [isProcessing, setIsProcessing] = useState(false);

  const handleFileSelect = (selectedFiles: File[]) => {
    setFiles(selectedFiles);
  };

  const handleOrganize = () => {
    if (files.length === 0) return;
    
    setIsProcessing(true);
    // Simulate processing
    setTimeout(() => {
      setIsProcessing(false);
      alert('Éditeur d\'organisation PDF ouvert avec succès!');
    }, 2000);
  };

  return (
    <ToolLayout
      title="Organiser PDF"
      description="Triez les pages de votre fichier PDF comme bon vous semble. Supprimez ou ajoutez des pages PDF à votre document"
      icon={<FolderOpen className="w-8 h-8" />}
      color="from-stone-500 to-stone-600"
    >
      <div className="space-y-6">
        <FileUpload
          onFileSelect={handleFileSelect}
          accept=".pdf"
          multiple={false}
          maxFiles={1}
          title="Sélectionnez votre fichier PDF"
          description="Glissez-déposez un fichier PDF ici ou cliquez pour sélectionner"
        />

        {files.length > 0 && (
          <div className="bg-slate-50 p-6 rounded-xl">
            <h3 className="text-lg font-semibold text-slate-700 mb-4">
              Outils d'organisation disponibles
            </h3>
            
            <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
              <div className="flex flex-col items-center p-4 bg-white rounded-lg hover:shadow-md transition-shadow">
                <div className="w-12 h-12 bg-stone-100 rounded-lg flex items-center justify-center mb-2">
                  <span className="text-stone-600 font-bold text-xl">📄</span>
                </div>
                <span className="text-sm text-slate-700">Réorganiser</span>
              </div>

              <div className="flex flex-col items-center p-4 bg-white rounded-lg hover:shadow-md transition-shadow">
                <div className="w-12 h-12 bg-stone-100 rounded-lg flex items-center justify-center mb-2">
                  <span className="text-stone-600 font-bold text-xl">🗑️</span>
                </div>
                <span className="text-sm text-slate-700">Supprimer</span>
              </div>

              <div className="flex flex-col items-center p-4 bg-white rounded-lg hover:shadow-md transition-shadow">
                <div className="w-12 h-12 bg-stone-100 rounded-lg flex items-center justify-center mb-2">
                  <span className="text-stone-600 font-bold text-xl">➕</span>
                </div>
                <span className="text-sm text-slate-700">Ajouter</span>
              </div>

              <div className="flex flex-col items-center p-4 bg-white rounded-lg hover:shadow-md transition-shadow">
                <div className="w-12 h-12 bg-stone-100 rounded-lg flex items-center justify-center mb-2">
                  <span className="text-stone-600 font-bold text-xl">🔄</span>
                </div>
                <span className="text-sm text-slate-700">Dupliquer</span>
              </div>

              <div className="flex flex-col items-center p-4 bg-white rounded-lg hover:shadow-md transition-shadow">
                <div className="w-12 h-12 bg-stone-100 rounded-lg flex items-center justify-center mb-2">
                  <span className="text-stone-600 font-bold text-xl">✂️</span>
                </div>
                <span className="text-sm text-slate-700">Extraire</span>
              </div>

              <div className="flex flex-col items-center p-4 bg-white rounded-lg hover:shadow-md transition-shadow">
                <div className="w-12 h-12 bg-stone-100 rounded-lg flex items-center justify-center mb-2">
                  <span className="text-stone-600 font-bold text-xl">🔃</span>
                </div>
                <span className="text-sm text-slate-700">Remplacer</span>
              </div>
            </div>

            <div className="mt-6 bg-blue-50 p-4 rounded-lg">
              <div className="flex items-center space-x-2">
                <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                <span className="text-sm text-blue-800 font-medium">Fonctionnalités</span>
              </div>
              <ul className="text-sm text-blue-700 mt-1 space-y-1">
                <li>• Glisser-déposer pour réorganiser les pages</li>
                <li>• Aperçu en temps réel des modifications</li>
                <li>• Suppression et ajout de pages faciles</li>
                <li>• Sauvegarde automatique des modifications</li>
              </ul>
            </div>
          </div>
        )}

        {files.length > 0 && (
          <div className="flex justify-center">
            <button
              onClick={handleOrganize}
              disabled={isProcessing}
              className="bg-gradient-to-r from-stone-600 to-slate-600 text-white px-8 py-4 rounded-xl text-lg font-medium hover:shadow-xl transition-all duration-200 transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2"
            >
              {isProcessing ? (
                <>
                  <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white"></div>
                  <span>Ouverture de l'éditeur...</span>
                </>
              ) : (
                <>
                  <span>Ouvrir l'éditeur d'organisation</span>
                  <ArrowRight className="w-5 h-5" />
                </>
              )}
            </button>
          </div>
        )}
      </div>
    </ToolLayout>
  );
};

export default OrganizePDF;