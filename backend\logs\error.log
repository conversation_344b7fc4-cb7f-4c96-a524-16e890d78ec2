{"database":"pdf","error":"Access denied for user 'root'@'localhost' (using password: NO)","host":"localhost","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mUnable to connect to database\u001b[39m","port":3306,"timestamp":"2025-07-12 14:06:49:649"}
{"database":"pdf","error":"Access denied for user 'root'@'localhost' (using password: NO)","host":"localhost","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mUnable to connect to database\u001b[39m","port":3306,"timestamp":"2025-07-12 14:07:29:729"}
{"database":"pdf","error":"Access denied for user 'root'@'localhost' (using password: NO)","host":"localhost","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mUnable to connect to database\u001b[39m","port":3306,"timestamp":"2025-07-12 14:08:00:80"}
{"error":"User with this email already exists","ip":"::1","isOperational":true,"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRequest error\u001b[39m","method":"POST","stack":"ValidationError: User with this email already exists\n    at G:\\PDF Porject\\backend\\src\\routes\\auth.ts:24:13\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","statusCode":400,"timestamp":"2025-07-12 14:25:49:2549","url":"/api/auth/register","userAgent":"axios/1.10.0"}
{"error":"\"parameters\" must be of type object","ip":"::1","isOperational":true,"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRequest error\u001b[39m","method":"POST","stack":"ValidationError: \"parameters\" must be of type object\n    at G:\\PDF Porject\\backend\\src\\middleware\\validation.ts:117:13\n    at Layer.handle [as handle_request] (G:\\PDF Porject\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (G:\\PDF Porject\\backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at validateFiles (G:\\PDF Porject\\backend\\src\\middleware\\validation.ts:151:3)\n    at Layer.handle [as handle_request] (G:\\PDF Porject\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (G:\\PDF Porject\\backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at done (G:\\PDF Porject\\backend\\node_modules\\multer\\lib\\make-middleware.js:47:7)\n    at indicateDone (G:\\PDF Porject\\backend\\node_modules\\multer\\lib\\make-middleware.js:51:68)\n    at Multipart.<anonymous> (G:\\PDF Porject\\backend\\node_modules\\multer\\lib\\make-middleware.js:168:7)\n    at Multipart.emit (node:events:518:28)","statusCode":400,"timestamp":"2025-07-12 16:22:23:2223","url":"/api/pdf-tools/process","userAgent":"axios/1.10.0"}
{"error":"\"parameters\" must be of type object","ip":"::1","isOperational":true,"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRequest error\u001b[39m","method":"POST","stack":"ValidationError: \"parameters\" must be of type object\n    at G:\\PDF Porject\\backend\\src\\middleware\\validation.ts:117:13\n    at Layer.handle [as handle_request] (G:\\PDF Porject\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (G:\\PDF Porject\\backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at validateFiles (G:\\PDF Porject\\backend\\src\\middleware\\validation.ts:151:3)\n    at Layer.handle [as handle_request] (G:\\PDF Porject\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (G:\\PDF Porject\\backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at done (G:\\PDF Porject\\backend\\node_modules\\multer\\lib\\make-middleware.js:47:7)\n    at indicateDone (G:\\PDF Porject\\backend\\node_modules\\multer\\lib\\make-middleware.js:51:68)\n    at Multipart.<anonymous> (G:\\PDF Porject\\backend\\node_modules\\multer\\lib\\make-middleware.js:168:7)\n    at Multipart.emit (node:events:518:28)","statusCode":400,"timestamp":"2025-07-12 16:25:55:2555","url":"/api/pdf-tools/process","userAgent":"axios/1.10.0"}
{"error":"\"parameters\" must be of type object","ip":"::1","isOperational":true,"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRequest error\u001b[39m","method":"POST","stack":"ValidationError: \"parameters\" must be of type object\n    at G:\\PDF Porject\\backend\\src\\middleware\\validation.ts:117:13\n    at Layer.handle [as handle_request] (G:\\PDF Porject\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (G:\\PDF Porject\\backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at validateFiles (G:\\PDF Porject\\backend\\src\\middleware\\validation.ts:151:3)\n    at Layer.handle [as handle_request] (G:\\PDF Porject\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (G:\\PDF Porject\\backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at done (G:\\PDF Porject\\backend\\node_modules\\multer\\lib\\make-middleware.js:47:7)\n    at indicateDone (G:\\PDF Porject\\backend\\node_modules\\multer\\lib\\make-middleware.js:51:68)\n    at Multipart.<anonymous> (G:\\PDF Porject\\backend\\node_modules\\multer\\lib\\make-middleware.js:168:7)\n    at Multipart.emit (node:events:518:28)","statusCode":400,"timestamp":"2025-07-12 16:36:23:3623","url":"/api/pdf-tools/process","userAgent":"axios/1.10.0"}
{"error":"\"password\" length must be at least 8 characters long, Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character","ip":"::1","isOperational":true,"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRequest error\u001b[39m","method":"POST","stack":"ValidationError: \"password\" length must be at least 8 characters long, Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character\n    at G:\\PDF Porject\\backend\\src\\middleware\\validation.ts:117:13\n    at Layer.handle [as handle_request] (G:\\PDF Porject\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (G:\\PDF Porject\\backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (G:\\PDF Porject\\backend\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (G:\\PDF Porject\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at G:\\PDF Porject\\backend\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (G:\\PDF Porject\\backend\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (G:\\PDF Porject\\backend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (G:\\PDF Porject\\backend\\node_modules\\express\\lib\\router\\index.js:175:3)\n    at router (G:\\PDF Porject\\backend\\node_modules\\express\\lib\\router\\index.js:47:12)","statusCode":400,"timestamp":"2025-07-12 16:37:21:3721","url":"/api/auth/register","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"error":"Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character","ip":"::1","isOperational":true,"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRequest error\u001b[39m","method":"POST","stack":"ValidationError: Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character\n    at G:\\PDF Porject\\backend\\src\\middleware\\validation.ts:117:13\n    at Layer.handle [as handle_request] (G:\\PDF Porject\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (G:\\PDF Porject\\backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (G:\\PDF Porject\\backend\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (G:\\PDF Porject\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at G:\\PDF Porject\\backend\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (G:\\PDF Porject\\backend\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (G:\\PDF Porject\\backend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (G:\\PDF Porject\\backend\\node_modules\\express\\lib\\router\\index.js:175:3)\n    at router (G:\\PDF Porject\\backend\\node_modules\\express\\lib\\router\\index.js:47:12)","statusCode":400,"timestamp":"2025-07-12 16:37:34:3734","url":"/api/auth/register","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"error":"Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character","ip":"::1","isOperational":true,"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRequest error\u001b[39m","method":"POST","stack":"ValidationError: Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character\n    at G:\\PDF Porject\\backend\\src\\middleware\\validation.ts:117:13\n    at Layer.handle [as handle_request] (G:\\PDF Porject\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (G:\\PDF Porject\\backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (G:\\PDF Porject\\backend\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (G:\\PDF Porject\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at G:\\PDF Porject\\backend\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (G:\\PDF Porject\\backend\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (G:\\PDF Porject\\backend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (G:\\PDF Porject\\backend\\node_modules\\express\\lib\\router\\index.js:175:3)\n    at router (G:\\PDF Porject\\backend\\node_modules\\express\\lib\\router\\index.js:47:12)","statusCode":400,"timestamp":"2025-07-12 16:37:48:3748","url":"/api/auth/register","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"error":"Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character","ip":"::1","isOperational":true,"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRequest error\u001b[39m","method":"POST","stack":"ValidationError: Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character\n    at G:\\PDF Porject\\backend\\src\\middleware\\validation.ts:117:13\n    at Layer.handle [as handle_request] (G:\\PDF Porject\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (G:\\PDF Porject\\backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (G:\\PDF Porject\\backend\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (G:\\PDF Porject\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at G:\\PDF Porject\\backend\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (G:\\PDF Porject\\backend\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (G:\\PDF Porject\\backend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (G:\\PDF Porject\\backend\\node_modules\\express\\lib\\router\\index.js:175:3)\n    at router (G:\\PDF Porject\\backend\\node_modules\\express\\lib\\router\\index.js:47:12)","statusCode":400,"timestamp":"2025-07-12 16:37:58:3758","url":"/api/auth/register","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"error":"Authorization header is required","ip":"::1","isOperational":true,"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRequest error\u001b[39m","method":"POST","stack":"AuthenticationError: Authorization header is required\n    at Function.extractTokenFromHeader (G:\\PDF Porject\\backend\\src\\middleware\\auth.ts:67:13)\n    at authenticate (G:\\PDF Porject\\backend\\src\\middleware\\auth.ts:82:31)\n    at Layer.handle [as handle_request] (G:\\PDF Porject\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (G:\\PDF Porject\\backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (G:\\PDF Porject\\backend\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (G:\\PDF Porject\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at G:\\PDF Porject\\backend\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (G:\\PDF Porject\\backend\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (G:\\PDF Porject\\backend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (G:\\PDF Porject\\backend\\node_modules\\express\\lib\\router\\index.js:175:3)","statusCode":401,"timestamp":"2025-07-12 16:46:17:4617","url":"/api/pdf-tools/check-quota","userAgent":"axios/1.10.0"}
{"error":"Invalid email or password","ip":"::1","isOperational":true,"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRequest error\u001b[39m","method":"POST","stack":"AuthenticationError: Invalid email or password\n    at G:\\PDF Porject\\backend\\src\\routes\\auth.ts:87:13\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","statusCode":401,"timestamp":"2025-07-12 16:46:17:4617","url":"/api/auth/login","userAgent":"axios/1.10.0"}
{"error":"\"parameters\" must be of type object","ip":"::1","isOperational":true,"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRequest error\u001b[39m","method":"POST","stack":"ValidationError: \"parameters\" must be of type object\n    at G:\\PDF Porject\\backend\\src\\middleware\\validation.ts:117:13\n    at Layer.handle [as handle_request] (G:\\PDF Porject\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (G:\\PDF Porject\\backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at validateFiles (G:\\PDF Porject\\backend\\src\\middleware\\validation.ts:151:3)\n    at Layer.handle [as handle_request] (G:\\PDF Porject\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (G:\\PDF Porject\\backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at done (G:\\PDF Porject\\backend\\node_modules\\multer\\lib\\make-middleware.js:47:7)\n    at indicateDone (G:\\PDF Porject\\backend\\node_modules\\multer\\lib\\make-middleware.js:51:68)\n    at Multipart.<anonymous> (G:\\PDF Porject\\backend\\node_modules\\multer\\lib\\make-middleware.js:168:7)\n    at Multipart.emit (node:events:518:28)","statusCode":400,"timestamp":"2025-07-12 16:50:29:5029","url":"/api/pdf-tools/process","userAgent":"axios/1.10.0"}
{"error":"Invalid email or password","ip":"::1","isOperational":true,"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRequest error\u001b[39m","method":"POST","stack":"AuthenticationError: Invalid email or password\n    at G:\\PDF Porject\\backend\\src\\routes\\auth.ts:87:13\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","statusCode":401,"timestamp":"2025-07-12 16:50:29:5029","url":"/api/auth/login","userAgent":"axios/1.10.0"}
{"error":"Python script failed with code 1: Traceback (most recent call last):\r\n  File \"G:\\PDF Porject\\tools\\repair_pdf_wrapper.py\", line 15, in <module>\r\n    from repair_pdf import repair_pdf_tool\r\n  File \"G:\\PDF Porject\\tools\\repair_pdf.py\", line 11, in <module>\r\n    from .base_tool import BasePDFTool, ProcessingError, ValidationError\r\nImportError: attempted relative import with no known parent package\r\n","jobId":"7716d455-06c9-42cd-ad93-8d49d4baf0ec","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mPython tool execution failed\u001b[39m","processingTime":1203,"timestamp":"2025-07-12 16:52:11:5211","toolName":"repair_pdf"}
{"error":"Invalid email or password","ip":"::1","isOperational":true,"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRequest error\u001b[39m","method":"POST","stack":"AuthenticationError: Invalid email or password\n    at G:\\PDF Porject\\backend\\src\\routes\\auth.ts:87:13\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","statusCode":401,"timestamp":"2025-07-12 16:52:11:5211","url":"/api/auth/login","userAgent":"axios/1.10.0"}
{"error":"Authorization header is required","ip":"::1","isOperational":true,"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRequest error\u001b[39m","method":"POST","stack":"AuthenticationError: Authorization header is required\n    at Function.extractTokenFromHeader (G:\\PDF Porject\\backend\\src\\middleware\\auth.ts:67:13)\n    at authenticate (G:\\PDF Porject\\backend\\src\\middleware\\auth.ts:82:31)\n    at Layer.handle [as handle_request] (G:\\PDF Porject\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (G:\\PDF Porject\\backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (G:\\PDF Porject\\backend\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (G:\\PDF Porject\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at G:\\PDF Porject\\backend\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (G:\\PDF Porject\\backend\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (G:\\PDF Porject\\backend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (G:\\PDF Porject\\backend\\node_modules\\express\\lib\\router\\index.js:175:3)","statusCode":401,"timestamp":"2025-07-12 16:52:53:5253","url":"/api/pdf-tools/complete-session","userAgent":"axios/1.10.0"}
{"error":"Can't DROP 'processing_sessions_ibfk_1'; check that column/key exists","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mFailed to initialize database\u001b[39m","timestamp":"2025-07-12 16:53:19:5319"}
{"error":"Constraint processing_sessions_ibfk_1 on table processing_sessions does not exist","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mFailed to initialize database\u001b[39m","timestamp":"2025-07-12 16:54:00:540"}
{"error":"Python script failed with code 2: python: can't open file 'G:\\\\PDF Porject\\\\tools\\\\compress_pdf_wrapper.py': [Errno 2] No such file or directory\r\n","jobId":"367b00b4-a0ea-4651-86fa-2e852f87d17e","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mPython tool execution failed\u001b[39m","processingTime":80,"timestamp":"2025-07-12 19:56:08:568","toolName":"compress_pdf"}
{"error":"Python script failed with code 2: python: can't open file 'G:\\\\PDF Porject\\\\tools\\\\compress_pdf_wrapper.py': [Errno 2] No such file or directory\r\n","jobId":"9e287f83-b5a7-45d5-a580-5bcc6de992e9","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mPython tool execution failed\u001b[39m","processingTime":79,"timestamp":"2025-07-12 19:56:10:5610","toolName":"compress_pdf"}
{"error":"Python script failed with code 2: python: can't open file 'G:\\\\PDF Porject\\\\tools\\\\compress_pdf_wrapper.py': [Errno 2] No such file or directory\r\n","jobId":"8e877cf6-d312-4424-af64-710859a6e6d2","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mPython tool execution failed\u001b[39m","processingTime":87,"timestamp":"2025-07-12 19:56:14:5614","toolName":"compress_pdf"}
{"error":"Python script failed with code 2: python: can't open file 'G:\\\\PDF Porject\\\\tools\\\\compress_pdf_wrapper.py': [Errno 2] No such file or directory\r\n","jobId":"8afcc312-8f4a-4502-96f8-5a24d568be9f","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mPython tool execution failed\u001b[39m","processingTime":82,"timestamp":"2025-07-12 19:56:18:5618","toolName":"compress_pdf"}
{"error":"Python script failed with code 2: python: can't open file 'G:\\\\PDF Porject\\\\tools\\\\compress_pdf_wrapper.py': [Errno 2] No such file or directory\r\n","jobId":"24e23b47-9d43-43a5-bd09-33239c4d4b7f","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mPython tool execution failed\u001b[39m","processingTime":1393,"timestamp":"2025-07-12 19:58:12:5812","toolName":"compress_pdf"}
{"error":"Python script failed with code 2: python: can't open file 'G:\\\\PDF Porject\\\\tools\\\\compress_pdf_wrapper.py': [Errno 2] No such file or directory\r\n","jobId":"771bcd6b-c4f0-467e-8d29-5554116c1a49","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mPython tool execution failed\u001b[39m","processingTime":96,"timestamp":"2025-07-12 19:58:35:5835","toolName":"compress_pdf"}
{"error":"Python script failed with code 1: ","jobId":"5719faef-401f-4601-b1d7-302559c73d37","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mPython tool execution failed\u001b[39m","processingTime":461,"timestamp":"2025-07-12 19:59:31:5931","toolName":"compress_pdf"}
{"error":"Python script failed with code 1: ","jobId":"e0e69ef0-1f4d-4ba1-8e70-749dbf0889d4","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mPython tool execution failed\u001b[39m","processingTime":97,"timestamp":"2025-07-12 20:00:43:043","toolName":"compress_pdf"}
{"error":"Python script failed with code 2: python: can't open file 'G:\\\\PDF Porject\\\\tools\\\\compress_pdf_wrapper_wrapper.py': [Errno 2] No such file or directory\r\n","jobId":"5b81a4a4-6be0-48ae-ac37-0c1cdb78dc07","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mPython tool execution failed\u001b[39m","processingTime":79,"timestamp":"2025-07-12 20:01:43:143","toolName":"compress_pdf"}
