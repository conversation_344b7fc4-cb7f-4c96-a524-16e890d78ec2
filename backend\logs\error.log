{"database":"pdf","error":"Access denied for user 'root'@'localhost' (using password: NO)","host":"localhost","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mUnable to connect to database\u001b[39m","port":3306,"timestamp":"2025-07-12 14:06:49:649"}
{"database":"pdf","error":"Access denied for user 'root'@'localhost' (using password: NO)","host":"localhost","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mUnable to connect to database\u001b[39m","port":3306,"timestamp":"2025-07-12 14:07:29:729"}
{"database":"pdf","error":"Access denied for user 'root'@'localhost' (using password: NO)","host":"localhost","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mUnable to connect to database\u001b[39m","port":3306,"timestamp":"2025-07-12 14:08:00:80"}
{"error":"User with this email already exists","ip":"::1","isOperational":true,"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRequest error\u001b[39m","method":"POST","stack":"ValidationError: User with this email already exists\n    at G:\\PDF Porject\\backend\\src\\routes\\auth.ts:24:13\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","statusCode":400,"timestamp":"2025-07-12 14:25:49:2549","url":"/api/auth/register","userAgent":"axios/1.10.0"}
{"error":"\"parameters\" must be of type object","ip":"::1","isOperational":true,"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRequest error\u001b[39m","method":"POST","stack":"ValidationError: \"parameters\" must be of type object\n    at G:\\PDF Porject\\backend\\src\\middleware\\validation.ts:117:13\n    at Layer.handle [as handle_request] (G:\\PDF Porject\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (G:\\PDF Porject\\backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at validateFiles (G:\\PDF Porject\\backend\\src\\middleware\\validation.ts:151:3)\n    at Layer.handle [as handle_request] (G:\\PDF Porject\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (G:\\PDF Porject\\backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at done (G:\\PDF Porject\\backend\\node_modules\\multer\\lib\\make-middleware.js:47:7)\n    at indicateDone (G:\\PDF Porject\\backend\\node_modules\\multer\\lib\\make-middleware.js:51:68)\n    at Multipart.<anonymous> (G:\\PDF Porject\\backend\\node_modules\\multer\\lib\\make-middleware.js:168:7)\n    at Multipart.emit (node:events:518:28)","statusCode":400,"timestamp":"2025-07-12 16:22:23:2223","url":"/api/pdf-tools/process","userAgent":"axios/1.10.0"}
{"error":"\"parameters\" must be of type object","ip":"::1","isOperational":true,"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRequest error\u001b[39m","method":"POST","stack":"ValidationError: \"parameters\" must be of type object\n    at G:\\PDF Porject\\backend\\src\\middleware\\validation.ts:117:13\n    at Layer.handle [as handle_request] (G:\\PDF Porject\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (G:\\PDF Porject\\backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at validateFiles (G:\\PDF Porject\\backend\\src\\middleware\\validation.ts:151:3)\n    at Layer.handle [as handle_request] (G:\\PDF Porject\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (G:\\PDF Porject\\backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at done (G:\\PDF Porject\\backend\\node_modules\\multer\\lib\\make-middleware.js:47:7)\n    at indicateDone (G:\\PDF Porject\\backend\\node_modules\\multer\\lib\\make-middleware.js:51:68)\n    at Multipart.<anonymous> (G:\\PDF Porject\\backend\\node_modules\\multer\\lib\\make-middleware.js:168:7)\n    at Multipart.emit (node:events:518:28)","statusCode":400,"timestamp":"2025-07-12 16:25:55:2555","url":"/api/pdf-tools/process","userAgent":"axios/1.10.0"}
