"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.SessionManager = void 0;
const uuid_1 = require("uuid");
const sequelize_1 = require("sequelize");
const ProcessingSession_1 = require("../models/ProcessingSession");
const UsageRecord_1 = require("../models/UsageRecord");
const logger_1 = require("../utils/logger");
const PLAN_LIMITS = {
    free: {
        maxFilesPerDay: 10,
        maxFileSizeMB: 10,
        maxProcessingTimePerDay: 300,
        allowedTools: ['repair_pdf', 'merge_pdf', 'compress_pdf', 'split_pdf']
    },
    premium: {
        maxFilesPerDay: 100,
        maxFileSizeMB: 50,
        maxProcessingTimePerDay: 3600,
        allowedTools: ['*']
    },
    enterprise: {
        maxFilesPerDay: 1000,
        maxFileSizeMB: 100,
        maxProcessingTimePerDay: 36000,
        allowedTools: ['*']
    }
};
class SessionManager {
    static async canStartNewSession(userId, subscriptionPlan, toolName) {
        try {
            const planLimits = PLAN_LIMITS[subscriptionPlan];
            const toolAllowed = planLimits.allowedTools.includes('*') ||
                planLimits.allowedTools.includes(toolName);
            if (!toolAllowed) {
                return {
                    canProcess: false,
                    reason: 'tool_not_allowed',
                    message: `Tool ${toolName} is not available for ${subscriptionPlan} plan`,
                    upgradeRequired: true
                };
            }
            const today = new Date();
            today.setHours(0, 0, 0, 0);
            const todayUsage = await UsageRecord_1.UsageRecord.findAll({
                where: {
                    user_id: userId,
                    created_at: {
                        [sequelize_1.Op.gte]: today
                    }
                }
            });
            const totalFilesToday = todayUsage.length;
            const totalProcessingTimeToday = todayUsage.reduce((sum, record) => sum + record.processing_time_seconds, 0);
            if (totalFilesToday >= planLimits.maxFilesPerDay) {
                return {
                    canProcess: false,
                    reason: 'daily_file_limit',
                    message: `Daily file limit of ${planLimits.maxFilesPerDay} reached`,
                    upgradeRequired: subscriptionPlan === 'free'
                };
            }
            if (totalProcessingTimeToday >= planLimits.maxProcessingTimePerDay) {
                return {
                    canProcess: false,
                    reason: 'daily_processing_limit',
                    message: `Daily processing time limit reached`,
                    upgradeRequired: subscriptionPlan === 'free'
                };
            }
            const sessionToken = (0, uuid_1.v4)();
            const expiresAt = new Date();
            expiresAt.setHours(expiresAt.getHours() + 2);
            await ProcessingSession_1.ProcessingSession.create({
                user_id: userId,
                session_token: sessionToken,
                expires_at: expiresAt
            });
            return {
                canProcess: true,
                sessionToken,
                remainingQuota: {
                    files: planLimits.maxFilesPerDay - totalFilesToday,
                    processingTimeSeconds: planLimits.maxProcessingTimePerDay - totalProcessingTimeToday
                }
            };
        }
        catch (error) {
            logger_1.logger.error('Error checking session quota', { userId, error });
            return {
                canProcess: false,
                reason: 'system_error',
                message: 'Unable to check quota at this time'
            };
        }
    }
    static async canContinueSession(sessionToken, userId) {
        try {
            const session = await ProcessingSession_1.ProcessingSession.findOne({
                where: {
                    session_token: sessionToken,
                    user_id: userId
                }
            });
            if (!session) {
                return {
                    canProcess: false,
                    reason: 'session_not_found',
                    message: 'Processing session not found'
                };
            }
            if (!session.isActive()) {
                return {
                    canProcess: false,
                    reason: 'session_expired',
                    message: 'Processing session has expired'
                };
            }
            return {
                canProcess: true,
                sessionToken
            };
        }
        catch (error) {
            logger_1.logger.error('Error checking session continuation', { sessionToken, userId, error });
            return {
                canProcess: false,
                reason: 'system_error',
                message: 'Unable to verify session'
            };
        }
    }
    static async recordProcessing(sessionToken, userId, toolName, fileSizeMB, processingTimeSeconds, inputFileCount, outputFileCount, success, errorMessage) {
        try {
            const session = await ProcessingSession_1.ProcessingSession.findOne({
                where: {
                    session_token: sessionToken,
                    user_id: userId
                }
            });
            if (session && session.isActive()) {
                session.incrementUsage();
                await session.save();
                await UsageRecord_1.UsageRecord.create({
                    user_id: userId,
                    tool_name: toolName,
                    file_size_mb: fileSizeMB,
                    processing_time_seconds: processingTimeSeconds,
                    input_file_count: inputFileCount,
                    output_file_count: outputFileCount,
                    success,
                    error_message: errorMessage
                });
                logger_1.logger.info('Processing recorded in session', {
                    sessionToken,
                    userId,
                    toolName,
                    success
                });
            }
        }
        catch (error) {
            logger_1.logger.error('Error recording processing in session', {
                sessionToken,
                userId,
                error
            });
        }
    }
    static async completeSession(sessionToken, userId) {
        try {
            const session = await ProcessingSession_1.ProcessingSession.findOne({
                where: {
                    session_token: sessionToken,
                    user_id: userId
                }
            });
            if (session) {
                session.markCompleted();
                await session.save();
                logger_1.logger.info('Session completed', { sessionToken, userId });
            }
        }
        catch (error) {
            logger_1.logger.error('Error completing session', { sessionToken, userId, error });
        }
    }
    static async canDownload(sessionToken, userId) {
        try {
            const session = await ProcessingSession_1.ProcessingSession.findOne({
                where: {
                    session_token: sessionToken,
                    user_id: userId
                }
            });
            return session ? session.can_download : false;
        }
        catch (error) {
            logger_1.logger.error('Error checking download permission', { sessionToken, userId, error });
            return false;
        }
    }
    static async cleanupExpiredSessions() {
        try {
            const now = new Date();
            await ProcessingSession_1.ProcessingSession.update({ status: 'expired' }, {
                where: {
                    expires_at: { [sequelize_1.Op.lt]: now },
                    status: 'active'
                }
            });
            logger_1.logger.info('Expired sessions cleaned up');
        }
        catch (error) {
            logger_1.logger.error('Error cleaning up expired sessions', { error });
        }
    }
}
exports.SessionManager = SessionManager;
setInterval(() => {
    SessionManager.cleanupExpiredSessions();
}, 60 * 60 * 1000);
//# sourceMappingURL=sessionManager.js.map