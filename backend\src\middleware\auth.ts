import jwt, { SignOptions } from 'jsonwebtoken';
import { Request, Response, NextFunction } from 'express';
import { AuthenticationError, AuthorizationError } from './errorHandler';

// Extend Request interface to include user
declare global {
  namespace Express {
    interface Request {
      user?: {
        id: string;
        email: string;
        subscriptionPlan: string;
        isVerified: boolean;
      };
    }
  }
}

export interface JWTPayload {
  userId: string;
  email: string;
  subscriptionPlan: string;
  isVerified: boolean;
  iat?: number;
  exp?: number;
}

export class AuthService {
  private static readonly JWT_SECRET = process.env['JWT_SECRET'] || 'your-super-secret-jwt-key';
  private static readonly JWT_EXPIRES_IN = process.env['JWT_EXPIRES_IN'] || '7d';
  private static readonly REFRESH_TOKEN_EXPIRES_IN = process.env['REFRESH_TOKEN_EXPIRES_IN'] || '30d';

  static generateTokens(payload: Omit<JWTPayload, 'iat' | 'exp'>): { accessToken: string; refreshToken: string } {
    // @ts-ignore - JWT typing issue
    const accessToken = jwt.sign(
      payload,
      this.JWT_SECRET,
      { expiresIn: this.JWT_EXPIRES_IN }
    );

    // @ts-ignore - JWT typing issue
    const refreshToken = jwt.sign(
      { userId: payload.userId },
      this.JWT_SECRET,
      { expiresIn: this.REFRESH_TOKEN_EXPIRES_IN }
    );

    return { accessToken, refreshToken };
  }

  static verifyToken(token: string): JWTPayload {
    try {
      return jwt.verify(token, this.JWT_SECRET) as JWTPayload;
    } catch (error) {
      if (error instanceof jwt.TokenExpiredError) {
        throw new AuthenticationError('Token has expired');
      } else if (error instanceof jwt.JsonWebTokenError) {
        throw new AuthenticationError('Invalid token');
      } else {
        throw new AuthenticationError('Token verification failed');
      }
    }
  }

  static extractTokenFromHeader(authHeader?: string): string {
    if (!authHeader) {
      throw new AuthenticationError('Authorization header is required');
    }

    const parts = authHeader.split(' ');
    if (parts.length !== 2 || parts[0] !== 'Bearer') {
      throw new AuthenticationError('Authorization header must be in format: Bearer <token>');
    }

    return parts[1]!;
  }
}

// Authentication middleware
export const authenticate = (req: Request, _res: Response, next: NextFunction) => {
  try {
    const token = AuthService.extractTokenFromHeader(req.headers.authorization);
    const payload = AuthService.verifyToken(token);

    req.user = {
      id: payload.userId,
      email: payload.email,
      subscriptionPlan: payload.subscriptionPlan,
      isVerified: payload.isVerified
    };

    next();
  } catch (error) {
    next(error);
  }
};

// Optional authentication middleware (doesn't throw if no token)
export const optionalAuth = (req: Request, _res: Response, next: NextFunction) => {
  try {
    const authHeader = req.headers.authorization;
    if (authHeader) {
      const token = AuthService.extractTokenFromHeader(authHeader);
      const payload = AuthService.verifyToken(token);

      req.user = {
        id: payload.userId,
        email: payload.email,
        subscriptionPlan: payload.subscriptionPlan,
        isVerified: payload.isVerified
      };
    }
    next();
  } catch (error) {
    // Ignore authentication errors for optional auth
    next();
  }
};

// Authorization middleware
export const authorize = (requiredPlans: string[] = ['free', 'premium', 'enterprise']) => {
  return (req: Request, _res: Response, next: NextFunction) => {
    if (!req.user) {
      throw new AuthenticationError('Authentication required');
    }

    if (!requiredPlans.includes(req.user.subscriptionPlan)) {
      throw new AuthorizationError(`This feature requires one of the following plans: ${requiredPlans.join(', ')}`);
    }

    next();
  };
};

// Email verification middleware
export const requireVerification = (req: Request, _res: Response, next: NextFunction) => {
  if (!req.user) {
    throw new AuthenticationError('Authentication required');
  }

  if (!req.user.isVerified) {
    throw new AuthorizationError('Email verification required');
  }

  next();
};

// Rate limiting by user
export const userRateLimit = (maxRequests: number, windowMs: number) => {
  const userRequests = new Map<string, { count: number; resetTime: number }>();

  return (req: Request, res: Response, next: NextFunction) => {
    const userId = req.user?.id || req.ip || 'anonymous';
    const now = Date.now();

    const userLimit = userRequests.get(userId);
    
    if (!userLimit || now > userLimit.resetTime) {
      // Reset or initialize user limit
      userRequests.set(userId, {
        count: 1,
        resetTime: now + windowMs
      });
      next();
    } else if (userLimit.count < maxRequests) {
      // Increment count
      userLimit.count++;
      next();
    } else {
      // Rate limit exceeded
      const resetIn = Math.ceil((userLimit.resetTime - now) / 1000);
      res.status(429).json({
        success: false,
        error: {
          message: `Rate limit exceeded. Try again in ${resetIn} seconds.`,
          type: 'RateLimitError'
        },
        retryAfter: resetIn
      });
    }
  };
};
