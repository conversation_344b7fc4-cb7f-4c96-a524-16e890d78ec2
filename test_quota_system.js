// Test script for graceful quota enforcement system
const axios = require('axios');
const FormData = require('form-data');
const fs = require('fs');

const BASE_URL = 'http://localhost:3001/api';

async function testGracefulQuotaSystem() {
  console.log('🧪 Testing Graceful Quota Enforcement System...\n');

  try {
    // Step 1: Login to get authentication token
    console.log('1. Logging in...');
    const loginResponse = await axios.post(`${BASE_URL}/auth/login`, {
      email: '<EMAIL>',
      password: 'TestPassword123!'
    });

    const authToken = loginResponse.data.data.tokens.accessToken;
    const authHeaders = { Authorization: `Bearer ${authToken}` };

    console.log('✅ Login successful\n');

    // Step 2: Check quota for a tool
    console.log('2. Checking quota for repair_pdf tool...');
    const quotaResponse = await axios.post(`${BASE_URL}/pdf-tools/check-quota`, {
      toolName: 'repair_pdf'
    }, { headers: authHeaders });

    if (quotaResponse.data.success) {
      const sessionToken = quotaResponse.data.data.sessionToken;
      const remainingQuota = quotaResponse.data.data.remainingQuota;
      
      console.log('✅ Quota check passed');
      console.log(`   Session Token: ${sessionToken.substring(0, 8)}...`);
      console.log(`   Remaining Files: ${remainingQuota.files}`);
      console.log(`   Remaining Processing Time: ${remainingQuota.processingTimeSeconds}s\n`);

      // Step 3: Create a dummy PDF file for testing
      console.log('3. Creating test PDF file...');
      const testPdfContent = Buffer.from('%PDF-1.4\n1 0 obj\n<<\n/Type /Catalog\n/Pages 2 0 R\n>>\nendobj\n2 0 obj\n<<\n/Type /Pages\n/Kids [3 0 R]\n/Count 1\n>>\nendobj\n3 0 obj\n<<\n/Type /Page\n/Parent 2 0 R\n/MediaBox [0 0 612 792]\n>>\nendobj\nxref\n0 4\n0000000000 65535 f \n0000000009 00000 n \n0000000074 00000 n \n0000000120 00000 n \ntrailer\n<<\n/Size 4\n/Root 1 0 R\n>>\nstartxref\n179\n%%EOF');
      
      console.log('✅ Test PDF created\n');

      // Step 4: Process file with session token
      console.log('4. Processing PDF with session token...');
      const formData = new FormData();
      formData.append('files', testPdfContent, {
        filename: 'test.pdf',
        contentType: 'application/pdf'
      });
      formData.append('toolName', 'repair_pdf');
      formData.append('sessionToken', sessionToken);
      formData.append('parameters', JSON.stringify({}));

      const processResponse = await axios.post(`${BASE_URL}/pdf-tools/process`, formData, {
        headers: {
          ...authHeaders,
          ...formData.getHeaders()
        }
      });

      if (processResponse.data.success) {
        console.log('✅ PDF processing successful');
        console.log(`   Output Files: ${processResponse.data.data.outputFiles.length}`);
        console.log(`   Processing Time: ${processResponse.data.data.processingTime}ms`);
        console.log(`   Quota Consumed: ${processResponse.data.data.quotaConsumed}\n`);

        // Step 5: Test download access (should work even if quota is reached)
        console.log('5. Testing download access...');
        const downloadUrls = processResponse.data.data.outputFiles.map(file => file.downloadUrl);
        
        for (const downloadUrl of downloadUrls) {
          try {
            const downloadResponse = await axios.get(`${BASE_URL}${downloadUrl}`, {
              headers: authHeaders,
              responseType: 'stream'
            });
            
            if (downloadResponse.status === 200) {
              console.log('✅ Download access confirmed');
            }
          } catch (downloadError) {
            console.log('❌ Download failed:', downloadError.response?.status);
          }
        }

        // Step 6: Complete session
        console.log('\n6. Completing processing session...');
        const completeResponse = await axios.post(`${BASE_URL}/pdf-tools/complete-session`, {
          sessionToken
        }, { headers: authHeaders });

        if (completeResponse.data.success) {
          console.log('✅ Session completed successfully\n');
        }

        // Step 7: Test quota enforcement for NEW session
        console.log('7. Testing quota enforcement for new session...');
        
        // Try to start another session immediately
        try {
          const newQuotaResponse = await axios.post(`${BASE_URL}/pdf-tools/check-quota`, {
            toolName: 'repair_pdf'
          }, { headers: authHeaders });

          if (newQuotaResponse.data.success) {
            console.log('✅ New session allowed');
            console.log(`   Remaining Files: ${newQuotaResponse.data.data.remainingQuota.files}`);
          }
        } catch (quotaError) {
          if (quotaError.response?.status === 429) {
            console.log('✅ Quota enforcement working - new session blocked');
            console.log(`   Reason: ${quotaError.response.data.error.reason}`);
            console.log(`   Message: ${quotaError.response.data.error.message}`);
          }
        }

      } else {
        console.log('❌ PDF processing failed:', processResponse.data.error);
      }

    } else {
      console.log('❌ Quota check failed:', quotaResponse.data.error);
    }

    console.log('\n🎉 Graceful quota enforcement test completed!');
    console.log('\n📋 Key Features Tested:');
    console.log('   ✅ Session-based quota tracking');
    console.log('   ✅ Processing allowed within session');
    console.log('   ✅ Download access preserved');
    console.log('   ✅ Session completion');
    console.log('   ✅ New session quota enforcement');

  } catch (error) {
    console.error('\n❌ Test failed:', error.response?.data || error.message);
    console.log('\n🔧 Troubleshooting:');
    console.log('   • Make sure backend is running on port 3001');
    console.log('   • Verify user exists (<EMAIL>)');
    console.log('   • Check database connection');
  }
}

// Run the test
testGracefulQuotaSystem();
