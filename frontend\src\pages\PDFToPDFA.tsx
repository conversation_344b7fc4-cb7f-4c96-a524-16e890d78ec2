import React, { useState } from 'react';
import { FileText, Download, ArrowRight } from 'lucide-react';
import ToolLayout from '../components/ToolLayout';
import FileUpload from '../components/FileUpload';

const PDFToPDFA = () => {
  const [files, setFiles] = useState<File[]>([]);
  const [isProcessing, setIsProcessing] = useState(false);
  const [pdfAVersion, setPdfAVersion] = useState<'1' | '2' | '3'>('2');

  const handleFileSelect = (selectedFiles: File[]) => {
    setFiles(selectedFiles);
  };

  const handleConvert = () => {
    if (files.length === 0) return;
    
    setIsProcessing(true);
    // Simulate processing
    setTimeout(() => {
      setIsProcessing(false);
      alert('PDF converti en PDF/A avec succès!');
    }, 2000);
  };

  return (
    <ToolLayout
      title="PDF en PDF/A"
      description="Transformez votre PDF en PDF/A, la version au standard ISO des PDF, pour un archivage à long-terme"
      icon={<FileText className="w-8 h-8" />}
      color="from-zinc-500 to-zinc-600"
    >
      <div className="space-y-6">
        <FileUpload
          onFileSelect={handleFileSelect}
          accept=".pdf"
          multiple={false}
          maxFiles={1}
          title="Sélectionnez votre fichier PDF"
          description="Glissez-déposez un fichier PDF ici ou cliquez pour sélectionner"
        />

        {files.length > 0 && (
          <div className="bg-slate-50 p-6 rounded-xl">
            <h3 className="text-lg font-semibold text-slate-700 mb-4">
              Version PDF/A
            </h3>
            
            <div className="space-y-4">
              <label className="flex items-center justify-between p-4 bg-white rounded-lg cursor-pointer hover:bg-slate-50 transition-colors">
                <div className="flex items-center space-x-3">
                  <input
                    type="radio"
                    name="pdfAVersion"
                    value="1"
                    checked={pdfAVersion === '1'}
                    onChange={(e) => setPdfAVersion(e.target.value as '1')}
                    className="text-zinc-600"
                  />
                  <div>
                    <span className="text-slate-700 font-medium">PDF/A-1</span>
                    <p className="text-sm text-slate-500">Basé sur PDF 1.4, compatible avec la plupart des systèmes</p>
                  </div>
                </div>
                <div className="text-zinc-600 font-medium">ISO 19005-1</div>
              </label>

              <label className="flex items-center justify-between p-4 bg-white rounded-lg cursor-pointer hover:bg-slate-50 transition-colors">
                <div className="flex items-center space-x-3">
                  <input
                    type="radio"
                    name="pdfAVersion"
                    value="2"
                    checked={pdfAVersion === '2'}
                    onChange={(e) => setPdfAVersion(e.target.value as '2')}
                    className="text-zinc-600"
                  />
                  <div>
                    <span className="text-slate-700 font-medium">PDF/A-2 (recommandé)</span>
                    <p className="text-sm text-slate-500">Basé sur PDF 1.7, support des signatures et formulaires</p>
                  </div>
                </div>
                <div className="text-zinc-600 font-medium">ISO 19005-2</div>
              </label>

              <label className="flex items-center justify-between p-4 bg-white rounded-lg cursor-pointer hover:bg-slate-50 transition-colors">
                <div className="flex items-center space-x-3">
                  <input
                    type="radio"
                    name="pdfAVersion"
                    value="3"
                    checked={pdfAVersion === '3'}
                    onChange={(e) => setPdfAVersion(e.target.value as '3')}
                    className="text-zinc-600"
                  />
                  <div>
                    <span className="text-slate-700 font-medium">PDF/A-3</span>
                    <p className="text-sm text-slate-500">Permet l'intégration de fichiers dans n'importe quel format</p>
                  </div>
                </div>
                <div className="text-zinc-600 font-medium">ISO 19005-3</div>
              </label>
            </div>

            <div className="mt-6 bg-green-50 p-4 rounded-lg">
              <div className="flex items-center space-x-2">
                <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                <span className="text-sm text-green-800 font-medium">Avantages PDF/A</span>
              </div>
              <ul className="text-sm text-green-700 mt-1 space-y-1">
                <li>• Archivage à long terme garanti</li>
                <li>• Conformité aux standards ISO</li>
                <li>• Indépendance des logiciels et matériels</li>
                <li>• Préservation de l'apparence visuelle</li>
              </ul>
            </div>
          </div>
        )}

        {files.length > 0 && (
          <div className="flex justify-center">
            <button
              onClick={handleConvert}
              disabled={isProcessing}
              className="bg-gradient-to-r from-zinc-600 to-slate-600 text-white px-8 py-4 rounded-xl text-lg font-medium hover:shadow-xl transition-all duration-200 transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2"
            >
              {isProcessing ? (
                <>
                  <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white"></div>
                  <span>Conversion en cours...</span>
                </>
              ) : (
                <>
                  <span>Convertir en PDF/A</span>
                  <ArrowRight className="w-5 h-5" />
                </>
              )}
            </button>
          </div>
        )}
      </div>
    </ToolLayout>
  );
};

export default PDFToPDFA;