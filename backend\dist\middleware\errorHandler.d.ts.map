{"version": 3, "file": "errorHandler.d.ts", "sourceRoot": "", "sources": ["../../src/middleware/errorHandler.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,OAAO,EAAE,QAAQ,EAAE,YAAY,EAAE,MAAM,SAAS,CAAC;AAG1D,MAAM,WAAW,QAAS,SAAQ,KAAK;IACrC,UAAU,CAAC,EAAE,MAAM,CAAC;IACpB,aAAa,CAAC,EAAE,OAAO,CAAC;CACzB;AAED,qBAAa,eAAgB,SAAQ,KAAK;IACxC,UAAU,SAAO;IACjB,aAAa,UAAQ;gBAET,OAAO,EAAE,MAAM;CAI5B;AAED,qBAAa,mBAAoB,SAAQ,KAAK;IAC5C,UAAU,SAAO;IACjB,aAAa,UAAQ;gBAET,OAAO,GAAE,MAAkC;CAIxD;AAED,qBAAa,kBAAmB,SAAQ,KAAK;IAC3C,UAAU,SAAO;IACjB,aAAa,UAAQ;gBAET,OAAO,GAAE,MAAmC;CAIzD;AAED,qBAAa,aAAc,SAAQ,KAAK;IACtC,UAAU,SAAO;IACjB,aAAa,UAAQ;gBAET,OAAO,GAAE,MAA6B;CAInD;AAED,qBAAa,eAAgB,SAAQ,KAAK;IACxC,UAAU,SAAO;IACjB,aAAa,UAAQ;gBAET,OAAO,EAAE,MAAM;CAI5B;AAED,qBAAa,cAAe,SAAQ,KAAK;IACvC,UAAU,SAAO;IACjB,aAAa,UAAQ;gBAET,OAAO,GAAE,MAA8B;CAIpD;AAED,eAAO,MAAM,YAAY,GACvB,OAAO,QAAQ,EACf,KAAK,OAAO,EACZ,KAAK,QAAQ,EACb,MAAM,YAAY,KACjB,IAgCF,CAAC;AAEF,eAAO,MAAM,YAAY,GAAI,IAAI,QAAQ,MAC/B,KAAK,OAAO,EAAE,KAAK,QAAQ,EAAE,MAAM,YAAY,SAGxD,CAAC"}