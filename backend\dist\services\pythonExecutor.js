"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.pythonExecutor = exports.PythonExecutor = void 0;
const child_process_1 = require("child_process");
const path_1 = __importDefault(require("path"));
const promises_1 = __importDefault(require("fs/promises"));
const uuid_1 = require("uuid");
const logger_1 = require("../utils/logger");
class PythonExecutor {
    constructor() {
        this.toolsPath = path_1.default.resolve(__dirname, '../../../tools');
        this.pythonPath = process.env['PYTHON_PATH'] || 'python';
        this.maxTimeout = parseInt(process.env['MAX_PROCESSING_TIMEOUT'] || '300000');
    }
    async executeTool(params) {
        const startTime = Date.now();
        const jobId = (0, uuid_1.v4)();
        logger_1.logger.info('Starting Python tool execution', {
            jobId,
            toolName: params.toolName,
            inputFiles: params.inputFiles.length,
            parameters: params.parameters
        });
        try {
            await this.validateInputs(params);
            const scriptPath = path_1.default.join(this.toolsPath, `${params.toolName}.py`);
            const tempConfigPath = await this.createTempConfig(jobId, params);
            const result = await this.runPythonScript(scriptPath, tempConfigPath, params.timeout);
            await this.cleanup(tempConfigPath);
            const processingTime = Date.now() - startTime;
            logger_1.logger.info('Python tool execution completed', {
                jobId,
                toolName: params.toolName,
                success: result.success,
                processingTime
            });
            return {
                ...result,
                processingTime
            };
        }
        catch (error) {
            const processingTime = Date.now() - startTime;
            logger_1.logger.error('Python tool execution failed', {
                jobId,
                toolName: params.toolName,
                error: error instanceof Error ? error.message : String(error),
                processingTime
            });
            return {
                success: false,
                outputFiles: [],
                error: error instanceof Error ? error.message : String(error),
                processingTime
            };
        }
    }
    async validateInputs(params) {
        const toolPath = path_1.default.join(this.toolsPath, `${params.toolName}.py`);
        try {
            await promises_1.default.access(toolPath);
        }
        catch {
            throw new Error(`Tool '${params.toolName}' not found`);
        }
        for (const inputFile of params.inputFiles) {
            try {
                await promises_1.default.access(inputFile);
            }
            catch {
                throw new Error(`Input file not found: ${inputFile}`);
            }
        }
        try {
            await promises_1.default.mkdir(params.outputPath, { recursive: true });
        }
        catch (error) {
            throw new Error(`Cannot create output directory: ${params.outputPath}`);
        }
    }
    async createTempConfig(jobId, params) {
        const config = {
            job_id: jobId,
            tool_name: params.toolName,
            input_files: params.inputFiles,
            output_path: params.outputPath,
            parameters: params.parameters || {}
        };
        const tempDir = path_1.default.join(__dirname, '../../../temp');
        await promises_1.default.mkdir(tempDir, { recursive: true });
        const configPath = path_1.default.join(tempDir, `${jobId}_config.json`);
        await promises_1.default.writeFile(configPath, JSON.stringify(config, null, 2));
        return configPath;
    }
    async runPythonScript(scriptPath, configPath, timeout) {
        return new Promise((resolve, reject) => {
            const actualTimeout = Math.min(timeout || this.maxTimeout, this.maxTimeout);
            const wrapperScript = path_1.default.join(this.toolsPath, `${path_1.default.basename(scriptPath, '.py')}_wrapper.py`);
            const pythonProcess = (0, child_process_1.spawn)(this.pythonPath, [
                wrapperScript,
                '--config', configPath
            ], {
                stdio: ['pipe', 'pipe', 'pipe'],
                timeout: actualTimeout
            });
            let stdout = '';
            let stderr = '';
            pythonProcess.stdout?.on('data', (data) => {
                stdout += data.toString();
            });
            pythonProcess.stderr?.on('data', (data) => {
                stderr += data.toString();
            });
            pythonProcess.on('close', (code) => {
                if (code === 0) {
                    try {
                        const result = JSON.parse(stdout);
                        resolve({
                            success: true,
                            outputFiles: result.output_files || [],
                            logs: result.logs || []
                        });
                    }
                    catch (error) {
                        reject(new Error(`Failed to parse Python script output: ${error}`));
                    }
                }
                else {
                    reject(new Error(`Python script failed with code ${code}: ${stderr}`));
                }
            });
            pythonProcess.on('error', (error) => {
                reject(new Error(`Failed to start Python process: ${error.message}`));
            });
            setTimeout(() => {
                if (!pythonProcess.killed) {
                    pythonProcess.kill('SIGTERM');
                    reject(new Error(`Python script timed out after ${actualTimeout}ms`));
                }
            }, actualTimeout);
        });
    }
    async cleanup(configPath) {
        try {
            await promises_1.default.unlink(configPath);
        }
        catch (error) {
            logger_1.logger.warn('Failed to cleanup temp config file', { configPath, error });
        }
    }
    async getAvailableTools() {
        try {
            const files = await promises_1.default.readdir(this.toolsPath);
            return files
                .filter(file => file.endsWith('.py') && file !== '__init__.py' && file !== 'base_tool.py')
                .map(file => path_1.default.basename(file, '.py'));
        }
        catch (error) {
            logger_1.logger.error('Failed to get available tools', { error });
            return [];
        }
    }
}
exports.PythonExecutor = PythonExecutor;
exports.pythonExecutor = new PythonExecutor();
//# sourceMappingURL=pythonExecutor.js.map