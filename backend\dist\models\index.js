"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ProcessingSession = exports.ProcessingJob = exports.UsageRecord = exports.User = exports.sequelize = void 0;
const database_1 = require("../config/database");
Object.defineProperty(exports, "sequelize", { enumerable: true, get: function () { return database_1.sequelize; } });
const User_1 = require("./User");
Object.defineProperty(exports, "User", { enumerable: true, get: function () { return User_1.User; } });
const UsageRecord_1 = require("./UsageRecord");
Object.defineProperty(exports, "UsageRecord", { enumerable: true, get: function () { return UsageRecord_1.UsageRecord; } });
const ProcessingJob_1 = require("./ProcessingJob");
Object.defineProperty(exports, "ProcessingJob", { enumerable: true, get: function () { return ProcessingJob_1.ProcessingJob; } });
const ProcessingSession_1 = require("./ProcessingSession");
Object.defineProperty(exports, "ProcessingSession", { enumerable: true, get: function () { return ProcessingSession_1.ProcessingSession; } });
User_1.User.hasMany(UsageRecord_1.UsageRecord, {
    foreignKey: 'user_id',
    as: 'usageRecords',
    onDelete: 'CASCADE',
});
UsageRecord_1.UsageRecord.belongsTo(User_1.User, {
    foreignKey: 'user_id',
    as: 'user',
});
User_1.User.hasMany(ProcessingJob_1.ProcessingJob, {
    foreignKey: 'user_id',
    as: 'processingJobs',
    onDelete: 'CASCADE',
});
ProcessingJob_1.ProcessingJob.belongsTo(User_1.User, {
    foreignKey: 'user_id',
    as: 'user',
});
User_1.User.hasMany(ProcessingSession_1.ProcessingSession, {
    foreignKey: 'user_id',
    as: 'processingSessions',
    onDelete: 'CASCADE',
});
ProcessingSession_1.ProcessingSession.belongsTo(User_1.User, {
    foreignKey: 'user_id',
    as: 'user',
});
exports.default = {
    sequelize: database_1.sequelize,
    User: User_1.User,
    UsageRecord: UsageRecord_1.UsageRecord,
    ProcessingJob: ProcessingJob_1.ProcessingJob,
    ProcessingSession: ProcessingSession_1.ProcessingSession,
};
//# sourceMappingURL=index.js.map