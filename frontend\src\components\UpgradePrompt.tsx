import React from 'react';
import { X, <PERSON>, Check, Zap, Shield, Clock } from 'lucide-react';
import { Link } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';

interface UpgradePromptProps {
  isOpen: boolean;
  onClose: () => void;
}

const UpgradePrompt: React.FC<UpgradePromptProps> = ({ isOpen, onClose }) => {
  const { isAuthenticated, user } = useAuth();

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-2xl shadow-2xl max-w-md w-full overflow-hidden">
        {/* Header */}
        <div className="bg-gradient-to-r from-orange-500 to-red-500 p-6 text-white relative">
          <button
            onClick={onClose}
            className="absolute top-4 right-4 p-1 hover:bg-white/20 rounded-full transition-colors"
          >
            <X className="w-5 h-5" />
          </button>
          <div className="text-center">
            <div className="w-16 h-16 bg-white/20 rounded-full flex items-center justify-center mx-auto mb-4">
              <Crown className="w-8 h-8" />
            </div>
            <h2 className="text-2xl font-bold mb-2">Quota quotidien atteint !</h2>
            <p className="text-orange-100">
              Vous avez utilisé vos 2 outils gratuits pour aujourd'hui
            </p>
          </div>
        </div>

        {/* Content */}
        <div className="p-6">
          <div className="text-center mb-6">
            <h3 className="text-xl font-bold text-gray-900 mb-2">
              Continuez avec un compte gratuit
            </h3>
            <p className="text-gray-600">
              Créez un compte pour débloquer plus d'utilisations et accéder aux fonctionnalités premium
            </p>
          </div>

          {/* Free Account Benefits */}
          <div className="bg-blue-50 rounded-lg p-4 mb-6">
            <h4 className="font-semibold text-blue-900 mb-3">Compte gratuit :</h4>
            <div className="space-y-2">
              <div className="flex items-center space-x-2">
                <Check className="w-4 h-4 text-blue-600" />
                <span className="text-sm text-blue-800">3 utilisations par jour</span>
              </div>
              <div className="flex items-center space-x-2">
                <Check className="w-4 h-4 text-blue-600" />
                <span className="text-sm text-blue-800">Accès à tous les outils de base</span>
              </div>
              <div className="flex items-center space-x-2">
                <Check className="w-4 h-4 text-blue-600" />
                <span className="text-sm text-blue-800">Traitement sécurisé</span>
              </div>
            </div>
          </div>

          {/* Premium Benefits */}
          <div className="bg-gradient-to-r from-yellow-50 to-orange-50 border border-yellow-200 rounded-lg p-4 mb-6">
            <h4 className="font-semibold text-yellow-900 mb-3 flex items-center">
              <Crown className="w-4 h-4 mr-2" />
              Premium (9,99€/mois) :
            </h4>
            <div className="space-y-2">
              <div className="flex items-center space-x-2">
                <Zap className="w-4 h-4 text-yellow-600" />
                <span className="text-sm text-yellow-800">Utilisations illimitées</span>
              </div>
              <div className="flex items-center space-x-2">
                <Shield className="w-4 h-4 text-yellow-600" />
                <span className="text-sm text-yellow-800">Outils premium (Protection PDF)</span>
              </div>
              <div className="flex items-center space-x-2">
                <Clock className="w-4 h-4 text-yellow-600" />
                <span className="text-sm text-yellow-800">Traitement prioritaire</span>
              </div>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="space-y-3">
            {isAuthenticated ? (
              <button
                onClick={onClose}
                className="w-full bg-gradient-to-r from-yellow-500 to-orange-500 text-white py-3 px-4 rounded-lg font-medium text-center hover:shadow-lg transition-all duration-200"
              >
                Passer Premium maintenant
              </button>
            ) : (
              <>
                <Link
                  to="/signup"
                  onClick={onClose}
                  className="w-full bg-gradient-to-r from-blue-600 to-purple-600 text-white py-3 px-4 rounded-lg font-medium text-center block hover:shadow-lg transition-all duration-200"
                >
                  Créer un compte gratuit
                </Link>
                <Link
                  to="/signin"
                  onClick={onClose}
                  className="w-full border border-gray-300 text-gray-700 py-3 px-4 rounded-lg font-medium text-center block hover:bg-gray-50 transition-colors"
                >
                  J'ai déjà un compte
                </Link>
              </>
            )}
          </div>

          <p className="text-xs text-gray-500 text-center mt-4">
            Vos utilisations se remettent à zéro chaque jour à minuit
          </p>
        </div>
      </div>
    </div>
  );
};

export default UpgradePrompt;