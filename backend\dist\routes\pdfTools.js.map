{"version": 3, "file": "pdfTools.js", "sourceRoot": "", "sources": ["../../src/routes/pdfTools.ts"], "names": [], "mappings": ";;;;;AAAA,sDAA8B;AAC9B,6DAA0D;AAC1D,yDAA4E;AAC5E,iDAA2D;AAC3D,+DAA4D;AAC5D,+DAA4D;AAC5D,6CAAgE;AAChE,4CAAyC;AACzC,+BAAoC;AACpC,gDAAwB;AAExB,MAAM,MAAM,GAAG,iBAAO,CAAC,MAAM,EAAE,CAAC;AAGhC,MAAM,CAAC,GAAG,CAAC,QAAQ,EAAE,IAAA,2BAAY,EAAC,KAAK,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE;IACnD,MAAM,KAAK,GAAG,MAAM,+BAAc,CAAC,iBAAiB,EAAE,CAAC;IAGvD,MAAM,YAAY,GAAG;QACnB,UAAU,EAAE;YACV,IAAI,EAAE,YAAY;YAClB,WAAW,EAAE,oCAAoC;YACjD,UAAU,EAAE,CAAC,iBAAiB,CAAC;YAC/B,UAAU,EAAE;gBACV,QAAQ,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,OAAO,EAAE,CAAC,OAAO,EAAE,UAAU,EAAE,aAAa,CAAC,EAAE,OAAO,EAAE,UAAU,EAAE;gBAChG,iBAAiB,EAAE,EAAE,IAAI,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE;aACtD;SACF;QACD,SAAS,EAAE;YACT,IAAI,EAAE,YAAY;YAClB,WAAW,EAAE,qCAAqC;YAClD,UAAU,EAAE,CAAC,iBAAiB,CAAC;YAC/B,UAAU,EAAE;gBACV,eAAe,EAAE,EAAE,IAAI,EAAE,OAAO,EAAE,WAAW,EAAE,qCAAqC,EAAE;aACvF;SACF;QACD,YAAY,EAAE;YACZ,IAAI,EAAE,cAAc;YACpB,WAAW,EAAE,sBAAsB;YACnC,UAAU,EAAE,CAAC,iBAAiB,CAAC;YAC/B,UAAU,EAAE;gBACV,OAAO,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,OAAO,EAAE,CAAC,KAAK,EAAE,QAAQ,EAAE,MAAM,CAAC,EAAE,OAAO,EAAE,QAAQ,EAAE;gBAClF,eAAe,EAAE,EAAE,IAAI,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE;aACpD;SACF;QACD,YAAY,EAAE;YACZ,IAAI,EAAE,cAAc;YACpB,WAAW,EAAE,mCAAmC;YAChD,UAAU,EAAE,CAAC,mEAAmE,EAAE,0BAA0B,CAAC;YAC7G,UAAU,EAAE;gBACV,WAAW,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,OAAO,EAAE,CAAC,UAAU,EAAE,WAAW,EAAE,MAAM,CAAC,EAAE,OAAO,EAAE,UAAU,EAAE;gBAChG,MAAM,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,OAAO,EAAE,KAAK,EAAE;gBAC1C,iBAAiB,EAAE,EAAE,IAAI,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE;aACtD;SACF;QACD,OAAO,EAAE;YACP,IAAI,EAAE,SAAS;YACf,WAAW,EAAE,0CAA0C;YACvD,UAAU,EAAE,CAAC,iBAAiB,EAAE,YAAY,EAAE,WAAW,CAAC;YAC1D,UAAU,EAAE;gBACV,QAAQ,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,OAAO,EAAE,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC,EAAE,OAAO,EAAE,KAAK,EAAE;gBACnF,aAAa,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,OAAO,EAAE,CAAC,KAAK,EAAE,MAAM,EAAE,MAAM,EAAE,MAAM,CAAC,EAAE,OAAO,EAAE,KAAK,EAAE;gBAC3F,GAAG,EAAE,EAAE,IAAI,EAAE,QAAQ,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,OAAO,EAAE,GAAG,EAAE;aAC1D;SACF;KACF,CAAC;IAEF,MAAM,cAAc,GAAG,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;QACxC,EAAE,EAAE,IAAI;QACR,GAAG,YAAY,CAAC,IAAiC,CAAC,IAAI;YACpD,IAAI,EAAE,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC,OAAO,CAAC,OAAO,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,WAAW,EAAE,CAAC;YACpE,WAAW,EAAE,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC,OAAO;YAC9C,UAAU,EAAE,CAAC,iBAAiB,CAAC;YAC/B,UAAU,EAAE,EAAE;SACf;KACF,CAAC,CAAC,CAAC;IAEJ,GAAG,CAAC,IAAI,CAAC;QACP,OAAO,EAAE,IAAI;QACb,IAAI,EAAE;YACJ,KAAK,EAAE,cAAc;YACrB,KAAK,EAAE,cAAc,CAAC,MAAM;SAC7B;KACF,CAAC,CAAC;AACL,CAAC,CAAC,CAAC,CAAC;AAGJ,MAAM,CAAC,IAAI,CAAC,cAAc,EACxB,mBAAY,EACZ,IAAA,2BAAY,EAAC,KAAK,EAAE,GAAQ,EAAE,GAAQ,EAAE,EAAE;IACxC,MAAM,EAAE,QAAQ,EAAE,GAAG,GAAG,CAAC,IAAI,CAAC;IAC9B,MAAM,MAAM,GAAG,GAAG,CAAC,IAAK,CAAC,EAAE,CAAC;IAC5B,MAAM,gBAAgB,GAAG,GAAG,CAAC,IAAK,CAAC,gBAAgB,CAAC;IAEpD,IAAI,CAAC,QAAQ,EAAE,CAAC;QACd,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;YAC1B,OAAO,EAAE,KAAK;YACd,KAAK,EAAE;gBACL,OAAO,EAAE,uBAAuB;gBAChC,IAAI,EAAE,iBAAiB;aACxB;SACF,CAAC,CAAC;IACL,CAAC;IAED,MAAM,UAAU,GAAG,MAAM,+BAAc,CAAC,kBAAkB,CACxD,MAAM,EACN,gBAAuB,EACvB,QAAQ,CACT,CAAC;IAEF,IAAI,UAAU,CAAC,UAAU,EAAE,CAAC;QAC1B,GAAG,CAAC,IAAI,CAAC;YACP,OAAO,EAAE,IAAI;YACb,IAAI,EAAE;gBACJ,UAAU,EAAE,IAAI;gBAChB,YAAY,EAAE,UAAU,CAAC,YAAY;gBACrC,cAAc,EAAE,UAAU,CAAC,cAAc;aAC1C;SACF,CAAC,CAAC;IACL,CAAC;SAAM,CAAC;QACN,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;YACnB,OAAO,EAAE,KAAK;YACd,KAAK,EAAE;gBACL,OAAO,EAAE,UAAU,CAAC,OAAO;gBAC3B,IAAI,EAAE,eAAe;gBACrB,MAAM,EAAE,UAAU,CAAC,MAAM;gBACzB,eAAe,EAAE,UAAU,CAAC,eAAe;aAC5C;SACF,CAAC,CAAC;IACL,CAAC;AACH,CAAC,CAAC,CACH,CAAC;AAGF,MAAM,CAAC,IAAI,CAAC,UAAU,EACpB,eAAM,CAAC,KAAK,CAAC,OAAO,EAAE,EAAE,CAAC,EACzB,0BAAa,EACb,IAAA,qBAAQ,EAAC,oBAAO,CAAC,aAAa,CAAC,EAC/B,mBAAY,EACZ,IAAA,2BAAY,EAAC,KAAK,EAAE,GAAQ,EAAE,GAAQ,EAAE,EAAE;IACxC,MAAM,EAAE,QAAQ,EAAE,YAAY,EAAE,GAAG,GAAG,CAAC,IAAI,CAAC;IAC5C,IAAI,UAAU,GAAG,EAAE,CAAC;IAGpB,IAAI,GAAG,CAAC,IAAI,CAAC,UAAU,EAAE,CAAC;QACxB,IAAI,CAAC;YACH,UAAU,GAAG,OAAO,GAAG,CAAC,IAAI,CAAC,UAAU,KAAK,QAAQ;gBAClD,CAAC,CAAC,IAAI,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC;gBACjC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,UAAU,CAAC;QAC1B,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;gBAC1B,OAAO,EAAE,KAAK;gBACd,KAAK,EAAE;oBACL,OAAO,EAAE,2BAA2B;oBACpC,IAAI,EAAE,iBAAiB;iBACxB;aACF,CAAC,CAAC;QACL,CAAC;IACH,CAAC;IAED,MAAM,KAAK,GAAG,GAAG,CAAC,KAA8B,CAAC;IACjD,MAAM,KAAK,GAAG,IAAA,SAAM,GAAE,CAAC;IACvB,MAAM,MAAM,GAAG,GAAG,CAAC,IAAI,EAAE,EAAE,CAAC;IAE5B,eAAM,CAAC,IAAI,CAAC,8BAA8B,EAAE;QAC1C,KAAK;QACL,QAAQ;QACR,SAAS,EAAE,KAAK,CAAC,MAAM;QACvB,UAAU;QACV,YAAY;QACZ,MAAM;KACP,CAAC,CAAC;IAEH,IAAI,CAAC;QAEH,IAAI,MAAM,IAAI,YAAY,EAAE,CAAC;YAC3B,MAAM,YAAY,GAAG,MAAM,+BAAc,CAAC,kBAAkB,CAAC,YAAY,EAAE,MAAM,CAAC,CAAC;YACnF,IAAI,CAAC,YAAY,CAAC,UAAU,EAAE,CAAC;gBAC7B,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;oBAC1B,OAAO,EAAE,KAAK;oBACd,KAAK,EAAE;wBACL,OAAO,EAAE,YAAY,CAAC,OAAO;wBAC7B,IAAI,EAAE,cAAc;wBACpB,MAAM,EAAE,YAAY,CAAC,MAAM;qBAC5B;iBACF,CAAC,CAAC;YACL,CAAC;QACH,CAAC;QAGD,MAAM,eAAe,GAAG,KAAK,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,IAAI,EAAE,EAAE,CAAC,GAAG,GAAG,IAAI,CAAC,IAAI,EAAE,CAAC,CAAC,GAAG,CAAC,IAAI,GAAG,IAAI,CAAC,CAAC;QAGxF,MAAM,UAAU,GAAG,MAAM,oBAAW,CAAC,iBAAiB,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;QAGtE,MAAM,UAAU,GAAG,MAAM,oBAAW,CAAC,qBAAqB,CAAC,MAAM,CAAC,CAAC;QAEnE,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAG7B,MAAM,MAAM,GAAG,MAAM,+BAAc,CAAC,WAAW,CAAC;YAC9C,QAAQ;YACR,UAAU;YACV,UAAU;YACV,UAAU;YACV,OAAO,EAAE,MAAM;SAChB,CAAC,CAAC;QAEH,MAAM,qBAAqB,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC,GAAG,IAAI,CAAC,CAAC;QAG1E,IAAI,MAAM,IAAI,YAAY,EAAE,CAAC;YAC3B,MAAM,+BAAc,CAAC,gBAAgB,CACnC,YAAY,EACZ,MAAM,EACN,QAAQ,EACR,eAAe,EACf,qBAAqB,EACrB,KAAK,CAAC,MAAM,EACZ,MAAM,CAAC,WAAW,CAAC,MAAM,EACzB,MAAM,CAAC,OAAO,EACd,MAAM,CAAC,KAAK,CACb,CAAC;QACJ,CAAC;QAED,IAAI,MAAM,CAAC,OAAO,EAAE,CAAC;YAEnB,oBAAW,CAAC,eAAe,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC;YAGjD,MAAM,YAAY,GAAG,MAAM,CAAC,WAAW,CAAC,GAAG,CAAC,QAAQ,CAAC,EAAE;gBACrD,MAAM,YAAY,GAAG,cAAI,CAAC,QAAQ,CAAC,oBAAW,CAAC,gBAAgB,EAAE,EAAE,QAAQ,CAAC,CAAC;gBAC7E,MAAM,WAAW,GAAG,2BAA2B,kBAAkB,CAAC,YAAY,CAAC,EAAE,CAAC;gBAClF,OAAO,YAAY,CAAC,CAAC,CAAC,GAAG,WAAW,YAAY,YAAY,EAAE,CAAC,CAAC,CAAC,WAAW,CAAC;YAC/E,CAAC,CAAC,CAAC;YAEH,GAAG,CAAC,IAAI,CAAC;gBACP,OAAO,EAAE,IAAI;gBACb,IAAI,EAAE;oBACJ,KAAK;oBACL,YAAY;oBACZ,WAAW,EAAE,MAAM,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC,QAAQ,EAAE,KAAK,EAAE,EAAE,CAAC,CAAC;wBACxD,IAAI,EAAE,QAAQ;wBACd,IAAI,EAAE,cAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC;wBAC7B,WAAW,EAAE,YAAY,CAAC,KAAK,CAAC;qBACjC,CAAC,CAAC;oBACH,cAAc,EAAE,MAAM,CAAC,cAAc;oBACrC,IAAI,EAAE,MAAM,CAAC,IAAI;oBACjB,aAAa,EAAE,MAAM,IAAI,YAAY,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK;iBACrD;aACF,CAAC,CAAC;QACL,CAAC;aAAM,CAAC;YAEN,MAAM,oBAAW,CAAC,YAAY,CAAC,UAAU,CAAC,CAAC;YAE3C,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;gBACnB,OAAO,EAAE,KAAK;gBACd,KAAK,EAAE;oBACL,OAAO,EAAE,MAAM,CAAC,KAAK,IAAI,mBAAmB;oBAC5C,IAAI,EAAE,iBAAiB;iBACxB;gBACD,IAAI,EAAE;oBACJ,KAAK;oBACL,YAAY;oBACZ,cAAc,EAAE,MAAM,CAAC,cAAc;iBACtC;aACF,CAAC,CAAC;QACL,CAAC;IAEH,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,eAAM,CAAC,KAAK,CAAC,4BAA4B,EAAE;YACzC,KAAK;YACL,QAAQ;YACR,KAAK,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC;SAC9D,CAAC,CAAC;QAEH,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;YACnB,OAAO,EAAE,KAAK;YACd,KAAK,EAAE;gBACL,OAAO,EAAE,2BAA2B;gBACpC,IAAI,EAAE,eAAe;aACtB;YACD,IAAI,EAAE,EAAE,KAAK,EAAE,YAAY,EAAE;SAC9B,CAAC,CAAC;IACL,CAAC;AACH,CAAC,CAAC,CACH,CAAC;AAGF,MAAM,CAAC,GAAG,CAAC,qBAAqB,EAC9B,mBAAY,EACZ,IAAA,2BAAY,EAAC,KAAK,EAAE,GAAQ,EAAE,GAAQ,EAAE,EAAE;IACxC,MAAM,QAAQ,GAAG,kBAAkB,CAAC,GAAG,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC;IACzD,MAAM,YAAY,GAAG,GAAG,CAAC,KAAK,CAAC,OAAO,CAAC;IACvC,MAAM,MAAM,GAAG,GAAG,CAAC,IAAI,EAAE,EAAE,CAAC;IAC5B,MAAM,QAAQ,GAAG,cAAI,CAAC,IAAI,CAAC,oBAAW,CAAC,gBAAgB,EAAE,EAAE,QAAQ,CAAC,CAAC;IAGrE,MAAM,YAAY,GAAG,cAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;IAC5C,MAAM,YAAY,GAAG,cAAI,CAAC,OAAO,CAAC,oBAAW,CAAC,gBAAgB,EAAE,CAAC,CAAC;IAElE,IAAI,CAAC,YAAY,CAAC,UAAU,CAAC,YAAY,CAAC,EAAE,CAAC;QAC3C,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;YAC1B,OAAO,EAAE,KAAK;YACd,KAAK,EAAE;gBACL,OAAO,EAAE,eAAe;gBACxB,IAAI,EAAE,eAAe;aACtB;SACF,CAAC,CAAC;IACL,CAAC;IAGD,IAAI,MAAM,IAAI,YAAY,EAAE,CAAC;QAC3B,MAAM,WAAW,GAAG,MAAM,+BAAc,CAAC,WAAW,CAAC,YAAY,EAAE,MAAM,CAAC,CAAC;QAC3E,IAAI,CAAC,WAAW,EAAE,CAAC;YACjB,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;gBAC1B,OAAO,EAAE,KAAK;gBACd,KAAK,EAAE;oBACL,OAAO,EAAE,uCAAuC;oBAChD,IAAI,EAAE,cAAc;iBACrB;aACF,CAAC,CAAC;QACL,CAAC;IACH,CAAC;IAGD,MAAM,QAAQ,GAAG,MAAM,oBAAW,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;IACzD,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,CAAC;QACrB,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;YAC1B,OAAO,EAAE,KAAK;YACd,KAAK,EAAE;gBACL,OAAO,EAAE,gBAAgB;gBACzB,IAAI,EAAE,eAAe;aACtB;SACF,CAAC,CAAC;IACL,CAAC;IAGD,MAAM,QAAQ,GAAG,cAAI,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;IACzC,MAAM,aAAa,GAAG,cAAI,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC,WAAW,EAAE,CAAC;IAE3D,IAAI,WAAW,GAAG,0BAA0B,CAAC;IAC7C,IAAI,aAAa,KAAK,MAAM,EAAE,CAAC;QAC7B,WAAW,GAAG,iBAAiB,CAAC;IAClC,CAAC;SAAM,IAAI,aAAa,KAAK,MAAM,EAAE,CAAC;QACpC,WAAW,GAAG,YAAY,CAAC;IAC7B,CAAC;SAAM,IAAI,aAAa,KAAK,OAAO,EAAE,CAAC;QACrC,WAAW,GAAG,kBAAkB,CAAC;IACnC,CAAC;SAAM,IAAI,aAAa,KAAK,OAAO,EAAE,CAAC;QACrC,WAAW,GAAG,yEAAyE,CAAC;IAC1F,CAAC;IAED,GAAG,CAAC,SAAS,CAAC,cAAc,EAAE,WAAW,CAAC,CAAC;IAC3C,GAAG,CAAC,SAAS,CAAC,qBAAqB,EAAE,yBAAyB,QAAQ,GAAG,CAAC,CAAC;IAC3E,GAAG,CAAC,SAAS,CAAC,gBAAgB,EAAE,QAAQ,CAAC,IAAI,IAAI,CAAC,CAAC,CAAC;IAGpD,GAAG,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;AACzB,CAAC,CAAC,CACH,CAAC;AAGF,MAAM,CAAC,IAAI,CAAC,mBAAmB,EAC7B,mBAAY,EACZ,IAAA,2BAAY,EAAC,KAAK,EAAE,GAAQ,EAAE,GAAQ,EAAE,EAAE;IACxC,MAAM,EAAE,YAAY,EAAE,GAAG,GAAG,CAAC,IAAI,CAAC;IAClC,MAAM,MAAM,GAAG,GAAG,CAAC,IAAK,CAAC,EAAE,CAAC;IAE5B,IAAI,CAAC,YAAY,EAAE,CAAC;QAClB,OAAO,GAAG,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,IAAI,CAAC;YAC1B,OAAO,EAAE,KAAK;YACd,KAAK,EAAE;gBACL,OAAO,EAAE,2BAA2B;gBACpC,IAAI,EAAE,iBAAiB;aACxB;SACF,CAAC,CAAC;IACL,CAAC;IAED,MAAM,+BAAc,CAAC,eAAe,CAAC,YAAY,EAAE,MAAM,CAAC,CAAC;IAE3D,GAAG,CAAC,IAAI,CAAC;QACP,OAAO,EAAE,IAAI;QACb,IAAI,EAAE;YACJ,OAAO,EAAE,gCAAgC;YACzC,YAAY;SACb;KACF,CAAC,CAAC;AACL,CAAC,CAAC,CACH,CAAC;AAGF,MAAM,CAAC,GAAG,CAAC,gBAAgB,EAAE,IAAA,2BAAY,EAAC,KAAK,EAAE,GAAG,EAAE,GAAG,EAAE,EAAE;IAC3D,MAAM,EAAE,KAAK,EAAE,GAAG,GAAG,CAAC,MAAM,CAAC;IAI7B,GAAG,CAAC,IAAI,CAAC;QACP,OAAO,EAAE,IAAI;QACb,IAAI,EAAE;YACJ,KAAK;YACL,MAAM,EAAE,WAAW;YACnB,OAAO,EAAE,uDAAuD;SACjE;KACF,CAAC,CAAC;AACL,CAAC,CAAC,CAAC,CAAC;AAEJ,kBAAe,MAAM,CAAC"}