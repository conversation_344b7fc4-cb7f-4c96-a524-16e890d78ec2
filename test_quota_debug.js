// Debug script to identify quota system issues
const axios = require('axios');

const BASE_URL = 'http://localhost:3001/api';

async function debugQuotaSystem() {
  console.log('🔍 Debugging Quota System Issues...\n');

  try {
    // Test 1: Check backend health
    console.log('1. Testing backend health...');
    const healthResponse = await axios.get(`${BASE_URL}/health`);
    console.log('✅ Backend is healthy:', healthResponse.data.status);

    // Test 2: Create a new user to test fresh quota
    console.log('\n2. Testing new user quota...');
    const newUser = {
      email: `debug-${Date.now()}@example.com`,
      password: 'TestPassword123!',
      confirmPassword: 'TestPassword123!',
      firstName: 'Debug',
      lastName: 'User'
    };

    const registerResponse = await axios.post(`${BASE_URL}/auth/register`, newUser);
    
    if (registerResponse.data.success) {
      const authToken = registerResponse.data.data.tokens.accessToken;
      console.log('✅ New user created successfully');
      console.log(`   User ID: ${registerResponse.data.data.user.id}`);
      console.log(`   Plan: ${registerResponse.data.data.user.subscriptionPlan}`);

      // Test 3: Check initial usage for new user
      console.log('\n3. Checking initial usage for new user...');
      const initialUsageResponse = await axios.get(`${BASE_URL}/user/usage`, {
        headers: { Authorization: `Bearer ${authToken}` }
      });

      if (initialUsageResponse.data.success) {
        const usage = initialUsageResponse.data.data;
        console.log('📊 Initial Usage Data:');
        console.log(`   Today's files: ${usage.today.files}`);
        console.log(`   Daily limit: ${usage.limits.maxFilesPerDay}`);
        console.log(`   Remaining: ${usage.remaining.files}`);
        console.log(`   Processing time today: ${usage.today.processingTimeSeconds}s`);
        console.log(`   Processing time limit: ${usage.limits.maxProcessingTimePerDay}s`);

        // Check if new user shows quota as exhausted
        if (usage.remaining.files <= 0) {
          console.log('❌ ISSUE FOUND: New user shows quota exhausted!');
        } else {
          console.log('✅ New user quota looks correct');
        }
      }

      // Test 4: Check quota system
      console.log('\n4. Testing quota check system...');
      const quotaResponse = await axios.post(`${BASE_URL}/pdf-tools/check-quota`, {
        toolName: 'repair_pdf'
      }, {
        headers: { Authorization: `Bearer ${authToken}` }
      });

      if (quotaResponse.data.success) {
        console.log('✅ Quota check successful');
        console.log(`   Session token: ${quotaResponse.data.data.sessionToken.substring(0, 8)}...`);
        console.log(`   Remaining quota: ${quotaResponse.data.data.remainingQuota.files} files`);
      } else {
        console.log('❌ ISSUE FOUND: Quota check failed for new user');
        console.log(`   Error: ${quotaResponse.data.error.message}`);
      }

    } else {
      console.log('❌ Failed to create new user:', registerResponse.data.error);
    }

    // Test 5: Test unregistered user quota (without auth)
    console.log('\n5. Testing unregistered user quota...');
    try {
      const guestQuotaResponse = await axios.post(`${BASE_URL}/pdf-tools/check-quota`, {
        toolName: 'repair_pdf'
      });

      if (guestQuotaResponse.data.success) {
        console.log('✅ Guest quota check successful');
        console.log(`   Remaining quota: ${guestQuotaResponse.data.data.remainingQuota.files} files`);
        console.log(`   Session token: ${guestQuotaResponse.data.data.sessionToken.substring(0, 8)}...`);

        // Test guest file processing
        console.log('\n5b. Testing guest file processing...');
        const FormData = require('form-data');
        const testPdfContent = Buffer.from('%PDF-1.4\n1 0 obj\n<<\n/Type /Catalog\n/Pages 2 0 R\n>>\nendobj\n2 0 obj\n<<\n/Type /Pages\n/Kids [3 0 R]\n/Count 1\n>>\nendobj\n3 0 obj\n<<\n/Type /Page\n/Parent 2 0 R\n/MediaBox [0 0 612 792]\n>>\nendobj\nxref\n0 4\n0000000000 65535 f \n0000000009 00000 n \n0000000074 00000 n \n0000000120 00000 n \ntrailer\n<<\n/Size 4\n/Root 1 0 R\n>>\nstartxref\n179\n%%EOF');

        const formData = new FormData();
        formData.append('files', testPdfContent, {
          filename: 'test.pdf',
          contentType: 'application/pdf'
        });
        formData.append('toolName', 'repair_pdf');
        formData.append('sessionToken', guestQuotaResponse.data.data.sessionToken);
        formData.append('parameters', '{}');

        const guestProcessResponse = await axios.post(`${BASE_URL}/pdf-tools/process`, formData, {
          headers: formData.getHeaders()
        });

        if (guestProcessResponse.data.success) {
          console.log('✅ Guest file processing successful');
          console.log(`   Output files: ${guestProcessResponse.data.data.outputFiles.length}`);

          // Test quota after processing
          const guestQuotaAfterResponse = await axios.post(`${BASE_URL}/pdf-tools/check-quota`, {
            toolName: 'repair_pdf'
          });

          if (guestQuotaAfterResponse.data.success) {
            console.log(`   Remaining quota after processing: ${guestQuotaAfterResponse.data.data.remainingQuota.files} files`);
          }
        } else {
          console.log('❌ Guest file processing failed');
          console.log(`   Error: ${guestProcessResponse.data.error.message}`);
        }

      } else {
        console.log('❌ ISSUE FOUND: Guest quota check failed');
        console.log(`   Error: ${guestQuotaResponse.data.error.message}`);
      }
    } catch (guestError) {
      console.log('❌ ISSUE FOUND: Guest quota system not working');
      console.log(`   Error: ${guestError.response?.data?.error?.message || guestError.message}`);
    }

    // Test 6: Check database for usage records
    console.log('\n6. Testing database usage records...');
    
    // Login with existing test user
    try {
      const loginResponse = await axios.post(`${BASE_URL}/auth/login`, {
        email: '<EMAIL>',
        password: 'TestPassword123!'
      });

      if (loginResponse.data.success) {
        const testToken = loginResponse.data.data.tokens.accessToken;
        
        const testUsageResponse = await axios.get(`${BASE_URL}/user/usage`, {
          headers: { Authorization: `Bearer ${testToken}` }
        });

        if (testUsageResponse.data.success) {
          const testUsage = testUsageResponse.data.data;
          console.log('📊 Existing User Usage Data:');
          console.log(`   Today's files: ${testUsage.today.files}`);
          console.log(`   Daily limit: ${testUsage.limits.maxFilesPerDay}`);
          console.log(`   Remaining: ${testUsage.remaining.files}`);
          
          // Get usage history
          const historyResponse = await axios.get(`${BASE_URL}/user/usage/history?days=1`, {
            headers: { Authorization: `Bearer ${testToken}` }
          });

          if (historyResponse.data.success) {
            console.log(`   Usage history records: ${historyResponse.data.data.records.length}`);
            if (historyResponse.data.data.records.length > 0) {
              const lastRecord = historyResponse.data.data.records[0];
              console.log(`   Last tool used: ${lastRecord.tool_name}`);
              console.log(`   Last usage time: ${lastRecord.created_at}`);
            }
          }
        }
      }
    } catch (testUserError) {
      console.log('ℹ️  Test user not found, skipping existing user test');
    }

    console.log('\n🔍 Quota System Analysis Complete!');
    console.log('\n📋 Issues to investigate:');
    console.log('   1. Check if new users are getting correct initial quota');
    console.log('   2. Verify guest/unregistered user quota system');
    console.log('   3. Check if usage records are being created correctly');
    console.log('   4. Verify quota calculation logic');
    console.log('   5. Check frontend quota display logic');

  } catch (error) {
    console.error('\n❌ Debug test failed:', error.response?.data || error.message);
  }
}

// Run the debug test
debugQuotaSystem();
