"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.asyncHandler = exports.errorHandler = exports.RateLimitError = exports.ProcessingError = exports.NotFoundError = exports.AuthorizationError = exports.AuthenticationError = exports.ValidationError = void 0;
const logger_1 = require("../utils/logger");
class ValidationError extends Error {
    constructor(message) {
        super(message);
        this.statusCode = 400;
        this.isOperational = true;
        this.name = 'ValidationError';
    }
}
exports.ValidationError = ValidationError;
class AuthenticationError extends Error {
    constructor(message = 'Authentication required') {
        super(message);
        this.statusCode = 401;
        this.isOperational = true;
        this.name = 'AuthenticationError';
    }
}
exports.AuthenticationError = AuthenticationError;
class AuthorizationError extends Error {
    constructor(message = 'Insufficient permissions') {
        super(message);
        this.statusCode = 403;
        this.isOperational = true;
        this.name = 'AuthorizationError';
    }
}
exports.AuthorizationError = AuthorizationError;
class NotFoundError extends Error {
    constructor(message = 'Resource not found') {
        super(message);
        this.statusCode = 404;
        this.isOperational = true;
        this.name = 'NotFoundError';
    }
}
exports.NotFoundError = NotFoundError;
class ProcessingError extends Error {
    constructor(message) {
        super(message);
        this.statusCode = 422;
        this.isOperational = true;
        this.name = 'ProcessingError';
    }
}
exports.ProcessingError = ProcessingError;
class RateLimitError extends Error {
    constructor(message = 'Rate limit exceeded') {
        super(message);
        this.statusCode = 429;
        this.isOperational = true;
        this.name = 'RateLimitError';
    }
}
exports.RateLimitError = RateLimitError;
const errorHandler = (error, req, res, next) => {
    const statusCode = error.statusCode || 500;
    const isOperational = error.isOperational || false;
    logger_1.logger.error('Request error', {
        error: error.message,
        stack: error.stack,
        statusCode,
        isOperational,
        method: req.method,
        url: req.url,
        userAgent: req.get('User-Agent'),
        ip: req.ip
    });
    const message = isOperational || process.env.NODE_ENV !== 'production'
        ? error.message
        : 'Internal server error';
    res.status(statusCode).json({
        success: false,
        error: {
            message,
            type: error.name,
            ...(process.env.NODE_ENV !== 'production' && !isOperational && {
                stack: error.stack
            })
        },
        timestamp: new Date().toISOString()
    });
};
exports.errorHandler = errorHandler;
const asyncHandler = (fn) => {
    return (req, res, next) => {
        Promise.resolve(fn(req, res, next)).catch(next);
    };
};
exports.asyncHandler = asyncHandler;
//# sourceMappingURL=errorHandler.js.map