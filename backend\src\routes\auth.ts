import express from 'express';
import bcrypt from 'bcryptjs';
import { asyncHand<PERSON> } from '../middleware/errorHandler';
import { validate, schemas } from '../middleware/validation';
import { AuthService, authenticate } from '../middleware/auth';
import { ValidationError, AuthenticationError } from '../middleware/errorHandler';
import { logger } from '../utils/logger';
import { User } from '../models/User';

const router = express.Router();

// Register new user
router.post('/register',
  validate(schemas.userRegistration),
  asyncHandler(async (req, res) => {
    const { email, password, firstName, lastName } = req.body;

    // Check if user already exists
    const existingUser = await User.findOne({
      where: { email: email.toLowerCase() }
    });

    if (existingUser) {
      throw new ValidationError('User with this email already exists');
    }

    // Hash password
    const saltRounds = 12;
    const passwordHash = await bcrypt.hash(password, saltRounds);

    // Create user
    const user = await User.create({
      email: email.toLowerCase(),
      password_hash: passwordHash,
      first_name: firstName,
      last_name: lastName,
      subscription_plan: 'free',
      is_verified: false, // In production, send verification email
    });

    // Generate tokens
    const { accessToken, refreshToken } = AuthService.generateTokens({
      userId: user.id,
      email: user.email,
      subscriptionPlan: user.subscription_plan,
      isVerified: user.is_verified
    });

    logger.info('User registered successfully', {
      userId: user.id,
      email: user.email
    });

    res.status(201).json({
      success: true,
      data: {
        user: {
          id: user.id,
          email: user.email,
          firstName: user.first_name,
          lastName: user.last_name,
          subscriptionPlan: user.subscription_plan,
          isVerified: user.is_verified
        },
        tokens: {
          accessToken,
          refreshToken
        }
      },
      message: 'User registered successfully'
    });
  })
);

// Login user
router.post('/login',
  validate(schemas.userLogin),
  asyncHandler(async (req, res) => {
    const { email, password } = req.body;

    // Find user
    const user = await User.findOne({
      where: { email: email.toLowerCase() }
    });

    if (!user) {
      throw new AuthenticationError('Invalid email or password');
    }

    // Verify password
    const isValidPassword = await bcrypt.compare(password, user.password_hash);
    if (!isValidPassword) {
      throw new AuthenticationError('Invalid email or password');
    }

    // Update last login
    user.last_login_at = new Date();
    await user.save();

    // Generate tokens
    const { accessToken, refreshToken } = AuthService.generateTokens({
      userId: user.id,
      email: user.email,
      subscriptionPlan: user.subscription_plan,
      isVerified: user.is_verified
    });

    logger.info('User logged in successfully', {
      userId: user.id,
      email: user.email
    });

    res.json({
      success: true,
      data: {
        user: {
          id: user.id,
          email: user.email,
          firstName: user.first_name,
          lastName: user.last_name,
          subscriptionPlan: user.subscription_plan,
          isVerified: user.is_verified,
          lastLoginAt: user.last_login_at
        },
        tokens: {
          accessToken,
          refreshToken
        }
      },
      message: 'Login successful'
    });
  })
);

// Refresh token
router.post('/refresh',
  asyncHandler(async (req, res) => {
    const { refreshToken } = req.body;

    if (!refreshToken) {
      throw new ValidationError('Refresh token is required');
    }

    try {
      const payload = AuthService.verifyToken(refreshToken);
      const user = await User.findByPk(payload.userId);

      if (!user) {
        throw new AuthenticationError('User not found');
      }

      // Generate new tokens
      const tokens = AuthService.generateTokens({
        userId: user.id,
        email: user.email,
        subscriptionPlan: user.subscription_plan,
        isVerified: user.is_verified
      });

      res.json({
        success: true,
        data: {
          tokens
        },
        message: 'Tokens refreshed successfully'
      });

    } catch (error) {
      throw new AuthenticationError('Invalid refresh token');
    }
  })
);

// Get current user profile
router.get('/profile',
  authenticate,
  asyncHandler(async (req, res) => {
    const user = await User.findByPk(req.user!.id);

    if (!user) {
      throw new AuthenticationError('User not found');
    }

    res.json({
      success: true,
      data: {
        user: {
          id: user.id,
          email: user.email,
          firstName: user.first_name,
          lastName: user.last_name,
          subscriptionPlan: user.subscription_plan,
          isVerified: user.is_verified,
          createdAt: user.created_at,
          lastLoginAt: user.last_login_at
        }
      }
    });
  })
);

// Update user profile
router.put('/profile',
  authenticate,
  asyncHandler(async (req, res) => {
    const { firstName, lastName } = req.body;
    const user = await User.findByPk(req.user!.id);

    if (!user) {
      throw new AuthenticationError('User not found');
    }

    // Update user data
    if (firstName) user.first_name = firstName;
    if (lastName) user.last_name = lastName;

    await user.save();

    logger.info('User profile updated', {
      userId: user.id,
      email: user.email
    });

    res.json({
      success: true,
      data: {
        user: {
          id: user.id,
          email: user.email,
          firstName: user.first_name,
          lastName: user.last_name,
          subscriptionPlan: user.subscription_plan,
          isVerified: user.is_verified
        }
      },
      message: 'Profile updated successfully'
    });
  })
);

// Change password
router.put('/password',
  authenticate,
  asyncHandler(async (req, res) => {
    const { currentPassword, newPassword } = req.body;
    const user = await User.findByPk(req.user!.id);

    if (!user) {
      throw new AuthenticationError('User not found');
    }

    // Verify current password
    const isValidPassword = await bcrypt.compare(currentPassword, user.password_hash);
    if (!isValidPassword) {
      throw new ValidationError('Current password is incorrect');
    }

    // Hash new password
    const saltRounds = 12;
    user.password_hash = await bcrypt.hash(newPassword, saltRounds);
    await user.save();

    logger.info('User password changed', {
      userId: user.id,
      email: user.email
    });

    res.json({
      success: true,
      message: 'Password changed successfully'
    });
  })
);

// Logout (client-side token removal)
router.post('/logout',
  authenticate,
  asyncHandler(async (req, res) => {
    logger.info('User logged out', {
      userId: req.user!.id,
      email: req.user!.email
    });

    res.json({
      success: true,
      message: 'Logged out successfully'
    });
  })
);

export default router;
