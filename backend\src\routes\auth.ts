import express from 'express';
import bcrypt from 'bcryptjs';
import { v4 as uuidv4 } from 'uuid';
import { asyncHand<PERSON> } from '../middleware/errorHandler';
import { validate, schemas } from '../middleware/validation';
import { AuthService, authenticate } from '../middleware/auth';
import { ValidationError, AuthenticationError } from '../middleware/errorHandler';
import { logger } from '../utils/logger';

const router = express.Router();

// In-memory user storage (replace with database in production)
interface User {
  id: string;
  email: string;
  passwordHash: string;
  firstName: string;
  lastName: string;
  subscriptionPlan: 'free' | 'premium' | 'enterprise';
  isVerified: boolean;
  createdAt: Date;
  lastLoginAt?: Date;
}

const users = new Map<string, User>();
const usersByEmail = new Map<string, User>();

// Register new user
router.post('/register', 
  validate(schemas.userRegistration),
  asyncHandler(async (req, res) => {
    const { email, password, firstName, lastName } = req.body;

    // Check if user already exists
    if (usersByEmail.has(email.toLowerCase())) {
      throw new ValidationError('User with this email already exists');
    }

    // Hash password
    const saltRounds = 12;
    const passwordHash = await bcrypt.hash(password, saltRounds);

    // Create user
    const user: User = {
      id: uuidv4(),
      email: email.toLowerCase(),
      passwordHash,
      firstName,
      lastName,
      subscriptionPlan: 'free',
      isVerified: false, // In production, send verification email
      createdAt: new Date()
    };

    // Store user
    users.set(user.id, user);
    usersByEmail.set(user.email, user);

    // Generate tokens
    const { accessToken, refreshToken } = AuthService.generateTokens({
      userId: user.id,
      email: user.email,
      subscriptionPlan: user.subscriptionPlan,
      isVerified: user.isVerified
    });

    logger.info('User registered successfully', {
      userId: user.id,
      email: user.email
    });

    res.status(201).json({
      success: true,
      data: {
        user: {
          id: user.id,
          email: user.email,
          firstName: user.firstName,
          lastName: user.lastName,
          subscriptionPlan: user.subscriptionPlan,
          isVerified: user.isVerified
        },
        tokens: {
          accessToken,
          refreshToken
        }
      },
      message: 'User registered successfully'
    });
  })
);

// Login user
router.post('/login',
  validate(schemas.userLogin),
  asyncHandler(async (req, res) => {
    const { email, password } = req.body;

    // Find user
    const user = usersByEmail.get(email.toLowerCase());
    if (!user) {
      throw new AuthenticationError('Invalid email or password');
    }

    // Verify password
    const isValidPassword = await bcrypt.compare(password, user.passwordHash);
    if (!isValidPassword) {
      throw new AuthenticationError('Invalid email or password');
    }

    // Update last login
    user.lastLoginAt = new Date();

    // Generate tokens
    const { accessToken, refreshToken } = AuthService.generateTokens({
      userId: user.id,
      email: user.email,
      subscriptionPlan: user.subscriptionPlan,
      isVerified: user.isVerified
    });

    logger.info('User logged in successfully', {
      userId: user.id,
      email: user.email
    });

    res.json({
      success: true,
      data: {
        user: {
          id: user.id,
          email: user.email,
          firstName: user.firstName,
          lastName: user.lastName,
          subscriptionPlan: user.subscriptionPlan,
          isVerified: user.isVerified,
          lastLoginAt: user.lastLoginAt
        },
        tokens: {
          accessToken,
          refreshToken
        }
      },
      message: 'Login successful'
    });
  })
);

// Refresh token
router.post('/refresh',
  asyncHandler(async (req, res) => {
    const { refreshToken } = req.body;

    if (!refreshToken) {
      throw new ValidationError('Refresh token is required');
    }

    try {
      const payload = AuthService.verifyToken(refreshToken);
      const user = users.get(payload.userId);

      if (!user) {
        throw new AuthenticationError('User not found');
      }

      // Generate new tokens
      const tokens = AuthService.generateTokens({
        userId: user.id,
        email: user.email,
        subscriptionPlan: user.subscriptionPlan,
        isVerified: user.isVerified
      });

      res.json({
        success: true,
        data: {
          tokens
        },
        message: 'Tokens refreshed successfully'
      });

    } catch (error) {
      throw new AuthenticationError('Invalid refresh token');
    }
  })
);

// Get current user profile
router.get('/profile',
  authenticate,
  asyncHandler(async (req, res) => {
    const user = users.get(req.user!.id);
    
    if (!user) {
      throw new AuthenticationError('User not found');
    }

    res.json({
      success: true,
      data: {
        user: {
          id: user.id,
          email: user.email,
          firstName: user.firstName,
          lastName: user.lastName,
          subscriptionPlan: user.subscriptionPlan,
          isVerified: user.isVerified,
          createdAt: user.createdAt,
          lastLoginAt: user.lastLoginAt
        }
      }
    });
  })
);

// Update user profile
router.put('/profile',
  authenticate,
  asyncHandler(async (req, res) => {
    const { firstName, lastName } = req.body;
    const user = users.get(req.user!.id);

    if (!user) {
      throw new AuthenticationError('User not found');
    }

    // Update user data
    if (firstName) user.firstName = firstName;
    if (lastName) user.lastName = lastName;

    logger.info('User profile updated', {
      userId: user.id,
      email: user.email
    });

    res.json({
      success: true,
      data: {
        user: {
          id: user.id,
          email: user.email,
          firstName: user.firstName,
          lastName: user.lastName,
          subscriptionPlan: user.subscriptionPlan,
          isVerified: user.isVerified
        }
      },
      message: 'Profile updated successfully'
    });
  })
);

// Change password
router.put('/password',
  authenticate,
  asyncHandler(async (req, res) => {
    const { currentPassword, newPassword } = req.body;
    const user = users.get(req.user!.id);

    if (!user) {
      throw new AuthenticationError('User not found');
    }

    // Verify current password
    const isValidPassword = await bcrypt.compare(currentPassword, user.passwordHash);
    if (!isValidPassword) {
      throw new ValidationError('Current password is incorrect');
    }

    // Hash new password
    const saltRounds = 12;
    user.passwordHash = await bcrypt.hash(newPassword, saltRounds);

    logger.info('User password changed', {
      userId: user.id,
      email: user.email
    });

    res.json({
      success: true,
      message: 'Password changed successfully'
    });
  })
);

// Logout (client-side token removal)
router.post('/logout',
  authenticate,
  asyncHandler(async (req, res) => {
    logger.info('User logged out', {
      userId: req.user!.id,
      email: req.user!.email
    });

    res.json({
      success: true,
      message: 'Logged out successfully'
    });
  })
);

export default router;
