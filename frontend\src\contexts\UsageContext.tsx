import React, { createContext, useContext, useState, useEffect } from 'react';
import { useAuth } from './AuthContext';

interface UsageData {
  ip: string;
  dailyUsage: number;
  lastUsageDate: string;
  browserFingerprint: string;
}

interface UsageContextType {
  canUseTools: () => boolean;
  incrementUsage: () => boolean;
  getRemainingUsage: () => number;
  showUpgradePrompt: boolean;
  setShowUpgradePrompt: (show: boolean) => void;
}

const UsageContext = createContext<UsageContextType | undefined>(undefined);

export const useUsage = () => {
  const context = useContext(UsageContext);
  if (!context) {
    throw new Error('useUsage must be used within a UsageProvider');
  }
  return context;
};

interface UsageProviderProps {
  children: React.ReactNode;
}

export const UsageProvider: React.FC<UsageProviderProps> = ({ children }) => {
  const [showUpgradePrompt, setShowUpgradePrompt] = useState(false);
  const { user, isAuthenticated } = useAuth();

  // Generate browser fingerprint
  const generateFingerprint = (): string => {
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    if (ctx) {
      ctx.textBaseline = 'top';
      ctx.font = '14px Arial';
      ctx.fillText('Browser fingerprint', 2, 2);
    }
    
    const fingerprint = [
      navigator.userAgent,
      navigator.language,
      screen.width + 'x' + screen.height,
      new Date().getTimezoneOffset(),
      canvas.toDataURL()
    ].join('|');
    
    return btoa(fingerprint).slice(0, 32);
  };

  // Get user's IP (simulated with a combination of factors)
  const getUserIdentifier = (): string => {
    const fingerprint = generateFingerprint();
    const stored = localStorage.getItem('user_identifier');
    if (stored) return stored;
    
    const identifier = fingerprint + '_' + Date.now();
    localStorage.setItem('user_identifier', identifier);
    return identifier;
  };

  const loadUsageData = (): UsageData => {
    const identifier = getUserIdentifier();
    const stored = localStorage.getItem(`usage_${identifier}`);
    
    if (stored) {
      const data = JSON.parse(stored);
      // Reset if new day
      const today = new Date().toDateString();
      if (data.lastUsageDate !== today) {
        data.dailyUsage = 0;
        data.lastUsageDate = today;
        saveUsageData(data);
      }
      return data;
    }
    
    const newData: UsageData = {
      ip: identifier,
      dailyUsage: 0,
      lastUsageDate: new Date().toDateString(),
      browserFingerprint: generateFingerprint()
    };
    
    saveUsageData(newData);
    return newData;
  };

  const saveUsageData = (data: UsageData) => {
    localStorage.setItem(`usage_${data.ip}`, JSON.stringify(data));
  };

  const canUseTools = (): boolean => {
    // Premium users have unlimited access
    if (isAuthenticated && user?.plan === 'premium') {
      return true;
    }
    
    // Free authenticated users get 3 uses per day
    if (isAuthenticated && user?.plan === 'free') {
      const data = loadUsageData();
      return data.dailyUsage < 3;
    }
    
    // Non-authenticated users get 2 uses per day
    const data = loadUsageData();
    return data.dailyUsage < 2; // Free quota is 2 uses per day
  };

  const incrementUsage = (): boolean => {
    // Premium users don't have usage limits
    if (isAuthenticated && user?.plan === 'premium') {
      return true;
    }
    
    // Check if user can still use tools before incrementing
    if (!canUseTools()) {
      return false;
    }
    
    const data = loadUsageData();
    const today = new Date().toDateString();
    
    if (data.lastUsageDate !== today) {
      data.dailyUsage = 1;
      data.lastUsageDate = today;
    } else {
      data.dailyUsage += 1;
    }
    
    saveUsageData(data);
    
    // Show upgrade prompt if exceeded quota
    const limit = isAuthenticated && user?.plan === 'free' ? 3 : 2;
    if (data.dailyUsage >= limit) {
      setTimeout(() => setShowUpgradePrompt(true), 1000);
    }
    
    return true;
  };

  const getRemainingUsage = (): number => {
    // Premium users have unlimited usage
    if (isAuthenticated && user?.plan === 'premium') {
      return 999;
    }
    
    // Free authenticated users get 3 uses per day
    if (isAuthenticated && user?.plan === 'free') {
      const data = loadUsageData();
      return Math.max(0, 3 - data.dailyUsage);
    }
    
    // Non-authenticated users get 2 uses per day
    const data = loadUsageData();
    return Math.max(0, 2 - data.dailyUsage);
  };

  return (
    <UsageContext.Provider value={{
      canUseTools,
      incrementUsage,
      getRemainingUsage,
      showUpgradePrompt,
      setShowUpgradePrompt
    }}>
      {children}
    </UsageContext.Provider>
  );
};