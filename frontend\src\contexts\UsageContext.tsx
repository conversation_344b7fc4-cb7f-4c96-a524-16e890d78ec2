import React, { createContext, useContext, useState, useEffect } from 'react';
import { ApiService } from '../services/apiClient';
import { useAuth } from './AuthContext';

interface UsageData {
  dailyUsage: number;
  weeklyUsage: number;
  monthlyUsage: number;
  totalUsage: number;
  dailyLimit: number;
  weeklyLimit: number;
  monthlyLimit: number;
  subscriptionPlan: string;
  lastResetDate: string;
}

interface UsageHistory {
  id: string;
  toolName: string;
  fileSizeMB: number;
  processingTimeSeconds: number;
  inputFileCount: number;
  outputFileCount: number;
  success: boolean;
  createdAt: string;
}

interface UsageContextType {
  usage: UsageData | null;
  history: UsageHistory[];
  loading: boolean;
  refreshUsage: () => Promise<void>;
  getUsageHistory: (days?: number) => Promise<void>;
  getRemainingDaily: () => number;
  getRemainingWeekly: () => number;
  getRemainingMonthly: () => number;
  canProcess: () => boolean;
  getUsagePercentage: () => number;
  // Legacy methods for compatibility
  incrementUsage: () => boolean;
  getRemainingUsage: () => number;
  canUseTools: () => boolean;
}

const UsageContext = createContext<UsageContextType | undefined>(undefined);

export const useUsage = () => {
  const context = useContext(UsageContext);
  if (!context) {
    throw new Error('useUsage must be used within a UsageProvider');
  }
  return context;
};

interface UsageProviderProps {
  children: React.ReactNode;
}

export const UsageProvider: React.FC<UsageProviderProps> = ({ children }) => {
  const [usage, setUsage] = useState<UsageData | null>(null);
  const [history, setHistory] = useState<UsageHistory[]>([]);
  const [loading, setLoading] = useState(true);
  const { user, isAuthenticated } = useAuth();

  // Load usage data when user changes
  useEffect(() => {
    if (isAuthenticated && user) {
      refreshUsage();
      getUsageHistory(7); // Last 7 days
    } else {
      setUsage(null);
      setHistory([]);
      setLoading(false);
    }
  }, [isAuthenticated, user]);

  const refreshUsage = async (): Promise<void> => {
    if (!isAuthenticated) {
      setLoading(false);
      return;
    }

    try {
      setLoading(true);
      const response = await ApiService.getUsage();

      if (response.success) {
        // Map backend response to frontend usage structure
        const backendData = response.data;
        setUsage({
          dailyUsage: backendData.today.files,
          weeklyUsage: backendData.today.files, // Backend doesn't track weekly yet
          monthlyUsage: backendData.today.files, // Backend doesn't track monthly yet
          totalUsage: backendData.today.files,
          dailyLimit: backendData.limits.maxFilesPerDay,
          weeklyLimit: backendData.limits.maxFilesPerDay * 7, // Estimate
          monthlyLimit: backendData.limits.maxFilesPerDay * 30, // Estimate
          subscriptionPlan: backendData.subscriptionPlan,
          lastResetDate: new Date().toISOString()
        });
      } else {
        console.error('Failed to fetch usage:', response.error);
        // Set default usage for free plan
        setUsage({
          dailyUsage: 0,
          weeklyUsage: 0,
          monthlyUsage: 0,
          totalUsage: 0,
          dailyLimit: 10,
          weeklyLimit: 50,
          monthlyLimit: 200,
          subscriptionPlan: user?.subscriptionPlan || 'free',
          lastResetDate: new Date().toISOString()
        });
      }
    } catch (error) {
      console.error('Error fetching usage:', error);
      // Fallback to default limits
      setUsage({
        dailyUsage: 0,
        weeklyUsage: 0,
        monthlyUsage: 0,
        totalUsage: 0,
        dailyLimit: user?.subscriptionPlan === 'premium' ? 100 : 10,
        weeklyLimit: user?.subscriptionPlan === 'premium' ? 500 : 50,
        monthlyLimit: user?.subscriptionPlan === 'premium' ? 2000 : 200,
        subscriptionPlan: user?.subscriptionPlan || 'free',
        lastResetDate: new Date().toISOString()
      });
    } finally {
      setLoading(false);
    }
  };

  const getUsageHistory = async (days: number = 7): Promise<void> => {
    if (!isAuthenticated) return;

    try {
      const response = await ApiService.getUsageHistory(days);

      if (response.success) {
        setHistory(response.data.records || []);
      }
    } catch (error) {
      console.error('Error fetching usage history:', error);
    }
  };

  const getRemainingDaily = (): number => {
    if (!usage) return 0;
    return Math.max(0, usage.dailyLimit - usage.dailyUsage);
  };

  const getRemainingWeekly = (): number => {
    if (!usage) return 0;
    return Math.max(0, usage.weeklyLimit - usage.weeklyUsage);
  };

  const getRemainingMonthly = (): number => {
    if (!usage) return 0;
    return Math.max(0, usage.monthlyLimit - usage.monthlyUsage);
  };

  const canProcess = (): boolean => {
    if (!usage) return false;
    return getRemainingDaily() > 0 && getRemainingWeekly() > 0 && getRemainingMonthly() > 0;
  };

  const getUsagePercentage = (): number => {
    if (!usage) return 0;
    return Math.round((usage.dailyUsage / usage.dailyLimit) * 100);
  };

  // Legacy methods for compatibility with existing components
  const incrementUsage = (): boolean => {
    // This is now handled by the backend, so just return true
    // The actual increment happens during PDF processing
    return canProcess();
  };

  const getRemainingUsage = (): number => {
    return getRemainingDaily();
  };

  const canUseTools = (): boolean => {
    return canProcess();
  };

  // For non-authenticated users, provide limited functionality
  if (!isAuthenticated) {
    return (
      <UsageContext.Provider value={{
        usage: null,
        history: [],
        loading: false,
        refreshUsage: async () => {},
        getUsageHistory: async () => {},
        getRemainingDaily: () => 3, // Allow 3 free uses for non-authenticated
        getRemainingWeekly: () => 10,
        getRemainingMonthly: () => 20,
        canProcess: () => true,
        getUsagePercentage: () => 0,
        incrementUsage: () => true,
        getRemainingUsage: () => 3,
        canUseTools: () => true
      }}>
        {children}
      </UsageContext.Provider>
    );
  }

  return (
    <UsageContext.Provider value={{
      usage,
      history,
      loading,
      refreshUsage,
      getUsageHistory,
      getRemainingDaily,
      getRemainingWeekly,
      getRemainingMonthly,
      canProcess,
      getUsagePercentage,
      incrementUsage,
      getRemainingUsage,
      canUseTools
    }}>
      {children}
    </UsageContext.Provider>
  );
};

export default UsageContext;