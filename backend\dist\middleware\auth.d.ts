import { Request, Response, NextFunction } from 'express';
declare global {
    namespace Express {
        interface Request {
            user?: {
                id: string;
                email: string;
                subscriptionPlan: string;
                isVerified: boolean;
            };
        }
    }
}
export interface JWTPayload {
    userId: string;
    email: string;
    subscriptionPlan: string;
    isVerified: boolean;
    iat?: number;
    exp?: number;
}
export declare class AuthService {
    private static readonly JWT_SECRET;
    private static readonly JWT_EXPIRES_IN;
    private static readonly REFRESH_TOKEN_EXPIRES_IN;
    static generateTokens(payload: Omit<JWTPayload, 'iat' | 'exp'>): {
        accessToken: string;
        refreshToken: string;
    };
    static verifyToken(token: string): JWTPayload;
    static extractTokenFromHeader(authHeader?: string): string;
}
export declare const authenticate: (req: Request, _res: Response, next: NextFunction) => void;
export declare const optionalAuth: (req: Request, _res: Response, next: NextFunction) => void;
export declare const authorize: (requiredPlans?: string[]) => (req: Request, _res: Response, next: NextFunction) => void;
export declare const requireVerification: (req: Request, _res: Response, next: NextFunction) => void;
export declare const userRateLimit: (maxRequests: number, windowMs: number) => (req: Request, res: Response, next: NextFunction) => void;
//# sourceMappingURL=auth.d.ts.map