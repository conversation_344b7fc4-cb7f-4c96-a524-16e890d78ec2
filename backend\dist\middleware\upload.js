"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.FileManager = exports.upload = void 0;
const multer_1 = __importDefault(require("multer"));
const path_1 = __importDefault(require("path"));
const promises_1 = __importDefault(require("fs/promises"));
const uuid_1 = require("uuid");
const errorHandler_1 = require("./errorHandler");
const logger_1 = require("../utils/logger");
const UPLOAD_DIR = path_1.default.join(__dirname, '../../uploads');
const TEMP_DIR = path_1.default.join(__dirname, '../../temp');
const PROCESSED_DIR = path_1.default.join(__dirname, '../../processed');
async function ensureDirectories() {
    try {
        await promises_1.default.mkdir(UPLOAD_DIR, { recursive: true });
        await promises_1.default.mkdir(TEMP_DIR, { recursive: true });
        await promises_1.default.mkdir(PROCESSED_DIR, { recursive: true });
    }
    catch (error) {
        logger_1.logger.error('Failed to create upload directories', { error });
    }
}
ensureDirectories();
const fileFilter = (req, file, cb) => {
    const allowedMimeTypes = [
        'application/pdf',
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        'application/vnd.ms-excel',
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        'application/msword',
        'application/vnd.openxmlformats-officedocument.presentationml.presentation',
        'application/vnd.ms-powerpoint',
        'text/html',
        'image/jpeg',
        'image/png',
        'image/bmp',
        'image/tiff',
        'text/csv'
    ];
    if (allowedMimeTypes.includes(file.mimetype)) {
        cb(null, true);
    }
    else {
        cb(new errorHandler_1.ValidationError(`File type ${file.mimetype} is not allowed`));
    }
};
const storage = multer_1.default.memoryStorage();
exports.upload = (0, multer_1.default)({
    storage,
    fileFilter,
    limits: {
        fileSize: 50 * 1024 * 1024,
        files: 10
    }
});
class FileManager {
    static async saveUploadedFiles(files, userId) {
        const savedPaths = [];
        const userDir = userId ? path_1.default.join(UPLOAD_DIR, userId) : UPLOAD_DIR;
        try {
            await promises_1.default.mkdir(userDir, { recursive: true });
            for (const file of files) {
                const fileId = (0, uuid_1.v4)();
                const fileExtension = path_1.default.extname(file.originalname);
                const fileName = `${fileId}${fileExtension}`;
                const filePath = path_1.default.join(userDir, fileName);
                await promises_1.default.writeFile(filePath, file.buffer);
                savedPaths.push(filePath);
                logger_1.logger.info('File saved', {
                    originalName: file.originalname,
                    savedPath: filePath,
                    size: file.size,
                    userId
                });
            }
            return savedPaths;
        }
        catch (error) {
            await this.cleanupFiles(savedPaths);
            throw new Error(`Failed to save uploaded files: ${error}`);
        }
    }
    static async createOutputDirectory(userId) {
        const jobId = (0, uuid_1.v4)();
        const outputDir = userId
            ? path_1.default.join(PROCESSED_DIR, userId, jobId)
            : path_1.default.join(PROCESSED_DIR, jobId);
        try {
            await promises_1.default.mkdir(outputDir, { recursive: true });
            return outputDir;
        }
        catch (error) {
            throw new Error(`Failed to create output directory: ${error}`);
        }
    }
    static async cleanupFiles(filePaths) {
        const cleanupPromises = filePaths.map(async (filePath) => {
            try {
                await promises_1.default.unlink(filePath);
                logger_1.logger.debug('File cleaned up', { filePath });
            }
            catch (error) {
                logger_1.logger.warn('Failed to cleanup file', { filePath, error });
            }
        });
        await Promise.allSettled(cleanupPromises);
    }
    static async cleanupDirectory(dirPath) {
        try {
            const files = await promises_1.default.readdir(dirPath);
            const filePaths = files.map(file => path_1.default.join(dirPath, file));
            await this.cleanupFiles(filePaths);
            await promises_1.default.rmdir(dirPath);
            logger_1.logger.debug('Directory cleaned up', { dirPath });
        }
        catch (error) {
            logger_1.logger.warn('Failed to cleanup directory', { dirPath, error });
        }
    }
    static async getFileInfo(filePath) {
        try {
            const stats = await promises_1.default.stat(filePath);
            return {
                exists: true,
                size: stats.size,
                mtime: stats.mtime
            };
        }
        catch (error) {
            return { exists: false };
        }
    }
    static async scheduleCleanup(filePaths, delayMs = 3600000) {
        setTimeout(async () => {
            await this.cleanupFiles(filePaths);
        }, delayMs);
    }
    static async cleanupOldFiles(maxAgeMs = 24 * 60 * 60 * 1000) {
        const directories = [UPLOAD_DIR, TEMP_DIR, PROCESSED_DIR];
        for (const dir of directories) {
            try {
                await this.cleanupOldFilesInDirectory(dir, maxAgeMs);
            }
            catch (error) {
                logger_1.logger.error('Failed to cleanup old files in directory', { dir, error });
            }
        }
    }
    static async cleanupOldFilesInDirectory(dirPath, maxAgeMs) {
        try {
            const entries = await promises_1.default.readdir(dirPath, { withFileTypes: true });
            const now = Date.now();
            for (const entry of entries) {
                const fullPath = path_1.default.join(dirPath, entry.name);
                try {
                    const stats = await promises_1.default.stat(fullPath);
                    const age = now - stats.mtime.getTime();
                    if (age > maxAgeMs) {
                        if (entry.isDirectory()) {
                            await this.cleanupDirectory(fullPath);
                        }
                        else {
                            await promises_1.default.unlink(fullPath);
                        }
                        logger_1.logger.debug('Old file/directory cleaned up', { path: fullPath, age });
                    }
                }
                catch (error) {
                    logger_1.logger.warn('Failed to process file/directory for cleanup', { path: fullPath, error });
                }
            }
        }
        catch (error) {
            logger_1.logger.error('Failed to read directory for cleanup', { dirPath, error });
        }
    }
    static getUploadPath() {
        return UPLOAD_DIR;
    }
    static getTempPath() {
        return TEMP_DIR;
    }
    static getProcessedPath() {
        return PROCESSED_DIR;
    }
}
exports.FileManager = FileManager;
setInterval(() => {
    FileManager.cleanupOldFiles();
}, 6 * 60 * 60 * 1000);
//# sourceMappingURL=upload.js.map