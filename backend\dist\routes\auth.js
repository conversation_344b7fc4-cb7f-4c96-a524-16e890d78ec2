"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const bcryptjs_1 = __importDefault(require("bcryptjs"));
const errorHandler_1 = require("../middleware/errorHandler");
const validation_1 = require("../middleware/validation");
const auth_1 = require("../middleware/auth");
const errorHandler_2 = require("../middleware/errorHandler");
const logger_1 = require("../utils/logger");
const User_1 = require("../models/User");
const router = express_1.default.Router();
router.post('/register', (0, validation_1.validate)(validation_1.schemas.userRegistration), (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const { email, password, firstName, lastName } = req.body;
    const existingUser = await User_1.User.findOne({
        where: { email: email.toLowerCase() }
    });
    if (existingUser) {
        throw new errorHandler_2.ValidationError('User with this email already exists');
    }
    const saltRounds = 12;
    const passwordHash = await bcryptjs_1.default.hash(password, saltRounds);
    const user = await User_1.User.create({
        email: email.toLowerCase(),
        password_hash: passwordHash,
        first_name: firstName,
        last_name: lastName,
        subscription_plan: 'free',
        is_verified: false,
    });
    const { accessToken, refreshToken } = auth_1.AuthService.generateTokens({
        userId: user.id,
        email: user.email,
        subscriptionPlan: user.subscription_plan,
        isVerified: user.is_verified
    });
    logger_1.logger.info('User registered successfully', {
        userId: user.id,
        email: user.email
    });
    res.status(201).json({
        success: true,
        data: {
            user: {
                id: user.id,
                email: user.email,
                firstName: user.first_name,
                lastName: user.last_name,
                subscriptionPlan: user.subscription_plan,
                isVerified: user.is_verified
            },
            tokens: {
                accessToken,
                refreshToken
            }
        },
        message: 'User registered successfully'
    });
}));
router.post('/login', (0, validation_1.validate)(validation_1.schemas.userLogin), (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const { email, password } = req.body;
    const user = await User_1.User.findOne({
        where: { email: email.toLowerCase() }
    });
    if (!user) {
        throw new errorHandler_2.AuthenticationError('Invalid email or password');
    }
    const isValidPassword = await bcryptjs_1.default.compare(password, user.password_hash);
    if (!isValidPassword) {
        throw new errorHandler_2.AuthenticationError('Invalid email or password');
    }
    user.last_login_at = new Date();
    await user.save();
    const { accessToken, refreshToken } = auth_1.AuthService.generateTokens({
        userId: user.id,
        email: user.email,
        subscriptionPlan: user.subscription_plan,
        isVerified: user.is_verified
    });
    logger_1.logger.info('User logged in successfully', {
        userId: user.id,
        email: user.email
    });
    res.json({
        success: true,
        data: {
            user: {
                id: user.id,
                email: user.email,
                firstName: user.first_name,
                lastName: user.last_name,
                subscriptionPlan: user.subscription_plan,
                isVerified: user.is_verified,
                lastLoginAt: user.last_login_at
            },
            tokens: {
                accessToken,
                refreshToken
            }
        },
        message: 'Login successful'
    });
}));
router.post('/refresh', (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const { refreshToken } = req.body;
    if (!refreshToken) {
        throw new errorHandler_2.ValidationError('Refresh token is required');
    }
    try {
        const payload = auth_1.AuthService.verifyToken(refreshToken);
        const user = await User_1.User.findByPk(payload.userId);
        if (!user) {
            throw new errorHandler_2.AuthenticationError('User not found');
        }
        const tokens = auth_1.AuthService.generateTokens({
            userId: user.id,
            email: user.email,
            subscriptionPlan: user.subscription_plan,
            isVerified: user.is_verified
        });
        res.json({
            success: true,
            data: {
                tokens
            },
            message: 'Tokens refreshed successfully'
        });
    }
    catch (error) {
        throw new errorHandler_2.AuthenticationError('Invalid refresh token');
    }
}));
router.get('/profile', auth_1.authenticate, (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const user = await User_1.User.findByPk(req.user.id);
    if (!user) {
        throw new errorHandler_2.AuthenticationError('User not found');
    }
    res.json({
        success: true,
        data: {
            user: {
                id: user.id,
                email: user.email,
                firstName: user.first_name,
                lastName: user.last_name,
                subscriptionPlan: user.subscription_plan,
                isVerified: user.is_verified,
                createdAt: user.created_at,
                lastLoginAt: user.last_login_at
            }
        }
    });
}));
router.put('/profile', auth_1.authenticate, (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const { firstName, lastName } = req.body;
    const user = await User_1.User.findByPk(req.user.id);
    if (!user) {
        throw new errorHandler_2.AuthenticationError('User not found');
    }
    if (firstName)
        user.first_name = firstName;
    if (lastName)
        user.last_name = lastName;
    await user.save();
    logger_1.logger.info('User profile updated', {
        userId: user.id,
        email: user.email
    });
    res.json({
        success: true,
        data: {
            user: {
                id: user.id,
                email: user.email,
                firstName: user.first_name,
                lastName: user.last_name,
                subscriptionPlan: user.subscription_plan,
                isVerified: user.is_verified
            }
        },
        message: 'Profile updated successfully'
    });
}));
router.put('/password', auth_1.authenticate, (0, errorHandler_1.asyncHandler)(async (req, res) => {
    const { currentPassword, newPassword } = req.body;
    const user = await User_1.User.findByPk(req.user.id);
    if (!user) {
        throw new errorHandler_2.AuthenticationError('User not found');
    }
    const isValidPassword = await bcryptjs_1.default.compare(currentPassword, user.password_hash);
    if (!isValidPassword) {
        throw new errorHandler_2.ValidationError('Current password is incorrect');
    }
    const saltRounds = 12;
    user.password_hash = await bcryptjs_1.default.hash(newPassword, saltRounds);
    await user.save();
    logger_1.logger.info('User password changed', {
        userId: user.id,
        email: user.email
    });
    res.json({
        success: true,
        message: 'Password changed successfully'
    });
}));
router.post('/logout', auth_1.authenticate, (0, errorHandler_1.asyncHandler)(async (req, res) => {
    logger_1.logger.info('User logged out', {
        userId: req.user.id,
        email: req.user.email
    });
    res.json({
        success: true,
        message: 'Logged out successfully'
    });
}));
exports.default = router;
//# sourceMappingURL=auth.js.map