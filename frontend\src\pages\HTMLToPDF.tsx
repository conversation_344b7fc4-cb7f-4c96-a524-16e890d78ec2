import React, { useState } from 'react';
import { Globe, Download, ArrowRight } from 'lucide-react';
import ToolLayout from '../components/ToolLayout';

const HTMLToPDF = () => {
  const [url, setUrl] = useState('');
  const [isProcessing, setIsProcessing] = useState(false);
  const [pageSize, setPageSize] = useState<'A4' | 'Letter' | 'Legal'>('A4');

  const handleConvert = () => {
    if (!url) return;
    
    setIsProcessing(true);
    // Simulate processing
    setTimeout(() => {
      setIsProcessing(false);
      alert('Page web convertie en PDF avec succès!');
    }, 3000);
  };

  return (
    <ToolLayout
      title="HTML en PDF"
      description="Convertissez des pages web HTML en PDF. Copiez-collez l'URL de la page qui vous intéresse"
      icon={<Globe className="w-8 h-8" />}
      color="from-slate-500 to-slate-600"
    >
      <div className="space-y-6">
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-slate-700 mb-2">
              URL de la page web
            </label>
            <input
              type="url"
              placeholder="https://example.com"
              value={url}
              onChange={(e) => setUrl(e.target.value)}
              className="w-full p-4 border border-slate-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-slate-500 text-lg"
            />
          </div>
        </div>

        {url && (
          <div className="bg-slate-50 p-6 rounded-xl">
            <h3 className="text-lg font-semibold text-slate-700 mb-4">
              Options de conversion
            </h3>
            
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-slate-700 mb-2">
                  Format de page
                </label>
                <div className="space-y-2">
                  <label className="flex items-center space-x-3">
                    <input
                      type="radio"
                      name="pageSize"
                      value="A4"
                      checked={pageSize === 'A4'}
                      onChange={(e) => setPageSize(e.target.value as 'A4')}
                      className="text-slate-600"
                    />
                    <span className="text-slate-700">A4</span>
                  </label>
                  <label className="flex items-center space-x-3">
                    <input
                      type="radio"
                      name="pageSize"
                      value="Letter"
                      checked={pageSize === 'Letter'}
                      onChange={(e) => setPageSize(e.target.value as 'Letter')}
                      className="text-slate-600"
                    />
                    <span className="text-slate-700">Letter</span>
                  </label>
                  <label className="flex items-center space-x-3">
                    <input
                      type="radio"
                      name="pageSize"
                      value="Legal"
                      checked={pageSize === 'Legal'}
                      onChange={(e) => setPageSize(e.target.value as 'Legal')}
                      className="text-slate-600"
                    />
                    <span className="text-slate-700">Legal</span>
                  </label>
                </div>
              </div>

              <div className="flex items-center space-x-3">
                <input
                  type="checkbox"
                  id="include-background"
                  defaultChecked
                  className="text-slate-600 rounded"
                />
                <label htmlFor="include-background" className="text-slate-700">
                  Inclure les arrière-plans
                </label>
              </div>

              <div className="flex items-center space-x-3">
                <input
                  type="checkbox"
                  id="include-images"
                  defaultChecked
                  className="text-slate-600 rounded"
                />
                <label htmlFor="include-images" className="text-slate-700">
                  Inclure les images
                </label>
              </div>

              <div className="flex items-center space-x-3">
                <input
                  type="checkbox"
                  id="preserve-links"
                  defaultChecked
                  className="text-slate-600 rounded"
                />
                <label htmlFor="preserve-links" className="text-slate-700">
                  Préserver les liens
                </label>
              </div>
            </div>
          </div>
        )}

        {url && (
          <div className="flex justify-center">
            <button
              onClick={handleConvert}
              disabled={isProcessing}
              className="bg-gradient-to-r from-slate-600 to-gray-600 text-white px-8 py-4 rounded-xl text-lg font-medium hover:shadow-xl transition-all duration-200 transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2"
            >
              {isProcessing ? (
                <>
                  <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white"></div>
                  <span>Conversion en cours...</span>
                </>
              ) : (
                <>
                  <span>Convertir en PDF</span>
                  <ArrowRight className="w-5 h-5" />
                </>
              )}
            </button>
          </div>
        )}
      </div>
    </ToolLayout>
  );
};

export default HTMLToPDF;