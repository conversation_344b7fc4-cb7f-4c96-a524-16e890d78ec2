import multer from 'multer';
export declare const upload: multer.Multer;
export declare class FileManager {
    static saveUploadedFiles(files: Express.Multer.File[], userId?: string): Promise<string[]>;
    static createOutputDirectory(userId?: string): Promise<string>;
    static cleanupFiles(filePaths: string[]): Promise<void>;
    static cleanupDirectory(dirPath: string): Promise<void>;
    static getFileInfo(filePath: string): Promise<{
        exists: boolean;
        size?: number;
        mtime?: Date;
    }>;
    static scheduleCleanup(filePaths: string[], delayMs?: number): Promise<void>;
    static cleanupOldFiles(maxAgeMs?: number): Promise<void>;
    private static cleanupOldFilesInDirectory;
    static getUploadPath(): string;
    static getTempPath(): string;
    static getProcessedPath(): string;
}
//# sourceMappingURL=upload.d.ts.map