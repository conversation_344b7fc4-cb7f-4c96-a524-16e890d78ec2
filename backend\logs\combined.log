{"database":"pdf","error":"Access denied for user 'root'@'localhost' (using password: NO)","host":"localhost","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mUnable to connect to database\u001b[39m","port":3306,"timestamp":"2025-07-12 14:06:49:649"}
{"database":"pdf","error":"Access denied for user 'root'@'localhost' (using password: NO)","host":"localhost","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mUnable to connect to database\u001b[39m","port":3306,"timestamp":"2025-07-12 14:07:29:729"}
{"database":"pdf","error":"Access denied for user 'root'@'localhost' (using password: NO)","host":"localhost","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mUnable to connect to database\u001b[39m","port":3306,"timestamp":"2025-07-12 14:08:00:80"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT 1+1 AS result","timestamp":"2025-07-12 14:08:19:819"}
{"database":"pdf","host":"127.0.0.1","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mDatabase connection established successfully\u001b[39m","port":3306,"timestamp":"2025-07-12 14:08:19:819"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT TABLE_NAME FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_TYPE = 'BASE TABLE' AND TABLE_NAME = 'users' AND TABLE_SCHEMA = 'pdf'","timestamp":"2025-07-12 14:08:19:819"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): CREATE TABLE IF NOT EXISTS `users` (`id` CHAR(36) BINARY , `email` VARCHAR(255) NOT NULL UNIQUE, `password_hash` VARCHAR(255) NOT NULL, `first_name` VARCHAR(100) NOT NULL, `last_name` VARCHAR(100) NOT NULL, `subscription_plan` ENUM('free', 'premium', 'enterprise') NOT NULL DEFAULT 'free', `is_verified` TINYINT(1) NOT NULL DEFAULT false, `verification_token` VARCHAR(255), `reset_password_token` VARCHAR(255), `reset_password_expires` DATETIME, `last_login_at` DATETIME, `created_at` DATETIME NOT NULL, `updated_at` DATETIME NOT NULL, PRIMARY KEY (`id`)) ENGINE=InnoDB;","timestamp":"2025-07-12 14:08:19:819"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SHOW INDEX FROM `users`","timestamp":"2025-07-12 14:08:20:820"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` ADD UNIQUE INDEX `users_email` (`email`)","timestamp":"2025-07-12 14:08:20:820"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` ADD INDEX `users_subscription_plan` (`subscription_plan`)","timestamp":"2025-07-12 14:08:21:821"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` ADD INDEX `users_is_verified` (`is_verified`)","timestamp":"2025-07-12 14:08:21:821"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` ADD INDEX `users_verification_token` (`verification_token`)","timestamp":"2025-07-12 14:08:21:821"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` ADD INDEX `users_reset_password_token` (`reset_password_token`)","timestamp":"2025-07-12 14:08:22:822"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT TABLE_NAME FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_TYPE = 'BASE TABLE' AND TABLE_NAME = 'usage_records' AND TABLE_SCHEMA = 'pdf'","timestamp":"2025-07-12 14:08:22:822"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): CREATE TABLE IF NOT EXISTS `usage_records` (`id` CHAR(36) BINARY , `user_id` CHAR(36) BINARY NOT NULL, `tool_name` VARCHAR(100) NOT NULL, `file_size_mb` DECIMAL(10,2) NOT NULL, `processing_time_seconds` INTEGER NOT NULL, `input_file_count` INTEGER NOT NULL, `output_file_count` INTEGER NOT NULL DEFAULT 0, `success` TINYINT(1) NOT NULL DEFAULT true, `error_message` TEXT, `parameters` JSON, `created_at` DATETIME NOT NULL, `updated_at` DATETIME NOT NULL, PRIMARY KEY (`id`), FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE ON UPDATE CASCADE) ENGINE=InnoDB;","timestamp":"2025-07-12 14:08:22:822"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SHOW INDEX FROM `usage_records`","timestamp":"2025-07-12 14:08:22:822"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `usage_records` ADD INDEX `usage_records_user_id` (`user_id`)","timestamp":"2025-07-12 14:08:22:822"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `usage_records` ADD INDEX `usage_records_tool_name` (`tool_name`)","timestamp":"2025-07-12 14:08:23:823"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `usage_records` ADD INDEX `usage_records_success` (`success`)","timestamp":"2025-07-12 14:08:23:823"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `usage_records` ADD INDEX `usage_records_created_at` (`created_at`)","timestamp":"2025-07-12 14:08:23:823"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `usage_records` ADD INDEX `usage_records_user_id_created_at` (`user_id`, `created_at`)","timestamp":"2025-07-12 14:08:24:824"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `usage_records` ADD INDEX `usage_records_user_id_tool_name` (`user_id`, `tool_name`)","timestamp":"2025-07-12 14:08:24:824"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mDatabase initialized successfully\u001b[39m","timestamp":"2025-07-12 14:08:24:824"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mServer running on port 3001\u001b[39m","timestamp":"2025-07-12 14:08:24:824"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mEnvironment: development\u001b[39m","timestamp":"2025-07-12 14:08:24:824"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mDatabase connected and synchronized\u001b[39m","timestamp":"2025-07-12 14:08:24:824"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT 1+1 AS result","timestamp":"2025-07-12 14:11:36:1136"}
{"database":"pdf","host":"127.0.0.1","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mDatabase connection established successfully\u001b[39m","port":3306,"timestamp":"2025-07-12 14:11:36:1136"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT TABLE_NAME FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_TYPE = 'BASE TABLE' AND TABLE_NAME = 'users' AND TABLE_SCHEMA = 'pdf'","timestamp":"2025-07-12 14:11:36:1136"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SHOW FULL COLUMNS FROM `users`;","timestamp":"2025-07-12 14:11:36:1136"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT CONSTRAINT_NAME as constraint_name,CONSTRAINT_NAME as constraintName,CONSTRAINT_SCHEMA as constraintSchema,CONSTRAINT_SCHEMA as constraintCatalog,TABLE_NAME as tableName,TABLE_SCHEMA as tableSchema,TABLE_SCHEMA as tableCatalog,COLUMN_NAME as columnName,REFERENCED_TABLE_SCHEMA as referencedTableSchema,REFERENCED_TABLE_SCHEMA as referencedTableCatalog,REFERENCED_TABLE_NAME as referencedTableName,REFERENCED_COLUMN_NAME as referencedColumnName FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE where TABLE_NAME = 'users' AND CONSTRAINT_NAME!='PRIMARY' AND CONSTRAINT_SCHEMA='pdf' AND REFERENCED_TABLE_NAME IS NOT NULL;","timestamp":"2025-07-12 14:11:36:1136"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `email` `email` VARCHAR(255) NOT NULL UNIQUE;","timestamp":"2025-07-12 14:11:36:1136"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `password_hash` `password_hash` VARCHAR(255) NOT NULL;","timestamp":"2025-07-12 14:11:36:1136"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `first_name` `first_name` VARCHAR(100) NOT NULL;","timestamp":"2025-07-12 14:11:36:1136"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `last_name` `last_name` VARCHAR(100) NOT NULL;","timestamp":"2025-07-12 14:11:36:1136"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `subscription_plan` `subscription_plan` ENUM('free', 'premium', 'enterprise') NOT NULL DEFAULT 'free';","timestamp":"2025-07-12 14:11:37:1137"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `is_verified` `is_verified` TINYINT(1) NOT NULL DEFAULT false;","timestamp":"2025-07-12 14:11:37:1137"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `verification_token` `verification_token` VARCHAR(255);","timestamp":"2025-07-12 14:11:38:1138"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `reset_password_token` `reset_password_token` VARCHAR(255);","timestamp":"2025-07-12 14:11:38:1138"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `reset_password_expires` `reset_password_expires` DATETIME;","timestamp":"2025-07-12 14:11:39:1139"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `last_login_at` `last_login_at` DATETIME;","timestamp":"2025-07-12 14:11:39:1139"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `created_at` `created_at` DATETIME NOT NULL;","timestamp":"2025-07-12 14:11:39:1139"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `updated_at` `updated_at` DATETIME NOT NULL;","timestamp":"2025-07-12 14:11:39:1139"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SHOW INDEX FROM `users`","timestamp":"2025-07-12 14:11:40:1140"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT TABLE_NAME FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_TYPE = 'BASE TABLE' AND TABLE_NAME = 'usage_records' AND TABLE_SCHEMA = 'pdf'","timestamp":"2025-07-12 14:11:40:1140"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SHOW FULL COLUMNS FROM `usage_records`;","timestamp":"2025-07-12 14:11:40:1140"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT CONSTRAINT_NAME as constraint_name,CONSTRAINT_NAME as constraintName,CONSTRAINT_SCHEMA as constraintSchema,CONSTRAINT_SCHEMA as constraintCatalog,TABLE_NAME as tableName,TABLE_SCHEMA as tableSchema,TABLE_SCHEMA as tableCatalog,COLUMN_NAME as columnName,REFERENCED_TABLE_SCHEMA as referencedTableSchema,REFERENCED_TABLE_SCHEMA as referencedTableCatalog,REFERENCED_TABLE_NAME as referencedTableName,REFERENCED_COLUMN_NAME as referencedColumnName FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE where TABLE_NAME = 'usage_records' AND CONSTRAINT_NAME!='PRIMARY' AND CONSTRAINT_SCHEMA='pdf' AND REFERENCED_TABLE_NAME IS NOT NULL;","timestamp":"2025-07-12 14:11:40:1140"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT CONSTRAINT_CATALOG AS constraintCatalog, CONSTRAINT_NAME AS constraintName, CONSTRAINT_SCHEMA AS constraintSchema, CONSTRAINT_TYPE AS constraintType, TABLE_NAME AS tableName, TABLE_SCHEMA AS tableSchema from INFORMATION_SCHEMA.TABLE_CONSTRAINTS WHERE table_name='usage_records' AND constraint_name = 'usage_records_ibfk_1' AND TABLE_SCHEMA = 'pdf';","timestamp":"2025-07-12 14:11:40:1140"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `usage_records` DROP FOREIGN KEY `usage_records_ibfk_1`;","timestamp":"2025-07-12 14:11:40:1140"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `usage_records` ADD FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE ON UPDATE CASCADE;","timestamp":"2025-07-12 14:11:40:1140"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `usage_records` CHANGE `tool_name` `tool_name` VARCHAR(100) NOT NULL;","timestamp":"2025-07-12 14:11:42:1142"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `usage_records` CHANGE `file_size_mb` `file_size_mb` DECIMAL(10,2) NOT NULL;","timestamp":"2025-07-12 14:11:42:1142"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `usage_records` CHANGE `processing_time_seconds` `processing_time_seconds` INTEGER NOT NULL;","timestamp":"2025-07-12 14:11:42:1142"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `usage_records` CHANGE `input_file_count` `input_file_count` INTEGER NOT NULL;","timestamp":"2025-07-12 14:11:42:1142"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `usage_records` CHANGE `output_file_count` `output_file_count` INTEGER NOT NULL DEFAULT 0;","timestamp":"2025-07-12 14:11:43:1143"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `usage_records` CHANGE `success` `success` TINYINT(1) NOT NULL DEFAULT true;","timestamp":"2025-07-12 14:11:43:1143"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `usage_records` CHANGE `error_message` `error_message` TEXT;","timestamp":"2025-07-12 14:11:43:1143"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `usage_records` CHANGE `parameters` `parameters` JSON;","timestamp":"2025-07-12 14:11:43:1143"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `usage_records` CHANGE `created_at` `created_at` DATETIME NOT NULL;","timestamp":"2025-07-12 14:11:43:1143"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `usage_records` CHANGE `updated_at` `updated_at` DATETIME NOT NULL;","timestamp":"2025-07-12 14:11:43:1143"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SHOW INDEX FROM `usage_records`","timestamp":"2025-07-12 14:11:43:1143"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mDatabase initialized successfully\u001b[39m","timestamp":"2025-07-12 14:11:44:1144"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mServer running on port 3001\u001b[39m","timestamp":"2025-07-12 14:11:44:1144"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mEnvironment: development\u001b[39m","timestamp":"2025-07-12 14:11:44:1144"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mDatabase connected and synchronized\u001b[39m","timestamp":"2025-07-12 14:11:44:1144"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT 1+1 AS result","timestamp":"2025-07-12 14:12:12:1212"}
{"database":"pdf","host":"127.0.0.1","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mDatabase connection established successfully\u001b[39m","port":3306,"timestamp":"2025-07-12 14:12:12:1212"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT TABLE_NAME FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_TYPE = 'BASE TABLE' AND TABLE_NAME = 'users' AND TABLE_SCHEMA = 'pdf'","timestamp":"2025-07-12 14:12:12:1212"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SHOW FULL COLUMNS FROM `users`;","timestamp":"2025-07-12 14:12:12:1212"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT CONSTRAINT_NAME as constraint_name,CONSTRAINT_NAME as constraintName,CONSTRAINT_SCHEMA as constraintSchema,CONSTRAINT_SCHEMA as constraintCatalog,TABLE_NAME as tableName,TABLE_SCHEMA as tableSchema,TABLE_SCHEMA as tableCatalog,COLUMN_NAME as columnName,REFERENCED_TABLE_SCHEMA as referencedTableSchema,REFERENCED_TABLE_SCHEMA as referencedTableCatalog,REFERENCED_TABLE_NAME as referencedTableName,REFERENCED_COLUMN_NAME as referencedColumnName FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE where TABLE_NAME = 'users' AND CONSTRAINT_NAME!='PRIMARY' AND CONSTRAINT_SCHEMA='pdf' AND REFERENCED_TABLE_NAME IS NOT NULL;","timestamp":"2025-07-12 14:12:12:1212"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `email` `email` VARCHAR(255) NOT NULL UNIQUE;","timestamp":"2025-07-12 14:12:12:1212"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `password_hash` `password_hash` VARCHAR(255) NOT NULL;","timestamp":"2025-07-12 14:12:12:1212"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `first_name` `first_name` VARCHAR(100) NOT NULL;","timestamp":"2025-07-12 14:12:12:1212"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `last_name` `last_name` VARCHAR(100) NOT NULL;","timestamp":"2025-07-12 14:12:13:1213"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `subscription_plan` `subscription_plan` ENUM('free', 'premium', 'enterprise') NOT NULL DEFAULT 'free';","timestamp":"2025-07-12 14:12:14:1214"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `is_verified` `is_verified` TINYINT(1) NOT NULL DEFAULT false;","timestamp":"2025-07-12 14:12:14:1214"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `verification_token` `verification_token` VARCHAR(255);","timestamp":"2025-07-12 14:12:14:1214"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `reset_password_token` `reset_password_token` VARCHAR(255);","timestamp":"2025-07-12 14:12:14:1214"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `reset_password_expires` `reset_password_expires` DATETIME;","timestamp":"2025-07-12 14:12:14:1214"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `last_login_at` `last_login_at` DATETIME;","timestamp":"2025-07-12 14:12:15:1215"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `created_at` `created_at` DATETIME NOT NULL;","timestamp":"2025-07-12 14:12:15:1215"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `updated_at` `updated_at` DATETIME NOT NULL;","timestamp":"2025-07-12 14:12:15:1215"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SHOW INDEX FROM `users`","timestamp":"2025-07-12 14:12:15:1215"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT TABLE_NAME FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_TYPE = 'BASE TABLE' AND TABLE_NAME = 'usage_records' AND TABLE_SCHEMA = 'pdf'","timestamp":"2025-07-12 14:12:15:1215"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SHOW FULL COLUMNS FROM `usage_records`;","timestamp":"2025-07-12 14:12:15:1215"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT CONSTRAINT_NAME as constraint_name,CONSTRAINT_NAME as constraintName,CONSTRAINT_SCHEMA as constraintSchema,CONSTRAINT_SCHEMA as constraintCatalog,TABLE_NAME as tableName,TABLE_SCHEMA as tableSchema,TABLE_SCHEMA as tableCatalog,COLUMN_NAME as columnName,REFERENCED_TABLE_SCHEMA as referencedTableSchema,REFERENCED_TABLE_SCHEMA as referencedTableCatalog,REFERENCED_TABLE_NAME as referencedTableName,REFERENCED_COLUMN_NAME as referencedColumnName FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE where TABLE_NAME = 'usage_records' AND CONSTRAINT_NAME!='PRIMARY' AND CONSTRAINT_SCHEMA='pdf' AND REFERENCED_TABLE_NAME IS NOT NULL;","timestamp":"2025-07-12 14:12:15:1215"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT CONSTRAINT_CATALOG AS constraintCatalog, CONSTRAINT_NAME AS constraintName, CONSTRAINT_SCHEMA AS constraintSchema, CONSTRAINT_TYPE AS constraintType, TABLE_NAME AS tableName, TABLE_SCHEMA AS tableSchema from INFORMATION_SCHEMA.TABLE_CONSTRAINTS WHERE table_name='usage_records' AND constraint_name = 'usage_records_ibfk_1' AND TABLE_SCHEMA = 'pdf';","timestamp":"2025-07-12 14:12:15:1215"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `usage_records` DROP FOREIGN KEY `usage_records_ibfk_1`;","timestamp":"2025-07-12 14:12:15:1215"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `usage_records` ADD FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE ON UPDATE CASCADE;","timestamp":"2025-07-12 14:12:15:1215"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `usage_records` CHANGE `tool_name` `tool_name` VARCHAR(100) NOT NULL;","timestamp":"2025-07-12 14:12:17:1217"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `usage_records` CHANGE `file_size_mb` `file_size_mb` DECIMAL(10,2) NOT NULL;","timestamp":"2025-07-12 14:12:18:1218"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `usage_records` CHANGE `processing_time_seconds` `processing_time_seconds` INTEGER NOT NULL;","timestamp":"2025-07-12 14:12:18:1218"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `usage_records` CHANGE `input_file_count` `input_file_count` INTEGER NOT NULL;","timestamp":"2025-07-12 14:12:18:1218"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `usage_records` CHANGE `output_file_count` `output_file_count` INTEGER NOT NULL DEFAULT 0;","timestamp":"2025-07-12 14:12:18:1218"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `usage_records` CHANGE `success` `success` TINYINT(1) NOT NULL DEFAULT true;","timestamp":"2025-07-12 14:12:18:1218"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `usage_records` CHANGE `error_message` `error_message` TEXT;","timestamp":"2025-07-12 14:12:18:1218"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `usage_records` CHANGE `parameters` `parameters` JSON;","timestamp":"2025-07-12 14:12:19:1219"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `usage_records` CHANGE `created_at` `created_at` DATETIME NOT NULL;","timestamp":"2025-07-12 14:12:19:1219"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `usage_records` CHANGE `updated_at` `updated_at` DATETIME NOT NULL;","timestamp":"2025-07-12 14:12:19:1219"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SHOW INDEX FROM `usage_records`","timestamp":"2025-07-12 14:12:19:1219"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mDatabase initialized successfully\u001b[39m","timestamp":"2025-07-12 14:12:19:1219"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mServer running on port 3001\u001b[39m","timestamp":"2025-07-12 14:12:19:1219"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mEnvironment: development\u001b[39m","timestamp":"2025-07-12 14:12:19:1219"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mDatabase connected and synchronized\u001b[39m","timestamp":"2025-07-12 14:12:19:1219"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT `id`, `email`, `password_hash`, `first_name`, `last_name`, `subscription_plan`, `is_verified`, `verification_token`, `reset_password_token`, `reset_password_expires`, `last_login_at`, `created_at` AS `createdAt`, `updated_at` AS `updatedAt` FROM `users` AS `User` WHERE `User`.`email` = '<EMAIL>';","timestamp":"2025-07-12 14:24:08:248"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): INSERT INTO `users` (`id`,`email`,`password_hash`,`first_name`,`last_name`,`subscription_plan`,`is_verified`,`created_at`,`updated_at`) VALUES (?,?,?,?,?,?,?,?,?);","timestamp":"2025-07-12 14:24:08:248"}
{"email":"<EMAIL>","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mUser registered successfully\u001b[39m","timestamp":"2025-07-12 14:24:08:248","userId":"5cf9306f-6b16-4a16-bc51-85aec3177a46"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT `id`, `email`, `password_hash`, `first_name`, `last_name`, `subscription_plan`, `is_verified`, `verification_token`, `reset_password_token`, `reset_password_expires`, `last_login_at`, `created_at` AS `createdAt`, `updated_at` AS `updatedAt` FROM `users` AS `User` WHERE `User`.`id` = '5cf9306f-6b16-4a16-bc51-85aec3177a46';","timestamp":"2025-07-12 14:24:08:248"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT `id`, `user_id`, `tool_name`, `file_size_mb`, `processing_time_seconds`, `input_file_count`, `output_file_count`, `success`, `error_message`, `parameters`, `created_at` AS `createdAt`, `updated_at` AS `updatedAt` FROM `usage_records` AS `UsageRecord` WHERE `UsageRecord`.`user_id` = '5cf9306f-6b16-4a16-bc51-85aec3177a46' AND `UsageRecord`.`created_at` >= '2025-07-11 23:00:00';","timestamp":"2025-07-12 14:24:08:248"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT `id`, `email`, `password_hash`, `first_name`, `last_name`, `subscription_plan`, `is_verified`, `verification_token`, `reset_password_token`, `reset_password_expires`, `last_login_at`, `created_at` AS `createdAt`, `updated_at` AS `updatedAt` FROM `users` AS `User` WHERE `User`.`email` = '<EMAIL>';","timestamp":"2025-07-12 14:25:49:2549"}
{"error":"User with this email already exists","ip":"::1","isOperational":true,"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRequest error\u001b[39m","method":"POST","stack":"ValidationError: User with this email already exists\n    at G:\\PDF Porject\\backend\\src\\routes\\auth.ts:24:13\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","statusCode":400,"timestamp":"2025-07-12 14:25:49:2549","url":"/api/auth/register","userAgent":"axios/1.10.0"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT `id`, `email`, `password_hash`, `first_name`, `last_name`, `subscription_plan`, `is_verified`, `verification_token`, `reset_password_token`, `reset_password_expires`, `last_login_at`, `created_at` AS `createdAt`, `updated_at` AS `updatedAt` FROM `users` AS `User` WHERE `User`.`email` = '<EMAIL>';","timestamp":"2025-07-12 14:25:49:2549"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): UPDATE `users` SET `last_login_at`=?,`updated_at`=? WHERE `id` = ?","timestamp":"2025-07-12 14:25:49:2549"}
{"email":"<EMAIL>","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mUser logged in successfully\u001b[39m","timestamp":"2025-07-12 14:25:49:2549","userId":"5cf9306f-6b16-4a16-bc51-85aec3177a46"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT 1+1 AS result","timestamp":"2025-07-12 14:32:58:3258"}
{"database":"pdf","host":"127.0.0.1","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mDatabase connection established successfully\u001b[39m","port":3306,"timestamp":"2025-07-12 14:32:58:3258"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT TABLE_NAME FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_TYPE = 'BASE TABLE' AND TABLE_NAME = 'users' AND TABLE_SCHEMA = 'pdf'","timestamp":"2025-07-12 14:32:58:3258"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SHOW FULL COLUMNS FROM `users`;","timestamp":"2025-07-12 14:32:58:3258"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT CONSTRAINT_NAME as constraint_name,CONSTRAINT_NAME as constraintName,CONSTRAINT_SCHEMA as constraintSchema,CONSTRAINT_SCHEMA as constraintCatalog,TABLE_NAME as tableName,TABLE_SCHEMA as tableSchema,TABLE_SCHEMA as tableCatalog,COLUMN_NAME as columnName,REFERENCED_TABLE_SCHEMA as referencedTableSchema,REFERENCED_TABLE_SCHEMA as referencedTableCatalog,REFERENCED_TABLE_NAME as referencedTableName,REFERENCED_COLUMN_NAME as referencedColumnName FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE where TABLE_NAME = 'users' AND CONSTRAINT_NAME!='PRIMARY' AND CONSTRAINT_SCHEMA='pdf' AND REFERENCED_TABLE_NAME IS NOT NULL;","timestamp":"2025-07-12 14:32:58:3258"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `email` `email` VARCHAR(255) NOT NULL UNIQUE;","timestamp":"2025-07-12 14:32:58:3258"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT 1+1 AS result","timestamp":"2025-07-12 14:33:07:337"}
{"database":"pdf","host":"127.0.0.1","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mDatabase connection established successfully\u001b[39m","port":3306,"timestamp":"2025-07-12 14:33:07:337"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT TABLE_NAME FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_TYPE = 'BASE TABLE' AND TABLE_NAME = 'users' AND TABLE_SCHEMA = 'pdf'","timestamp":"2025-07-12 14:33:07:337"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SHOW FULL COLUMNS FROM `users`;","timestamp":"2025-07-12 14:33:07:337"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT CONSTRAINT_NAME as constraint_name,CONSTRAINT_NAME as constraintName,CONSTRAINT_SCHEMA as constraintSchema,CONSTRAINT_SCHEMA as constraintCatalog,TABLE_NAME as tableName,TABLE_SCHEMA as tableSchema,TABLE_SCHEMA as tableCatalog,COLUMN_NAME as columnName,REFERENCED_TABLE_SCHEMA as referencedTableSchema,REFERENCED_TABLE_SCHEMA as referencedTableCatalog,REFERENCED_TABLE_NAME as referencedTableName,REFERENCED_COLUMN_NAME as referencedColumnName FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE where TABLE_NAME = 'users' AND CONSTRAINT_NAME!='PRIMARY' AND CONSTRAINT_SCHEMA='pdf' AND REFERENCED_TABLE_NAME IS NOT NULL;","timestamp":"2025-07-12 14:33:07:337"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `email` `email` VARCHAR(255) NOT NULL UNIQUE;","timestamp":"2025-07-12 14:33:07:337"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `password_hash` `password_hash` VARCHAR(255) NOT NULL;","timestamp":"2025-07-12 14:33:07:337"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `first_name` `first_name` VARCHAR(100) NOT NULL;","timestamp":"2025-07-12 14:33:08:338"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `last_name` `last_name` VARCHAR(100) NOT NULL;","timestamp":"2025-07-12 14:33:08:338"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `subscription_plan` `subscription_plan` ENUM('free', 'premium', 'enterprise') NOT NULL DEFAULT 'free';","timestamp":"2025-07-12 14:33:08:338"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `is_verified` `is_verified` TINYINT(1) NOT NULL DEFAULT false;","timestamp":"2025-07-12 14:33:08:338"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `verification_token` `verification_token` VARCHAR(255);","timestamp":"2025-07-12 14:33:08:338"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `reset_password_token` `reset_password_token` VARCHAR(255);","timestamp":"2025-07-12 14:33:08:338"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `reset_password_expires` `reset_password_expires` DATETIME;","timestamp":"2025-07-12 14:33:08:338"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `last_login_at` `last_login_at` DATETIME;","timestamp":"2025-07-12 14:33:09:339"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `created_at` `created_at` DATETIME NOT NULL;","timestamp":"2025-07-12 14:33:09:339"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `updated_at` `updated_at` DATETIME NOT NULL;","timestamp":"2025-07-12 14:33:09:339"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SHOW INDEX FROM `users`","timestamp":"2025-07-12 14:33:09:339"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT TABLE_NAME FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_TYPE = 'BASE TABLE' AND TABLE_NAME = 'usage_records' AND TABLE_SCHEMA = 'pdf'","timestamp":"2025-07-12 14:33:09:339"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SHOW FULL COLUMNS FROM `usage_records`;","timestamp":"2025-07-12 14:33:09:339"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT CONSTRAINT_NAME as constraint_name,CONSTRAINT_NAME as constraintName,CONSTRAINT_SCHEMA as constraintSchema,CONSTRAINT_SCHEMA as constraintCatalog,TABLE_NAME as tableName,TABLE_SCHEMA as tableSchema,TABLE_SCHEMA as tableCatalog,COLUMN_NAME as columnName,REFERENCED_TABLE_SCHEMA as referencedTableSchema,REFERENCED_TABLE_SCHEMA as referencedTableCatalog,REFERENCED_TABLE_NAME as referencedTableName,REFERENCED_COLUMN_NAME as referencedColumnName FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE where TABLE_NAME = 'usage_records' AND CONSTRAINT_NAME!='PRIMARY' AND CONSTRAINT_SCHEMA='pdf' AND REFERENCED_TABLE_NAME IS NOT NULL;","timestamp":"2025-07-12 14:33:09:339"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT CONSTRAINT_CATALOG AS constraintCatalog, CONSTRAINT_NAME AS constraintName, CONSTRAINT_SCHEMA AS constraintSchema, CONSTRAINT_TYPE AS constraintType, TABLE_NAME AS tableName, TABLE_SCHEMA AS tableSchema from INFORMATION_SCHEMA.TABLE_CONSTRAINTS WHERE table_name='usage_records' AND constraint_name = 'usage_records_ibfk_1' AND TABLE_SCHEMA = 'pdf';","timestamp":"2025-07-12 14:33:09:339"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `usage_records` DROP FOREIGN KEY `usage_records_ibfk_1`;","timestamp":"2025-07-12 14:33:09:339"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `usage_records` ADD FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE ON UPDATE CASCADE;","timestamp":"2025-07-12 14:33:09:339"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `usage_records` CHANGE `tool_name` `tool_name` VARCHAR(100) NOT NULL;","timestamp":"2025-07-12 14:33:11:3311"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `usage_records` CHANGE `file_size_mb` `file_size_mb` DECIMAL(10,2) NOT NULL;","timestamp":"2025-07-12 14:33:12:3312"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `usage_records` CHANGE `processing_time_seconds` `processing_time_seconds` INTEGER NOT NULL;","timestamp":"2025-07-12 14:33:12:3312"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `usage_records` CHANGE `input_file_count` `input_file_count` INTEGER NOT NULL;","timestamp":"2025-07-12 14:33:12:3312"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `usage_records` CHANGE `output_file_count` `output_file_count` INTEGER NOT NULL DEFAULT 0;","timestamp":"2025-07-12 14:33:12:3312"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `usage_records` CHANGE `success` `success` TINYINT(1) NOT NULL DEFAULT true;","timestamp":"2025-07-12 14:33:12:3312"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT 1+1 AS result","timestamp":"2025-07-12 14:33:19:3319"}
{"database":"pdf","host":"127.0.0.1","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mDatabase connection established successfully\u001b[39m","port":3306,"timestamp":"2025-07-12 14:33:19:3319"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT TABLE_NAME FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_TYPE = 'BASE TABLE' AND TABLE_NAME = 'users' AND TABLE_SCHEMA = 'pdf'","timestamp":"2025-07-12 14:33:19:3319"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SHOW FULL COLUMNS FROM `users`;","timestamp":"2025-07-12 14:33:19:3319"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT CONSTRAINT_NAME as constraint_name,CONSTRAINT_NAME as constraintName,CONSTRAINT_SCHEMA as constraintSchema,CONSTRAINT_SCHEMA as constraintCatalog,TABLE_NAME as tableName,TABLE_SCHEMA as tableSchema,TABLE_SCHEMA as tableCatalog,COLUMN_NAME as columnName,REFERENCED_TABLE_SCHEMA as referencedTableSchema,REFERENCED_TABLE_SCHEMA as referencedTableCatalog,REFERENCED_TABLE_NAME as referencedTableName,REFERENCED_COLUMN_NAME as referencedColumnName FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE where TABLE_NAME = 'users' AND CONSTRAINT_NAME!='PRIMARY' AND CONSTRAINT_SCHEMA='pdf' AND REFERENCED_TABLE_NAME IS NOT NULL;","timestamp":"2025-07-12 14:33:19:3319"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `email` `email` VARCHAR(255) NOT NULL UNIQUE;","timestamp":"2025-07-12 14:33:19:3319"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `password_hash` `password_hash` VARCHAR(255) NOT NULL;","timestamp":"2025-07-12 14:33:19:3319"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `first_name` `first_name` VARCHAR(100) NOT NULL;","timestamp":"2025-07-12 14:33:19:3319"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `last_name` `last_name` VARCHAR(100) NOT NULL;","timestamp":"2025-07-12 14:33:20:3320"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `subscription_plan` `subscription_plan` ENUM('free', 'premium', 'enterprise') NOT NULL DEFAULT 'free';","timestamp":"2025-07-12 14:33:20:3320"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `is_verified` `is_verified` TINYINT(1) NOT NULL DEFAULT false;","timestamp":"2025-07-12 14:33:21:3321"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `verification_token` `verification_token` VARCHAR(255);","timestamp":"2025-07-12 14:33:21:3321"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `reset_password_token` `reset_password_token` VARCHAR(255);","timestamp":"2025-07-12 14:33:22:3322"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `reset_password_expires` `reset_password_expires` DATETIME;","timestamp":"2025-07-12 14:33:22:3322"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `last_login_at` `last_login_at` DATETIME;","timestamp":"2025-07-12 14:33:22:3322"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `created_at` `created_at` DATETIME NOT NULL;","timestamp":"2025-07-12 14:33:22:3322"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `updated_at` `updated_at` DATETIME NOT NULL;","timestamp":"2025-07-12 14:33:22:3322"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SHOW INDEX FROM `users`","timestamp":"2025-07-12 14:33:22:3322"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT TABLE_NAME FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_TYPE = 'BASE TABLE' AND TABLE_NAME = 'usage_records' AND TABLE_SCHEMA = 'pdf'","timestamp":"2025-07-12 14:33:22:3322"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SHOW FULL COLUMNS FROM `usage_records`;","timestamp":"2025-07-12 14:33:22:3322"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT CONSTRAINT_NAME as constraint_name,CONSTRAINT_NAME as constraintName,CONSTRAINT_SCHEMA as constraintSchema,CONSTRAINT_SCHEMA as constraintCatalog,TABLE_NAME as tableName,TABLE_SCHEMA as tableSchema,TABLE_SCHEMA as tableCatalog,COLUMN_NAME as columnName,REFERENCED_TABLE_SCHEMA as referencedTableSchema,REFERENCED_TABLE_SCHEMA as referencedTableCatalog,REFERENCED_TABLE_NAME as referencedTableName,REFERENCED_COLUMN_NAME as referencedColumnName FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE where TABLE_NAME = 'usage_records' AND CONSTRAINT_NAME!='PRIMARY' AND CONSTRAINT_SCHEMA='pdf' AND REFERENCED_TABLE_NAME IS NOT NULL;","timestamp":"2025-07-12 14:33:22:3322"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT CONSTRAINT_CATALOG AS constraintCatalog, CONSTRAINT_NAME AS constraintName, CONSTRAINT_SCHEMA AS constraintSchema, CONSTRAINT_TYPE AS constraintType, TABLE_NAME AS tableName, TABLE_SCHEMA AS tableSchema from INFORMATION_SCHEMA.TABLE_CONSTRAINTS WHERE table_name='usage_records' AND constraint_name = 'usage_records_ibfk_1' AND TABLE_SCHEMA = 'pdf';","timestamp":"2025-07-12 14:33:22:3322"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `usage_records` DROP FOREIGN KEY `usage_records_ibfk_1`;","timestamp":"2025-07-12 14:33:22:3322"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `usage_records` ADD FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE ON UPDATE CASCADE;","timestamp":"2025-07-12 14:33:22:3322"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `usage_records` CHANGE `tool_name` `tool_name` VARCHAR(100) NOT NULL;","timestamp":"2025-07-12 14:33:24:3324"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `usage_records` CHANGE `file_size_mb` `file_size_mb` DECIMAL(10,2) NOT NULL;","timestamp":"2025-07-12 14:33:24:3324"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `usage_records` CHANGE `processing_time_seconds` `processing_time_seconds` INTEGER NOT NULL;","timestamp":"2025-07-12 14:33:25:3325"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT 1+1 AS result","timestamp":"2025-07-12 14:33:30:3330"}
{"database":"pdf","host":"127.0.0.1","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mDatabase connection established successfully\u001b[39m","port":3306,"timestamp":"2025-07-12 14:33:30:3330"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT TABLE_NAME FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_TYPE = 'BASE TABLE' AND TABLE_NAME = 'users' AND TABLE_SCHEMA = 'pdf'","timestamp":"2025-07-12 14:33:30:3330"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SHOW FULL COLUMNS FROM `users`;","timestamp":"2025-07-12 14:33:30:3330"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT CONSTRAINT_NAME as constraint_name,CONSTRAINT_NAME as constraintName,CONSTRAINT_SCHEMA as constraintSchema,CONSTRAINT_SCHEMA as constraintCatalog,TABLE_NAME as tableName,TABLE_SCHEMA as tableSchema,TABLE_SCHEMA as tableCatalog,COLUMN_NAME as columnName,REFERENCED_TABLE_SCHEMA as referencedTableSchema,REFERENCED_TABLE_SCHEMA as referencedTableCatalog,REFERENCED_TABLE_NAME as referencedTableName,REFERENCED_COLUMN_NAME as referencedColumnName FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE where TABLE_NAME = 'users' AND CONSTRAINT_NAME!='PRIMARY' AND CONSTRAINT_SCHEMA='pdf' AND REFERENCED_TABLE_NAME IS NOT NULL;","timestamp":"2025-07-12 14:33:30:3330"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `email` `email` VARCHAR(255) NOT NULL UNIQUE;","timestamp":"2025-07-12 14:33:30:3330"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `password_hash` `password_hash` VARCHAR(255) NOT NULL;","timestamp":"2025-07-12 14:33:30:3330"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `first_name` `first_name` VARCHAR(100) NOT NULL;","timestamp":"2025-07-12 14:33:30:3330"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `last_name` `last_name` VARCHAR(100) NOT NULL;","timestamp":"2025-07-12 14:33:30:3330"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `subscription_plan` `subscription_plan` ENUM('free', 'premium', 'enterprise') NOT NULL DEFAULT 'free';","timestamp":"2025-07-12 14:33:31:3331"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `is_verified` `is_verified` TINYINT(1) NOT NULL DEFAULT false;","timestamp":"2025-07-12 14:33:31:3331"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `verification_token` `verification_token` VARCHAR(255);","timestamp":"2025-07-12 14:33:31:3331"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `reset_password_token` `reset_password_token` VARCHAR(255);","timestamp":"2025-07-12 14:33:31:3331"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `reset_password_expires` `reset_password_expires` DATETIME;","timestamp":"2025-07-12 14:33:31:3331"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `last_login_at` `last_login_at` DATETIME;","timestamp":"2025-07-12 14:33:32:3332"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `created_at` `created_at` DATETIME NOT NULL;","timestamp":"2025-07-12 14:33:32:3332"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `updated_at` `updated_at` DATETIME NOT NULL;","timestamp":"2025-07-12 14:33:32:3332"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SHOW INDEX FROM `users`","timestamp":"2025-07-12 14:33:32:3332"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT TABLE_NAME FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_TYPE = 'BASE TABLE' AND TABLE_NAME = 'usage_records' AND TABLE_SCHEMA = 'pdf'","timestamp":"2025-07-12 14:33:32:3332"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SHOW FULL COLUMNS FROM `usage_records`;","timestamp":"2025-07-12 14:33:32:3332"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT CONSTRAINT_NAME as constraint_name,CONSTRAINT_NAME as constraintName,CONSTRAINT_SCHEMA as constraintSchema,CONSTRAINT_SCHEMA as constraintCatalog,TABLE_NAME as tableName,TABLE_SCHEMA as tableSchema,TABLE_SCHEMA as tableCatalog,COLUMN_NAME as columnName,REFERENCED_TABLE_SCHEMA as referencedTableSchema,REFERENCED_TABLE_SCHEMA as referencedTableCatalog,REFERENCED_TABLE_NAME as referencedTableName,REFERENCED_COLUMN_NAME as referencedColumnName FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE where TABLE_NAME = 'usage_records' AND CONSTRAINT_NAME!='PRIMARY' AND CONSTRAINT_SCHEMA='pdf' AND REFERENCED_TABLE_NAME IS NOT NULL;","timestamp":"2025-07-12 14:33:32:3332"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT CONSTRAINT_CATALOG AS constraintCatalog, CONSTRAINT_NAME AS constraintName, CONSTRAINT_SCHEMA AS constraintSchema, CONSTRAINT_TYPE AS constraintType, TABLE_NAME AS tableName, TABLE_SCHEMA AS tableSchema from INFORMATION_SCHEMA.TABLE_CONSTRAINTS WHERE table_name='usage_records' AND constraint_name = 'usage_records_ibfk_1' AND TABLE_SCHEMA = 'pdf';","timestamp":"2025-07-12 14:33:32:3332"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `usage_records` DROP FOREIGN KEY `usage_records_ibfk_1`;","timestamp":"2025-07-12 14:33:32:3332"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `usage_records` ADD FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE ON UPDATE CASCADE;","timestamp":"2025-07-12 14:33:32:3332"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `usage_records` CHANGE `tool_name` `tool_name` VARCHAR(100) NOT NULL;","timestamp":"2025-07-12 14:33:34:3334"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `usage_records` CHANGE `file_size_mb` `file_size_mb` DECIMAL(10,2) NOT NULL;","timestamp":"2025-07-12 14:33:34:3334"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `usage_records` CHANGE `processing_time_seconds` `processing_time_seconds` INTEGER NOT NULL;","timestamp":"2025-07-12 14:33:35:3335"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `usage_records` CHANGE `input_file_count` `input_file_count` INTEGER NOT NULL;","timestamp":"2025-07-12 14:33:35:3335"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `usage_records` CHANGE `output_file_count` `output_file_count` INTEGER NOT NULL DEFAULT 0;","timestamp":"2025-07-12 14:33:35:3335"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `usage_records` CHANGE `success` `success` TINYINT(1) NOT NULL DEFAULT true;","timestamp":"2025-07-12 14:33:35:3335"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `usage_records` CHANGE `error_message` `error_message` TEXT;","timestamp":"2025-07-12 14:33:35:3335"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `usage_records` CHANGE `parameters` `parameters` JSON;","timestamp":"2025-07-12 14:33:35:3335"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `usage_records` CHANGE `created_at` `created_at` DATETIME NOT NULL;","timestamp":"2025-07-12 14:33:36:3336"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `usage_records` CHANGE `updated_at` `updated_at` DATETIME NOT NULL;","timestamp":"2025-07-12 14:33:36:3336"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SHOW INDEX FROM `usage_records`","timestamp":"2025-07-12 14:33:36:3336"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mDatabase initialized successfully\u001b[39m","timestamp":"2025-07-12 14:33:36:3336"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mServer running on port 3001\u001b[39m","timestamp":"2025-07-12 14:33:36:3336"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mEnvironment: development\u001b[39m","timestamp":"2025-07-12 14:33:36:3336"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mDatabase connected and synchronized\u001b[39m","timestamp":"2025-07-12 14:33:36:3336"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT 1+1 AS result","timestamp":"2025-07-12 14:34:16:3416"}
{"database":"pdf","host":"127.0.0.1","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mDatabase connection established successfully\u001b[39m","port":3306,"timestamp":"2025-07-12 14:34:16:3416"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT TABLE_NAME FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_TYPE = 'BASE TABLE' AND TABLE_NAME = 'users' AND TABLE_SCHEMA = 'pdf'","timestamp":"2025-07-12 14:34:16:3416"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SHOW FULL COLUMNS FROM `users`;","timestamp":"2025-07-12 14:34:16:3416"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT CONSTRAINT_NAME as constraint_name,CONSTRAINT_NAME as constraintName,CONSTRAINT_SCHEMA as constraintSchema,CONSTRAINT_SCHEMA as constraintCatalog,TABLE_NAME as tableName,TABLE_SCHEMA as tableSchema,TABLE_SCHEMA as tableCatalog,COLUMN_NAME as columnName,REFERENCED_TABLE_SCHEMA as referencedTableSchema,REFERENCED_TABLE_SCHEMA as referencedTableCatalog,REFERENCED_TABLE_NAME as referencedTableName,REFERENCED_COLUMN_NAME as referencedColumnName FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE where TABLE_NAME = 'users' AND CONSTRAINT_NAME!='PRIMARY' AND CONSTRAINT_SCHEMA='pdf' AND REFERENCED_TABLE_NAME IS NOT NULL;","timestamp":"2025-07-12 14:34:16:3416"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `email` `email` VARCHAR(255) NOT NULL UNIQUE;","timestamp":"2025-07-12 14:34:16:3416"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `password_hash` `password_hash` VARCHAR(255) NOT NULL;","timestamp":"2025-07-12 14:34:16:3416"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `first_name` `first_name` VARCHAR(100) NOT NULL;","timestamp":"2025-07-12 14:34:16:3416"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `last_name` `last_name` VARCHAR(100) NOT NULL;","timestamp":"2025-07-12 14:34:17:3417"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `subscription_plan` `subscription_plan` ENUM('free', 'premium', 'enterprise') NOT NULL DEFAULT 'free';","timestamp":"2025-07-12 14:34:17:3417"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `is_verified` `is_verified` TINYINT(1) NOT NULL DEFAULT false;","timestamp":"2025-07-12 14:34:17:3417"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `verification_token` `verification_token` VARCHAR(255);","timestamp":"2025-07-12 14:34:17:3417"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `reset_password_token` `reset_password_token` VARCHAR(255);","timestamp":"2025-07-12 14:34:17:3417"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `reset_password_expires` `reset_password_expires` DATETIME;","timestamp":"2025-07-12 14:34:18:3418"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `last_login_at` `last_login_at` DATETIME;","timestamp":"2025-07-12 14:34:18:3418"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `created_at` `created_at` DATETIME NOT NULL;","timestamp":"2025-07-12 14:34:18:3418"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `updated_at` `updated_at` DATETIME NOT NULL;","timestamp":"2025-07-12 14:34:18:3418"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SHOW INDEX FROM `users`","timestamp":"2025-07-12 14:34:18:3418"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT TABLE_NAME FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_TYPE = 'BASE TABLE' AND TABLE_NAME = 'usage_records' AND TABLE_SCHEMA = 'pdf'","timestamp":"2025-07-12 14:34:18:3418"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SHOW FULL COLUMNS FROM `usage_records`;","timestamp":"2025-07-12 14:34:18:3418"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT CONSTRAINT_NAME as constraint_name,CONSTRAINT_NAME as constraintName,CONSTRAINT_SCHEMA as constraintSchema,CONSTRAINT_SCHEMA as constraintCatalog,TABLE_NAME as tableName,TABLE_SCHEMA as tableSchema,TABLE_SCHEMA as tableCatalog,COLUMN_NAME as columnName,REFERENCED_TABLE_SCHEMA as referencedTableSchema,REFERENCED_TABLE_SCHEMA as referencedTableCatalog,REFERENCED_TABLE_NAME as referencedTableName,REFERENCED_COLUMN_NAME as referencedColumnName FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE where TABLE_NAME = 'usage_records' AND CONSTRAINT_NAME!='PRIMARY' AND CONSTRAINT_SCHEMA='pdf' AND REFERENCED_TABLE_NAME IS NOT NULL;","timestamp":"2025-07-12 14:34:18:3418"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT CONSTRAINT_CATALOG AS constraintCatalog, CONSTRAINT_NAME AS constraintName, CONSTRAINT_SCHEMA AS constraintSchema, CONSTRAINT_TYPE AS constraintType, TABLE_NAME AS tableName, TABLE_SCHEMA AS tableSchema from INFORMATION_SCHEMA.TABLE_CONSTRAINTS WHERE table_name='usage_records' AND constraint_name = 'usage_records_ibfk_1' AND TABLE_SCHEMA = 'pdf';","timestamp":"2025-07-12 14:34:18:3418"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `usage_records` DROP FOREIGN KEY `usage_records_ibfk_1`;","timestamp":"2025-07-12 14:34:18:3418"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `usage_records` ADD FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE ON UPDATE CASCADE;","timestamp":"2025-07-12 14:34:19:3419"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `usage_records` CHANGE `tool_name` `tool_name` VARCHAR(100) NOT NULL;","timestamp":"2025-07-12 14:34:21:3421"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT 1+1 AS result","timestamp":"2025-07-12 14:34:25:3425"}
{"database":"pdf","host":"127.0.0.1","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mDatabase connection established successfully\u001b[39m","port":3306,"timestamp":"2025-07-12 14:34:25:3425"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT TABLE_NAME FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_TYPE = 'BASE TABLE' AND TABLE_NAME = 'users' AND TABLE_SCHEMA = 'pdf'","timestamp":"2025-07-12 14:34:25:3425"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SHOW FULL COLUMNS FROM `users`;","timestamp":"2025-07-12 14:34:25:3425"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT CONSTRAINT_NAME as constraint_name,CONSTRAINT_NAME as constraintName,CONSTRAINT_SCHEMA as constraintSchema,CONSTRAINT_SCHEMA as constraintCatalog,TABLE_NAME as tableName,TABLE_SCHEMA as tableSchema,TABLE_SCHEMA as tableCatalog,COLUMN_NAME as columnName,REFERENCED_TABLE_SCHEMA as referencedTableSchema,REFERENCED_TABLE_SCHEMA as referencedTableCatalog,REFERENCED_TABLE_NAME as referencedTableName,REFERENCED_COLUMN_NAME as referencedColumnName FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE where TABLE_NAME = 'users' AND CONSTRAINT_NAME!='PRIMARY' AND CONSTRAINT_SCHEMA='pdf' AND REFERENCED_TABLE_NAME IS NOT NULL;","timestamp":"2025-07-12 14:34:25:3425"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `email` `email` VARCHAR(255) NOT NULL UNIQUE;","timestamp":"2025-07-12 14:34:25:3425"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `password_hash` `password_hash` VARCHAR(255) NOT NULL;","timestamp":"2025-07-12 14:34:27:3427"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `first_name` `first_name` VARCHAR(100) NOT NULL;","timestamp":"2025-07-12 14:34:28:3428"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `last_name` `last_name` VARCHAR(100) NOT NULL;","timestamp":"2025-07-12 14:34:28:3428"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `subscription_plan` `subscription_plan` ENUM('free', 'premium', 'enterprise') NOT NULL DEFAULT 'free';","timestamp":"2025-07-12 14:34:28:3428"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `is_verified` `is_verified` TINYINT(1) NOT NULL DEFAULT false;","timestamp":"2025-07-12 14:34:28:3428"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `verification_token` `verification_token` VARCHAR(255);","timestamp":"2025-07-12 14:34:28:3428"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `reset_password_token` `reset_password_token` VARCHAR(255);","timestamp":"2025-07-12 14:34:28:3428"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `reset_password_expires` `reset_password_expires` DATETIME;","timestamp":"2025-07-12 14:34:29:3429"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `last_login_at` `last_login_at` DATETIME;","timestamp":"2025-07-12 14:34:29:3429"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `created_at` `created_at` DATETIME NOT NULL;","timestamp":"2025-07-12 14:34:29:3429"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `updated_at` `updated_at` DATETIME NOT NULL;","timestamp":"2025-07-12 14:34:29:3429"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SHOW INDEX FROM `users`","timestamp":"2025-07-12 14:34:29:3429"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT TABLE_NAME FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_TYPE = 'BASE TABLE' AND TABLE_NAME = 'usage_records' AND TABLE_SCHEMA = 'pdf'","timestamp":"2025-07-12 14:34:29:3429"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SHOW FULL COLUMNS FROM `usage_records`;","timestamp":"2025-07-12 14:34:29:3429"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT CONSTRAINT_NAME as constraint_name,CONSTRAINT_NAME as constraintName,CONSTRAINT_SCHEMA as constraintSchema,CONSTRAINT_SCHEMA as constraintCatalog,TABLE_NAME as tableName,TABLE_SCHEMA as tableSchema,TABLE_SCHEMA as tableCatalog,COLUMN_NAME as columnName,REFERENCED_TABLE_SCHEMA as referencedTableSchema,REFERENCED_TABLE_SCHEMA as referencedTableCatalog,REFERENCED_TABLE_NAME as referencedTableName,REFERENCED_COLUMN_NAME as referencedColumnName FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE where TABLE_NAME = 'usage_records' AND CONSTRAINT_NAME!='PRIMARY' AND CONSTRAINT_SCHEMA='pdf' AND REFERENCED_TABLE_NAME IS NOT NULL;","timestamp":"2025-07-12 14:34:29:3429"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT CONSTRAINT_CATALOG AS constraintCatalog, CONSTRAINT_NAME AS constraintName, CONSTRAINT_SCHEMA AS constraintSchema, CONSTRAINT_TYPE AS constraintType, TABLE_NAME AS tableName, TABLE_SCHEMA AS tableSchema from INFORMATION_SCHEMA.TABLE_CONSTRAINTS WHERE table_name='usage_records' AND constraint_name = 'usage_records_ibfk_1' AND TABLE_SCHEMA = 'pdf';","timestamp":"2025-07-12 14:34:29:3429"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `usage_records` DROP FOREIGN KEY `usage_records_ibfk_1`;","timestamp":"2025-07-12 14:34:29:3429"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `usage_records` ADD FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE ON UPDATE CASCADE;","timestamp":"2025-07-12 14:34:30:3430"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `usage_records` CHANGE `tool_name` `tool_name` VARCHAR(100) NOT NULL;","timestamp":"2025-07-12 14:34:32:3432"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `usage_records` CHANGE `file_size_mb` `file_size_mb` DECIMAL(10,2) NOT NULL;","timestamp":"2025-07-12 14:34:32:3432"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `usage_records` CHANGE `processing_time_seconds` `processing_time_seconds` INTEGER NOT NULL;","timestamp":"2025-07-12 14:34:32:3432"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `usage_records` CHANGE `input_file_count` `input_file_count` INTEGER NOT NULL;","timestamp":"2025-07-12 14:34:32:3432"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `usage_records` CHANGE `output_file_count` `output_file_count` INTEGER NOT NULL DEFAULT 0;","timestamp":"2025-07-12 14:34:32:3432"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `usage_records` CHANGE `success` `success` TINYINT(1) NOT NULL DEFAULT true;","timestamp":"2025-07-12 14:34:32:3432"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `usage_records` CHANGE `error_message` `error_message` TEXT;","timestamp":"2025-07-12 14:34:33:3433"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `usage_records` CHANGE `parameters` `parameters` JSON;","timestamp":"2025-07-12 14:34:33:3433"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `usage_records` CHANGE `created_at` `created_at` DATETIME NOT NULL;","timestamp":"2025-07-12 14:34:33:3433"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `usage_records` CHANGE `updated_at` `updated_at` DATETIME NOT NULL;","timestamp":"2025-07-12 14:34:33:3433"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SHOW INDEX FROM `usage_records`","timestamp":"2025-07-12 14:34:33:3433"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mDatabase initialized successfully\u001b[39m","timestamp":"2025-07-12 14:34:33:3433"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mServer running on port 3001\u001b[39m","timestamp":"2025-07-12 14:34:33:3433"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mEnvironment: development\u001b[39m","timestamp":"2025-07-12 14:34:33:3433"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mDatabase connected and synchronized\u001b[39m","timestamp":"2025-07-12 14:34:33:3433"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT 1+1 AS result","timestamp":"2025-07-12 14:34:43:3443"}
{"database":"pdf","host":"127.0.0.1","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mDatabase connection established successfully\u001b[39m","port":3306,"timestamp":"2025-07-12 14:34:43:3443"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT TABLE_NAME FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_TYPE = 'BASE TABLE' AND TABLE_NAME = 'users' AND TABLE_SCHEMA = 'pdf'","timestamp":"2025-07-12 14:34:43:3443"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SHOW FULL COLUMNS FROM `users`;","timestamp":"2025-07-12 14:34:43:3443"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT CONSTRAINT_NAME as constraint_name,CONSTRAINT_NAME as constraintName,CONSTRAINT_SCHEMA as constraintSchema,CONSTRAINT_SCHEMA as constraintCatalog,TABLE_NAME as tableName,TABLE_SCHEMA as tableSchema,TABLE_SCHEMA as tableCatalog,COLUMN_NAME as columnName,REFERENCED_TABLE_SCHEMA as referencedTableSchema,REFERENCED_TABLE_SCHEMA as referencedTableCatalog,REFERENCED_TABLE_NAME as referencedTableName,REFERENCED_COLUMN_NAME as referencedColumnName FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE where TABLE_NAME = 'users' AND CONSTRAINT_NAME!='PRIMARY' AND CONSTRAINT_SCHEMA='pdf' AND REFERENCED_TABLE_NAME IS NOT NULL;","timestamp":"2025-07-12 14:34:43:3443"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `email` `email` VARCHAR(255) NOT NULL UNIQUE;","timestamp":"2025-07-12 14:34:43:3443"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `password_hash` `password_hash` VARCHAR(255) NOT NULL;","timestamp":"2025-07-12 14:34:44:3444"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `first_name` `first_name` VARCHAR(100) NOT NULL;","timestamp":"2025-07-12 14:34:44:3444"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `last_name` `last_name` VARCHAR(100) NOT NULL;","timestamp":"2025-07-12 14:34:45:3445"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `subscription_plan` `subscription_plan` ENUM('free', 'premium', 'enterprise') NOT NULL DEFAULT 'free';","timestamp":"2025-07-12 14:34:45:3445"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `is_verified` `is_verified` TINYINT(1) NOT NULL DEFAULT false;","timestamp":"2025-07-12 14:34:45:3445"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `verification_token` `verification_token` VARCHAR(255);","timestamp":"2025-07-12 14:34:45:3445"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `reset_password_token` `reset_password_token` VARCHAR(255);","timestamp":"2025-07-12 14:34:45:3445"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `reset_password_expires` `reset_password_expires` DATETIME;","timestamp":"2025-07-12 14:34:45:3445"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `last_login_at` `last_login_at` DATETIME;","timestamp":"2025-07-12 14:34:46:3446"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `created_at` `created_at` DATETIME NOT NULL;","timestamp":"2025-07-12 14:34:46:3446"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `updated_at` `updated_at` DATETIME NOT NULL;","timestamp":"2025-07-12 14:34:46:3446"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SHOW INDEX FROM `users`","timestamp":"2025-07-12 14:34:46:3446"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT TABLE_NAME FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_TYPE = 'BASE TABLE' AND TABLE_NAME = 'processing_sessions' AND TABLE_SCHEMA = 'pdf'","timestamp":"2025-07-12 14:34:46:3446"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): CREATE TABLE IF NOT EXISTS `processing_sessions` (`id` CHAR(36) BINARY , `user_id` CHAR(36) BINARY NOT NULL, `session_token` VARCHAR(255) NOT NULL UNIQUE, `status` ENUM('active', 'completed', 'expired') NOT NULL DEFAULT 'active', `files_processed` INTEGER NOT NULL DEFAULT 0, `quota_consumed` INTEGER NOT NULL DEFAULT 0, `can_download` TINYINT(1) NOT NULL DEFAULT true, `expires_at` DATETIME NOT NULL, `created_at` DATETIME NOT NULL, `updated_at` DATETIME NOT NULL, PRIMARY KEY (`id`), FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE ON UPDATE CASCADE) ENGINE=InnoDB;","timestamp":"2025-07-12 14:34:46:3446"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SHOW INDEX FROM `processing_sessions`","timestamp":"2025-07-12 14:34:47:3447"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `processing_sessions` ADD INDEX `processing_sessions_user_id` (`user_id`)","timestamp":"2025-07-12 14:34:47:3447"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `processing_sessions` ADD UNIQUE INDEX `processing_sessions_session_token` (`session_token`)","timestamp":"2025-07-12 14:34:48:3448"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `processing_sessions` ADD INDEX `processing_sessions_status` (`status`)","timestamp":"2025-07-12 14:34:48:3448"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `processing_sessions` ADD INDEX `processing_sessions_expires_at` (`expires_at`)","timestamp":"2025-07-12 14:34:49:3449"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `processing_sessions` ADD INDEX `processing_sessions_user_id_status` (`user_id`, `status`)","timestamp":"2025-07-12 14:34:49:3449"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT TABLE_NAME FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_TYPE = 'BASE TABLE' AND TABLE_NAME = 'usage_records' AND TABLE_SCHEMA = 'pdf'","timestamp":"2025-07-12 14:34:49:3449"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SHOW FULL COLUMNS FROM `usage_records`;","timestamp":"2025-07-12 14:34:49:3449"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT CONSTRAINT_NAME as constraint_name,CONSTRAINT_NAME as constraintName,CONSTRAINT_SCHEMA as constraintSchema,CONSTRAINT_SCHEMA as constraintCatalog,TABLE_NAME as tableName,TABLE_SCHEMA as tableSchema,TABLE_SCHEMA as tableCatalog,COLUMN_NAME as columnName,REFERENCED_TABLE_SCHEMA as referencedTableSchema,REFERENCED_TABLE_SCHEMA as referencedTableCatalog,REFERENCED_TABLE_NAME as referencedTableName,REFERENCED_COLUMN_NAME as referencedColumnName FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE where TABLE_NAME = 'usage_records' AND CONSTRAINT_NAME!='PRIMARY' AND CONSTRAINT_SCHEMA='pdf' AND REFERENCED_TABLE_NAME IS NOT NULL;","timestamp":"2025-07-12 14:34:49:3449"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT CONSTRAINT_CATALOG AS constraintCatalog, CONSTRAINT_NAME AS constraintName, CONSTRAINT_SCHEMA AS constraintSchema, CONSTRAINT_TYPE AS constraintType, TABLE_NAME AS tableName, TABLE_SCHEMA AS tableSchema from INFORMATION_SCHEMA.TABLE_CONSTRAINTS WHERE table_name='usage_records' AND constraint_name = 'usage_records_ibfk_1' AND TABLE_SCHEMA = 'pdf';","timestamp":"2025-07-12 14:34:49:3449"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `usage_records` DROP FOREIGN KEY `usage_records_ibfk_1`;","timestamp":"2025-07-12 14:34:49:3449"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `usage_records` ADD FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE ON UPDATE CASCADE;","timestamp":"2025-07-12 14:34:50:3450"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `usage_records` CHANGE `tool_name` `tool_name` VARCHAR(100) NOT NULL;","timestamp":"2025-07-12 14:34:52:3452"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `usage_records` CHANGE `file_size_mb` `file_size_mb` DECIMAL(10,2) NOT NULL;","timestamp":"2025-07-12 14:34:52:3452"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `usage_records` CHANGE `processing_time_seconds` `processing_time_seconds` INTEGER NOT NULL;","timestamp":"2025-07-12 14:34:52:3452"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `usage_records` CHANGE `input_file_count` `input_file_count` INTEGER NOT NULL;","timestamp":"2025-07-12 14:34:53:3453"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `usage_records` CHANGE `output_file_count` `output_file_count` INTEGER NOT NULL DEFAULT 0;","timestamp":"2025-07-12 14:34:53:3453"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `usage_records` CHANGE `success` `success` TINYINT(1) NOT NULL DEFAULT true;","timestamp":"2025-07-12 14:34:53:3453"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `usage_records` CHANGE `error_message` `error_message` TEXT;","timestamp":"2025-07-12 14:34:53:3453"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `usage_records` CHANGE `parameters` `parameters` JSON;","timestamp":"2025-07-12 14:34:53:3453"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `usage_records` CHANGE `created_at` `created_at` DATETIME NOT NULL;","timestamp":"2025-07-12 14:34:53:3453"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `usage_records` CHANGE `updated_at` `updated_at` DATETIME NOT NULL;","timestamp":"2025-07-12 14:34:54:3454"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SHOW INDEX FROM `usage_records`","timestamp":"2025-07-12 14:34:54:3454"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mDatabase initialized successfully\u001b[39m","timestamp":"2025-07-12 14:34:54:3454"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mServer running on port 3001\u001b[39m","timestamp":"2025-07-12 14:34:54:3454"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mEnvironment: development\u001b[39m","timestamp":"2025-07-12 14:34:54:3454"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mDatabase connected and synchronized\u001b[39m","timestamp":"2025-07-12 14:34:54:3454"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT 1+1 AS result","timestamp":"2025-07-12 14:35:18:3518"}
{"database":"pdf","host":"127.0.0.1","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mDatabase connection established successfully\u001b[39m","port":3306,"timestamp":"2025-07-12 14:35:18:3518"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT TABLE_NAME FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_TYPE = 'BASE TABLE' AND TABLE_NAME = 'users' AND TABLE_SCHEMA = 'pdf'","timestamp":"2025-07-12 14:35:18:3518"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SHOW FULL COLUMNS FROM `users`;","timestamp":"2025-07-12 14:35:18:3518"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT CONSTRAINT_NAME as constraint_name,CONSTRAINT_NAME as constraintName,CONSTRAINT_SCHEMA as constraintSchema,CONSTRAINT_SCHEMA as constraintCatalog,TABLE_NAME as tableName,TABLE_SCHEMA as tableSchema,TABLE_SCHEMA as tableCatalog,COLUMN_NAME as columnName,REFERENCED_TABLE_SCHEMA as referencedTableSchema,REFERENCED_TABLE_SCHEMA as referencedTableCatalog,REFERENCED_TABLE_NAME as referencedTableName,REFERENCED_COLUMN_NAME as referencedColumnName FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE where TABLE_NAME = 'users' AND CONSTRAINT_NAME!='PRIMARY' AND CONSTRAINT_SCHEMA='pdf' AND REFERENCED_TABLE_NAME IS NOT NULL;","timestamp":"2025-07-12 14:35:18:3518"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `email` `email` VARCHAR(255) NOT NULL UNIQUE;","timestamp":"2025-07-12 14:35:18:3518"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `password_hash` `password_hash` VARCHAR(255) NOT NULL;","timestamp":"2025-07-12 14:35:18:3518"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `first_name` `first_name` VARCHAR(100) NOT NULL;","timestamp":"2025-07-12 14:35:19:3519"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `last_name` `last_name` VARCHAR(100) NOT NULL;","timestamp":"2025-07-12 14:35:20:3520"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `subscription_plan` `subscription_plan` ENUM('free', 'premium', 'enterprise') NOT NULL DEFAULT 'free';","timestamp":"2025-07-12 14:35:20:3520"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `is_verified` `is_verified` TINYINT(1) NOT NULL DEFAULT false;","timestamp":"2025-07-12 14:35:20:3520"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `verification_token` `verification_token` VARCHAR(255);","timestamp":"2025-07-12 14:35:20:3520"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `reset_password_token` `reset_password_token` VARCHAR(255);","timestamp":"2025-07-12 14:35:20:3520"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `reset_password_expires` `reset_password_expires` DATETIME;","timestamp":"2025-07-12 14:35:21:3521"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `last_login_at` `last_login_at` DATETIME;","timestamp":"2025-07-12 14:35:21:3521"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `created_at` `created_at` DATETIME NOT NULL;","timestamp":"2025-07-12 14:35:21:3521"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `updated_at` `updated_at` DATETIME NOT NULL;","timestamp":"2025-07-12 14:35:21:3521"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SHOW INDEX FROM `users`","timestamp":"2025-07-12 14:35:22:3522"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT TABLE_NAME FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_TYPE = 'BASE TABLE' AND TABLE_NAME = 'processing_sessions' AND TABLE_SCHEMA = 'pdf'","timestamp":"2025-07-12 14:35:22:3522"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SHOW FULL COLUMNS FROM `processing_sessions`;","timestamp":"2025-07-12 14:35:22:3522"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT CONSTRAINT_NAME as constraint_name,CONSTRAINT_NAME as constraintName,CONSTRAINT_SCHEMA as constraintSchema,CONSTRAINT_SCHEMA as constraintCatalog,TABLE_NAME as tableName,TABLE_SCHEMA as tableSchema,TABLE_SCHEMA as tableCatalog,COLUMN_NAME as columnName,REFERENCED_TABLE_SCHEMA as referencedTableSchema,REFERENCED_TABLE_SCHEMA as referencedTableCatalog,REFERENCED_TABLE_NAME as referencedTableName,REFERENCED_COLUMN_NAME as referencedColumnName FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE where TABLE_NAME = 'processing_sessions' AND CONSTRAINT_NAME!='PRIMARY' AND CONSTRAINT_SCHEMA='pdf' AND REFERENCED_TABLE_NAME IS NOT NULL;","timestamp":"2025-07-12 14:35:22:3522"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT CONSTRAINT_CATALOG AS constraintCatalog, CONSTRAINT_NAME AS constraintName, CONSTRAINT_SCHEMA AS constraintSchema, CONSTRAINT_TYPE AS constraintType, TABLE_NAME AS tableName, TABLE_SCHEMA AS tableSchema from INFORMATION_SCHEMA.TABLE_CONSTRAINTS WHERE table_name='processing_sessions' AND constraint_name = 'processing_sessions_ibfk_1' AND TABLE_SCHEMA = 'pdf';","timestamp":"2025-07-12 14:35:22:3522"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `processing_sessions` DROP FOREIGN KEY `processing_sessions_ibfk_1`;","timestamp":"2025-07-12 14:35:22:3522"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `processing_sessions` ADD FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE ON UPDATE CASCADE;","timestamp":"2025-07-12 14:35:22:3522"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `processing_sessions` CHANGE `session_token` `session_token` VARCHAR(255) NOT NULL UNIQUE;","timestamp":"2025-07-12 14:35:24:3524"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `processing_sessions` CHANGE `status` `status` ENUM('active', 'completed', 'expired') NOT NULL DEFAULT 'active';","timestamp":"2025-07-12 14:35:24:3524"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `processing_sessions` CHANGE `files_processed` `files_processed` INTEGER NOT NULL DEFAULT 0;","timestamp":"2025-07-12 14:35:25:3525"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `processing_sessions` CHANGE `quota_consumed` `quota_consumed` INTEGER NOT NULL DEFAULT 0;","timestamp":"2025-07-12 14:35:25:3525"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `processing_sessions` CHANGE `can_download` `can_download` TINYINT(1) NOT NULL DEFAULT true;","timestamp":"2025-07-12 14:35:25:3525"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `processing_sessions` CHANGE `expires_at` `expires_at` DATETIME NOT NULL;","timestamp":"2025-07-12 14:35:25:3525"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `processing_sessions` CHANGE `created_at` `created_at` DATETIME NOT NULL;","timestamp":"2025-07-12 14:35:25:3525"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `processing_sessions` CHANGE `updated_at` `updated_at` DATETIME NOT NULL;","timestamp":"2025-07-12 14:35:25:3525"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SHOW INDEX FROM `processing_sessions`","timestamp":"2025-07-12 14:35:26:3526"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT TABLE_NAME FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_TYPE = 'BASE TABLE' AND TABLE_NAME = 'usage_records' AND TABLE_SCHEMA = 'pdf'","timestamp":"2025-07-12 14:35:26:3526"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SHOW FULL COLUMNS FROM `usage_records`;","timestamp":"2025-07-12 14:35:26:3526"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT CONSTRAINT_NAME as constraint_name,CONSTRAINT_NAME as constraintName,CONSTRAINT_SCHEMA as constraintSchema,CONSTRAINT_SCHEMA as constraintCatalog,TABLE_NAME as tableName,TABLE_SCHEMA as tableSchema,TABLE_SCHEMA as tableCatalog,COLUMN_NAME as columnName,REFERENCED_TABLE_SCHEMA as referencedTableSchema,REFERENCED_TABLE_SCHEMA as referencedTableCatalog,REFERENCED_TABLE_NAME as referencedTableName,REFERENCED_COLUMN_NAME as referencedColumnName FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE where TABLE_NAME = 'usage_records' AND CONSTRAINT_NAME!='PRIMARY' AND CONSTRAINT_SCHEMA='pdf' AND REFERENCED_TABLE_NAME IS NOT NULL;","timestamp":"2025-07-12 14:35:26:3526"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT CONSTRAINT_CATALOG AS constraintCatalog, CONSTRAINT_NAME AS constraintName, CONSTRAINT_SCHEMA AS constraintSchema, CONSTRAINT_TYPE AS constraintType, TABLE_NAME AS tableName, TABLE_SCHEMA AS tableSchema from INFORMATION_SCHEMA.TABLE_CONSTRAINTS WHERE table_name='usage_records' AND constraint_name = 'usage_records_ibfk_1' AND TABLE_SCHEMA = 'pdf';","timestamp":"2025-07-12 14:35:26:3526"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `usage_records` DROP FOREIGN KEY `usage_records_ibfk_1`;","timestamp":"2025-07-12 14:35:26:3526"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `usage_records` ADD FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE ON UPDATE CASCADE;","timestamp":"2025-07-12 14:35:26:3526"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `usage_records` CHANGE `tool_name` `tool_name` VARCHAR(100) NOT NULL;","timestamp":"2025-07-12 14:35:28:3528"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `usage_records` CHANGE `file_size_mb` `file_size_mb` DECIMAL(10,2) NOT NULL;","timestamp":"2025-07-12 14:35:28:3528"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `usage_records` CHANGE `processing_time_seconds` `processing_time_seconds` INTEGER NOT NULL;","timestamp":"2025-07-12 14:35:29:3529"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `usage_records` CHANGE `input_file_count` `input_file_count` INTEGER NOT NULL;","timestamp":"2025-07-12 14:35:29:3529"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `usage_records` CHANGE `output_file_count` `output_file_count` INTEGER NOT NULL DEFAULT 0;","timestamp":"2025-07-12 14:35:29:3529"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `usage_records` CHANGE `success` `success` TINYINT(1) NOT NULL DEFAULT true;","timestamp":"2025-07-12 14:35:29:3529"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `usage_records` CHANGE `error_message` `error_message` TEXT;","timestamp":"2025-07-12 14:35:29:3529"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `usage_records` CHANGE `parameters` `parameters` JSON;","timestamp":"2025-07-12 14:35:29:3529"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `usage_records` CHANGE `created_at` `created_at` DATETIME NOT NULL;","timestamp":"2025-07-12 14:35:30:3530"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `usage_records` CHANGE `updated_at` `updated_at` DATETIME NOT NULL;","timestamp":"2025-07-12 14:35:30:3530"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SHOW INDEX FROM `usage_records`","timestamp":"2025-07-12 14:35:30:3530"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mDatabase initialized successfully\u001b[39m","timestamp":"2025-07-12 14:35:30:3530"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mServer running on port 3001\u001b[39m","timestamp":"2025-07-12 14:35:30:3530"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mEnvironment: development\u001b[39m","timestamp":"2025-07-12 14:35:30:3530"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mDatabase connected and synchronized\u001b[39m","timestamp":"2025-07-12 14:35:30:3530"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT 1+1 AS result","timestamp":"2025-07-12 14:35:40:3540"}
{"database":"pdf","host":"127.0.0.1","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mDatabase connection established successfully\u001b[39m","port":3306,"timestamp":"2025-07-12 14:35:40:3540"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT TABLE_NAME FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_TYPE = 'BASE TABLE' AND TABLE_NAME = 'users' AND TABLE_SCHEMA = 'pdf'","timestamp":"2025-07-12 14:35:40:3540"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SHOW FULL COLUMNS FROM `users`;","timestamp":"2025-07-12 14:35:40:3540"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT CONSTRAINT_NAME as constraint_name,CONSTRAINT_NAME as constraintName,CONSTRAINT_SCHEMA as constraintSchema,CONSTRAINT_SCHEMA as constraintCatalog,TABLE_NAME as tableName,TABLE_SCHEMA as tableSchema,TABLE_SCHEMA as tableCatalog,COLUMN_NAME as columnName,REFERENCED_TABLE_SCHEMA as referencedTableSchema,REFERENCED_TABLE_SCHEMA as referencedTableCatalog,REFERENCED_TABLE_NAME as referencedTableName,REFERENCED_COLUMN_NAME as referencedColumnName FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE where TABLE_NAME = 'users' AND CONSTRAINT_NAME!='PRIMARY' AND CONSTRAINT_SCHEMA='pdf' AND REFERENCED_TABLE_NAME IS NOT NULL;","timestamp":"2025-07-12 14:35:40:3540"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `email` `email` VARCHAR(255) NOT NULL UNIQUE;","timestamp":"2025-07-12 14:35:40:3540"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `password_hash` `password_hash` VARCHAR(255) NOT NULL;","timestamp":"2025-07-12 14:35:41:3541"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `first_name` `first_name` VARCHAR(100) NOT NULL;","timestamp":"2025-07-12 14:35:41:3541"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `last_name` `last_name` VARCHAR(100) NOT NULL;","timestamp":"2025-07-12 14:35:41:3541"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `subscription_plan` `subscription_plan` ENUM('free', 'premium', 'enterprise') NOT NULL DEFAULT 'free';","timestamp":"2025-07-12 14:35:42:3542"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `is_verified` `is_verified` TINYINT(1) NOT NULL DEFAULT false;","timestamp":"2025-07-12 14:35:42:3542"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `verification_token` `verification_token` VARCHAR(255);","timestamp":"2025-07-12 14:35:42:3542"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `reset_password_token` `reset_password_token` VARCHAR(255);","timestamp":"2025-07-12 14:35:42:3542"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `reset_password_expires` `reset_password_expires` DATETIME;","timestamp":"2025-07-12 14:35:42:3542"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `last_login_at` `last_login_at` DATETIME;","timestamp":"2025-07-12 14:35:43:3543"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `created_at` `created_at` DATETIME NOT NULL;","timestamp":"2025-07-12 14:35:43:3543"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `updated_at` `updated_at` DATETIME NOT NULL;","timestamp":"2025-07-12 14:35:43:3543"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SHOW INDEX FROM `users`","timestamp":"2025-07-12 14:35:43:3543"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT TABLE_NAME FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_TYPE = 'BASE TABLE' AND TABLE_NAME = 'processing_sessions' AND TABLE_SCHEMA = 'pdf'","timestamp":"2025-07-12 14:35:43:3543"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SHOW FULL COLUMNS FROM `processing_sessions`;","timestamp":"2025-07-12 14:35:43:3543"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT CONSTRAINT_NAME as constraint_name,CONSTRAINT_NAME as constraintName,CONSTRAINT_SCHEMA as constraintSchema,CONSTRAINT_SCHEMA as constraintCatalog,TABLE_NAME as tableName,TABLE_SCHEMA as tableSchema,TABLE_SCHEMA as tableCatalog,COLUMN_NAME as columnName,REFERENCED_TABLE_SCHEMA as referencedTableSchema,REFERENCED_TABLE_SCHEMA as referencedTableCatalog,REFERENCED_TABLE_NAME as referencedTableName,REFERENCED_COLUMN_NAME as referencedColumnName FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE where TABLE_NAME = 'processing_sessions' AND CONSTRAINT_NAME!='PRIMARY' AND CONSTRAINT_SCHEMA='pdf' AND REFERENCED_TABLE_NAME IS NOT NULL;","timestamp":"2025-07-12 14:35:43:3543"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT CONSTRAINT_CATALOG AS constraintCatalog, CONSTRAINT_NAME AS constraintName, CONSTRAINT_SCHEMA AS constraintSchema, CONSTRAINT_TYPE AS constraintType, TABLE_NAME AS tableName, TABLE_SCHEMA AS tableSchema from INFORMATION_SCHEMA.TABLE_CONSTRAINTS WHERE table_name='processing_sessions' AND constraint_name = 'processing_sessions_ibfk_1' AND TABLE_SCHEMA = 'pdf';","timestamp":"2025-07-12 14:35:43:3543"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `processing_sessions` DROP FOREIGN KEY `processing_sessions_ibfk_1`;","timestamp":"2025-07-12 14:35:43:3543"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `processing_sessions` ADD FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE ON UPDATE CASCADE;","timestamp":"2025-07-12 14:35:44:3544"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `processing_sessions` CHANGE `session_token` `session_token` VARCHAR(255) NOT NULL UNIQUE;","timestamp":"2025-07-12 14:35:46:3546"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT 1+1 AS result","timestamp":"2025-07-12 14:35:50:3550"}
{"database":"pdf","host":"127.0.0.1","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mDatabase connection established successfully\u001b[39m","port":3306,"timestamp":"2025-07-12 14:35:50:3550"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT TABLE_NAME FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_TYPE = 'BASE TABLE' AND TABLE_NAME = 'users' AND TABLE_SCHEMA = 'pdf'","timestamp":"2025-07-12 14:35:50:3550"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SHOW FULL COLUMNS FROM `users`;","timestamp":"2025-07-12 14:35:50:3550"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT CONSTRAINT_NAME as constraint_name,CONSTRAINT_NAME as constraintName,CONSTRAINT_SCHEMA as constraintSchema,CONSTRAINT_SCHEMA as constraintCatalog,TABLE_NAME as tableName,TABLE_SCHEMA as tableSchema,TABLE_SCHEMA as tableCatalog,COLUMN_NAME as columnName,REFERENCED_TABLE_SCHEMA as referencedTableSchema,REFERENCED_TABLE_SCHEMA as referencedTableCatalog,REFERENCED_TABLE_NAME as referencedTableName,REFERENCED_COLUMN_NAME as referencedColumnName FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE where TABLE_NAME = 'users' AND CONSTRAINT_NAME!='PRIMARY' AND CONSTRAINT_SCHEMA='pdf' AND REFERENCED_TABLE_NAME IS NOT NULL;","timestamp":"2025-07-12 14:35:50:3550"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `email` `email` VARCHAR(255) NOT NULL UNIQUE;","timestamp":"2025-07-12 14:35:50:3550"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `password_hash` `password_hash` VARCHAR(255) NOT NULL;","timestamp":"2025-07-12 14:35:52:3552"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `first_name` `first_name` VARCHAR(100) NOT NULL;","timestamp":"2025-07-12 14:35:52:3552"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `last_name` `last_name` VARCHAR(100) NOT NULL;","timestamp":"2025-07-12 14:35:52:3552"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `subscription_plan` `subscription_plan` ENUM('free', 'premium', 'enterprise') NOT NULL DEFAULT 'free';","timestamp":"2025-07-12 14:35:52:3552"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `is_verified` `is_verified` TINYINT(1) NOT NULL DEFAULT false;","timestamp":"2025-07-12 14:35:52:3552"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `verification_token` `verification_token` VARCHAR(255);","timestamp":"2025-07-12 14:35:53:3553"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `reset_password_token` `reset_password_token` VARCHAR(255);","timestamp":"2025-07-12 14:35:53:3553"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `reset_password_expires` `reset_password_expires` DATETIME;","timestamp":"2025-07-12 14:35:53:3553"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `last_login_at` `last_login_at` DATETIME;","timestamp":"2025-07-12 14:35:54:3554"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `created_at` `created_at` DATETIME NOT NULL;","timestamp":"2025-07-12 14:35:54:3554"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `updated_at` `updated_at` DATETIME NOT NULL;","timestamp":"2025-07-12 14:35:54:3554"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SHOW INDEX FROM `users`","timestamp":"2025-07-12 14:35:54:3554"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT TABLE_NAME FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_TYPE = 'BASE TABLE' AND TABLE_NAME = 'processing_sessions' AND TABLE_SCHEMA = 'pdf'","timestamp":"2025-07-12 14:35:54:3554"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SHOW FULL COLUMNS FROM `processing_sessions`;","timestamp":"2025-07-12 14:35:54:3554"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT CONSTRAINT_NAME as constraint_name,CONSTRAINT_NAME as constraintName,CONSTRAINT_SCHEMA as constraintSchema,CONSTRAINT_SCHEMA as constraintCatalog,TABLE_NAME as tableName,TABLE_SCHEMA as tableSchema,TABLE_SCHEMA as tableCatalog,COLUMN_NAME as columnName,REFERENCED_TABLE_SCHEMA as referencedTableSchema,REFERENCED_TABLE_SCHEMA as referencedTableCatalog,REFERENCED_TABLE_NAME as referencedTableName,REFERENCED_COLUMN_NAME as referencedColumnName FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE where TABLE_NAME = 'processing_sessions' AND CONSTRAINT_NAME!='PRIMARY' AND CONSTRAINT_SCHEMA='pdf' AND REFERENCED_TABLE_NAME IS NOT NULL;","timestamp":"2025-07-12 14:35:54:3554"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT CONSTRAINT_CATALOG AS constraintCatalog, CONSTRAINT_NAME AS constraintName, CONSTRAINT_SCHEMA AS constraintSchema, CONSTRAINT_TYPE AS constraintType, TABLE_NAME AS tableName, TABLE_SCHEMA AS tableSchema from INFORMATION_SCHEMA.TABLE_CONSTRAINTS WHERE table_name='processing_sessions' AND constraint_name = 'processing_sessions_ibfk_1' AND TABLE_SCHEMA = 'pdf';","timestamp":"2025-07-12 14:35:54:3554"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `processing_sessions` DROP FOREIGN KEY `processing_sessions_ibfk_1`;","timestamp":"2025-07-12 14:35:54:3554"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `processing_sessions` ADD FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE ON UPDATE CASCADE;","timestamp":"2025-07-12 14:35:55:3555"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `processing_sessions` CHANGE `session_token` `session_token` VARCHAR(255) NOT NULL UNIQUE;","timestamp":"2025-07-12 14:35:58:3558"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `processing_sessions` CHANGE `status` `status` ENUM('active', 'completed', 'expired') NOT NULL DEFAULT 'active';","timestamp":"2025-07-12 14:35:58:3558"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `processing_sessions` CHANGE `files_processed` `files_processed` INTEGER NOT NULL DEFAULT 0;","timestamp":"2025-07-12 14:35:58:3558"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `processing_sessions` CHANGE `quota_consumed` `quota_consumed` INTEGER NOT NULL DEFAULT 0;","timestamp":"2025-07-12 14:35:59:3559"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `processing_sessions` CHANGE `can_download` `can_download` TINYINT(1) NOT NULL DEFAULT true;","timestamp":"2025-07-12 14:35:59:3559"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `processing_sessions` CHANGE `expires_at` `expires_at` DATETIME NOT NULL;","timestamp":"2025-07-12 14:35:59:3559"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `processing_sessions` CHANGE `created_at` `created_at` DATETIME NOT NULL;","timestamp":"2025-07-12 14:35:59:3559"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `processing_sessions` CHANGE `updated_at` `updated_at` DATETIME NOT NULL;","timestamp":"2025-07-12 14:35:59:3559"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SHOW INDEX FROM `processing_sessions`","timestamp":"2025-07-12 14:35:59:3559"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT TABLE_NAME FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_TYPE = 'BASE TABLE' AND TABLE_NAME = 'usage_records' AND TABLE_SCHEMA = 'pdf'","timestamp":"2025-07-12 14:36:00:360"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SHOW FULL COLUMNS FROM `usage_records`;","timestamp":"2025-07-12 14:36:00:360"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT CONSTRAINT_NAME as constraint_name,CONSTRAINT_NAME as constraintName,CONSTRAINT_SCHEMA as constraintSchema,CONSTRAINT_SCHEMA as constraintCatalog,TABLE_NAME as tableName,TABLE_SCHEMA as tableSchema,TABLE_SCHEMA as tableCatalog,COLUMN_NAME as columnName,REFERENCED_TABLE_SCHEMA as referencedTableSchema,REFERENCED_TABLE_SCHEMA as referencedTableCatalog,REFERENCED_TABLE_NAME as referencedTableName,REFERENCED_COLUMN_NAME as referencedColumnName FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE where TABLE_NAME = 'usage_records' AND CONSTRAINT_NAME!='PRIMARY' AND CONSTRAINT_SCHEMA='pdf' AND REFERENCED_TABLE_NAME IS NOT NULL;","timestamp":"2025-07-12 14:36:00:360"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT CONSTRAINT_CATALOG AS constraintCatalog, CONSTRAINT_NAME AS constraintName, CONSTRAINT_SCHEMA AS constraintSchema, CONSTRAINT_TYPE AS constraintType, TABLE_NAME AS tableName, TABLE_SCHEMA AS tableSchema from INFORMATION_SCHEMA.TABLE_CONSTRAINTS WHERE table_name='usage_records' AND constraint_name = 'usage_records_ibfk_1' AND TABLE_SCHEMA = 'pdf';","timestamp":"2025-07-12 14:36:00:360"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `usage_records` DROP FOREIGN KEY `usage_records_ibfk_1`;","timestamp":"2025-07-12 14:36:00:360"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `usage_records` ADD FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE ON UPDATE CASCADE;","timestamp":"2025-07-12 14:36:00:360"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `usage_records` CHANGE `tool_name` `tool_name` VARCHAR(100) NOT NULL;","timestamp":"2025-07-12 14:36:02:362"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `usage_records` CHANGE `file_size_mb` `file_size_mb` DECIMAL(10,2) NOT NULL;","timestamp":"2025-07-12 14:36:02:362"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `usage_records` CHANGE `processing_time_seconds` `processing_time_seconds` INTEGER NOT NULL;","timestamp":"2025-07-12 14:36:02:362"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `usage_records` CHANGE `input_file_count` `input_file_count` INTEGER NOT NULL;","timestamp":"2025-07-12 14:36:03:363"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `usage_records` CHANGE `output_file_count` `output_file_count` INTEGER NOT NULL DEFAULT 0;","timestamp":"2025-07-12 14:36:03:363"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `usage_records` CHANGE `success` `success` TINYINT(1) NOT NULL DEFAULT true;","timestamp":"2025-07-12 14:36:03:363"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `usage_records` CHANGE `error_message` `error_message` TEXT;","timestamp":"2025-07-12 14:36:03:363"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `usage_records` CHANGE `parameters` `parameters` JSON;","timestamp":"2025-07-12 14:36:03:363"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `usage_records` CHANGE `created_at` `created_at` DATETIME NOT NULL;","timestamp":"2025-07-12 14:36:03:363"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `usage_records` CHANGE `updated_at` `updated_at` DATETIME NOT NULL;","timestamp":"2025-07-12 14:36:04:364"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SHOW INDEX FROM `usage_records`","timestamp":"2025-07-12 14:36:04:364"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mDatabase initialized successfully\u001b[39m","timestamp":"2025-07-12 14:36:04:364"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mServer running on port 3001\u001b[39m","timestamp":"2025-07-12 14:36:04:364"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mEnvironment: development\u001b[39m","timestamp":"2025-07-12 14:36:04:364"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mDatabase connected and synchronized\u001b[39m","timestamp":"2025-07-12 14:36:04:364"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT 1+1 AS result","timestamp":"2025-07-12 14:36:35:3635"}
{"database":"pdf","host":"127.0.0.1","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mDatabase connection established successfully\u001b[39m","port":3306,"timestamp":"2025-07-12 14:36:35:3635"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT TABLE_NAME FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_TYPE = 'BASE TABLE' AND TABLE_NAME = 'users' AND TABLE_SCHEMA = 'pdf'","timestamp":"2025-07-12 14:36:35:3635"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SHOW FULL COLUMNS FROM `users`;","timestamp":"2025-07-12 14:36:35:3635"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT CONSTRAINT_NAME as constraint_name,CONSTRAINT_NAME as constraintName,CONSTRAINT_SCHEMA as constraintSchema,CONSTRAINT_SCHEMA as constraintCatalog,TABLE_NAME as tableName,TABLE_SCHEMA as tableSchema,TABLE_SCHEMA as tableCatalog,COLUMN_NAME as columnName,REFERENCED_TABLE_SCHEMA as referencedTableSchema,REFERENCED_TABLE_SCHEMA as referencedTableCatalog,REFERENCED_TABLE_NAME as referencedTableName,REFERENCED_COLUMN_NAME as referencedColumnName FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE where TABLE_NAME = 'users' AND CONSTRAINT_NAME!='PRIMARY' AND CONSTRAINT_SCHEMA='pdf' AND REFERENCED_TABLE_NAME IS NOT NULL;","timestamp":"2025-07-12 14:36:35:3635"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `email` `email` VARCHAR(255) NOT NULL UNIQUE;","timestamp":"2025-07-12 14:36:35:3635"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `password_hash` `password_hash` VARCHAR(255) NOT NULL;","timestamp":"2025-07-12 14:36:36:3636"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `first_name` `first_name` VARCHAR(100) NOT NULL;","timestamp":"2025-07-12 14:36:37:3637"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `last_name` `last_name` VARCHAR(100) NOT NULL;","timestamp":"2025-07-12 14:36:37:3637"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `subscription_plan` `subscription_plan` ENUM('free', 'premium', 'enterprise') NOT NULL DEFAULT 'free';","timestamp":"2025-07-12 14:36:38:3638"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `is_verified` `is_verified` TINYINT(1) NOT NULL DEFAULT false;","timestamp":"2025-07-12 14:36:38:3638"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `verification_token` `verification_token` VARCHAR(255);","timestamp":"2025-07-12 14:36:38:3638"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `reset_password_token` `reset_password_token` VARCHAR(255);","timestamp":"2025-07-12 14:36:38:3638"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `reset_password_expires` `reset_password_expires` DATETIME;","timestamp":"2025-07-12 14:36:38:3638"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `last_login_at` `last_login_at` DATETIME;","timestamp":"2025-07-12 14:36:38:3638"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `created_at` `created_at` DATETIME NOT NULL;","timestamp":"2025-07-12 14:36:39:3639"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `updated_at` `updated_at` DATETIME NOT NULL;","timestamp":"2025-07-12 14:36:39:3639"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SHOW INDEX FROM `users`","timestamp":"2025-07-12 14:36:39:3639"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT TABLE_NAME FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_TYPE = 'BASE TABLE' AND TABLE_NAME = 'processing_sessions' AND TABLE_SCHEMA = 'pdf'","timestamp":"2025-07-12 14:36:39:3639"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SHOW FULL COLUMNS FROM `processing_sessions`;","timestamp":"2025-07-12 14:36:39:3639"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT CONSTRAINT_NAME as constraint_name,CONSTRAINT_NAME as constraintName,CONSTRAINT_SCHEMA as constraintSchema,CONSTRAINT_SCHEMA as constraintCatalog,TABLE_NAME as tableName,TABLE_SCHEMA as tableSchema,TABLE_SCHEMA as tableCatalog,COLUMN_NAME as columnName,REFERENCED_TABLE_SCHEMA as referencedTableSchema,REFERENCED_TABLE_SCHEMA as referencedTableCatalog,REFERENCED_TABLE_NAME as referencedTableName,REFERENCED_COLUMN_NAME as referencedColumnName FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE where TABLE_NAME = 'processing_sessions' AND CONSTRAINT_NAME!='PRIMARY' AND CONSTRAINT_SCHEMA='pdf' AND REFERENCED_TABLE_NAME IS NOT NULL;","timestamp":"2025-07-12 14:36:39:3639"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT CONSTRAINT_CATALOG AS constraintCatalog, CONSTRAINT_NAME AS constraintName, CONSTRAINT_SCHEMA AS constraintSchema, CONSTRAINT_TYPE AS constraintType, TABLE_NAME AS tableName, TABLE_SCHEMA AS tableSchema from INFORMATION_SCHEMA.TABLE_CONSTRAINTS WHERE table_name='processing_sessions' AND constraint_name = 'processing_sessions_ibfk_1' AND TABLE_SCHEMA = 'pdf';","timestamp":"2025-07-12 14:36:39:3639"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `processing_sessions` DROP FOREIGN KEY `processing_sessions_ibfk_1`;","timestamp":"2025-07-12 14:36:39:3639"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `processing_sessions` ADD FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE ON UPDATE CASCADE;","timestamp":"2025-07-12 14:36:39:3639"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `processing_sessions` CHANGE `session_token` `session_token` VARCHAR(255) NOT NULL UNIQUE;","timestamp":"2025-07-12 14:36:42:3642"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `processing_sessions` CHANGE `status` `status` ENUM('active', 'completed', 'expired') NOT NULL DEFAULT 'active';","timestamp":"2025-07-12 14:36:42:3642"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `processing_sessions` CHANGE `files_processed` `files_processed` INTEGER NOT NULL DEFAULT 0;","timestamp":"2025-07-12 14:36:42:3642"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `processing_sessions` CHANGE `quota_consumed` `quota_consumed` INTEGER NOT NULL DEFAULT 0;","timestamp":"2025-07-12 14:36:42:3642"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `processing_sessions` CHANGE `can_download` `can_download` TINYINT(1) NOT NULL DEFAULT true;","timestamp":"2025-07-12 14:36:43:3643"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `processing_sessions` CHANGE `expires_at` `expires_at` DATETIME NOT NULL;","timestamp":"2025-07-12 14:36:43:3643"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `processing_sessions` CHANGE `created_at` `created_at` DATETIME NOT NULL;","timestamp":"2025-07-12 14:36:43:3643"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `processing_sessions` CHANGE `updated_at` `updated_at` DATETIME NOT NULL;","timestamp":"2025-07-12 14:36:43:3643"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SHOW INDEX FROM `processing_sessions`","timestamp":"2025-07-12 14:36:43:3643"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT TABLE_NAME FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_TYPE = 'BASE TABLE' AND TABLE_NAME = 'usage_records' AND TABLE_SCHEMA = 'pdf'","timestamp":"2025-07-12 14:36:43:3643"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SHOW FULL COLUMNS FROM `usage_records`;","timestamp":"2025-07-12 14:36:43:3643"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT CONSTRAINT_NAME as constraint_name,CONSTRAINT_NAME as constraintName,CONSTRAINT_SCHEMA as constraintSchema,CONSTRAINT_SCHEMA as constraintCatalog,TABLE_NAME as tableName,TABLE_SCHEMA as tableSchema,TABLE_SCHEMA as tableCatalog,COLUMN_NAME as columnName,REFERENCED_TABLE_SCHEMA as referencedTableSchema,REFERENCED_TABLE_SCHEMA as referencedTableCatalog,REFERENCED_TABLE_NAME as referencedTableName,REFERENCED_COLUMN_NAME as referencedColumnName FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE where TABLE_NAME = 'usage_records' AND CONSTRAINT_NAME!='PRIMARY' AND CONSTRAINT_SCHEMA='pdf' AND REFERENCED_TABLE_NAME IS NOT NULL;","timestamp":"2025-07-12 14:36:43:3643"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT CONSTRAINT_CATALOG AS constraintCatalog, CONSTRAINT_NAME AS constraintName, CONSTRAINT_SCHEMA AS constraintSchema, CONSTRAINT_TYPE AS constraintType, TABLE_NAME AS tableName, TABLE_SCHEMA AS tableSchema from INFORMATION_SCHEMA.TABLE_CONSTRAINTS WHERE table_name='usage_records' AND constraint_name = 'usage_records_ibfk_1' AND TABLE_SCHEMA = 'pdf';","timestamp":"2025-07-12 14:36:43:3643"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `usage_records` DROP FOREIGN KEY `usage_records_ibfk_1`;","timestamp":"2025-07-12 14:36:43:3643"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `usage_records` ADD FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE ON UPDATE CASCADE;","timestamp":"2025-07-12 14:36:43:3643"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `usage_records` CHANGE `tool_name` `tool_name` VARCHAR(100) NOT NULL;","timestamp":"2025-07-12 14:36:46:3646"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `usage_records` CHANGE `file_size_mb` `file_size_mb` DECIMAL(10,2) NOT NULL;","timestamp":"2025-07-12 14:36:46:3646"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `usage_records` CHANGE `processing_time_seconds` `processing_time_seconds` INTEGER NOT NULL;","timestamp":"2025-07-12 14:36:46:3646"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `usage_records` CHANGE `input_file_count` `input_file_count` INTEGER NOT NULL;","timestamp":"2025-07-12 14:36:46:3646"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `usage_records` CHANGE `output_file_count` `output_file_count` INTEGER NOT NULL DEFAULT 0;","timestamp":"2025-07-12 14:36:46:3646"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `usage_records` CHANGE `success` `success` TINYINT(1) NOT NULL DEFAULT true;","timestamp":"2025-07-12 14:36:46:3646"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `usage_records` CHANGE `error_message` `error_message` TEXT;","timestamp":"2025-07-12 14:36:46:3646"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `usage_records` CHANGE `parameters` `parameters` JSON;","timestamp":"2025-07-12 14:36:46:3646"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `usage_records` CHANGE `created_at` `created_at` DATETIME NOT NULL;","timestamp":"2025-07-12 14:36:47:3647"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `usage_records` CHANGE `updated_at` `updated_at` DATETIME NOT NULL;","timestamp":"2025-07-12 14:36:47:3647"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SHOW INDEX FROM `usage_records`","timestamp":"2025-07-12 14:36:47:3647"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mDatabase initialized successfully\u001b[39m","timestamp":"2025-07-12 14:36:47:3647"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mServer running on port 3001\u001b[39m","timestamp":"2025-07-12 14:36:47:3647"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mEnvironment: development\u001b[39m","timestamp":"2025-07-12 14:36:47:3647"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mDatabase connected and synchronized\u001b[39m","timestamp":"2025-07-12 14:36:47:3647"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT 1+1 AS result","timestamp":"2025-07-12 14:42:55:4255"}
{"database":"pdf","host":"127.0.0.1","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mDatabase connection established successfully\u001b[39m","port":3306,"timestamp":"2025-07-12 14:42:55:4255"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT TABLE_NAME FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_TYPE = 'BASE TABLE' AND TABLE_NAME = 'users' AND TABLE_SCHEMA = 'pdf'","timestamp":"2025-07-12 14:42:55:4255"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SHOW FULL COLUMNS FROM `users`;","timestamp":"2025-07-12 14:42:55:4255"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT CONSTRAINT_NAME as constraint_name,CONSTRAINT_NAME as constraintName,CONSTRAINT_SCHEMA as constraintSchema,CONSTRAINT_SCHEMA as constraintCatalog,TABLE_NAME as tableName,TABLE_SCHEMA as tableSchema,TABLE_SCHEMA as tableCatalog,COLUMN_NAME as columnName,REFERENCED_TABLE_SCHEMA as referencedTableSchema,REFERENCED_TABLE_SCHEMA as referencedTableCatalog,REFERENCED_TABLE_NAME as referencedTableName,REFERENCED_COLUMN_NAME as referencedColumnName FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE where TABLE_NAME = 'users' AND CONSTRAINT_NAME!='PRIMARY' AND CONSTRAINT_SCHEMA='pdf' AND REFERENCED_TABLE_NAME IS NOT NULL;","timestamp":"2025-07-12 14:42:55:4255"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `email` `email` VARCHAR(255) NOT NULL UNIQUE;","timestamp":"2025-07-12 14:42:55:4255"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `password_hash` `password_hash` VARCHAR(255) NOT NULL;","timestamp":"2025-07-12 14:42:56:4256"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `first_name` `first_name` VARCHAR(100) NOT NULL;","timestamp":"2025-07-12 14:42:56:4256"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `last_name` `last_name` VARCHAR(100) NOT NULL;","timestamp":"2025-07-12 14:42:57:4257"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `subscription_plan` `subscription_plan` ENUM('free', 'premium', 'enterprise') NOT NULL DEFAULT 'free';","timestamp":"2025-07-12 14:42:58:4258"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `is_verified` `is_verified` TINYINT(1) NOT NULL DEFAULT false;","timestamp":"2025-07-12 14:42:58:4258"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `verification_token` `verification_token` VARCHAR(255);","timestamp":"2025-07-12 14:42:58:4258"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `reset_password_token` `reset_password_token` VARCHAR(255);","timestamp":"2025-07-12 14:42:58:4258"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `reset_password_expires` `reset_password_expires` DATETIME;","timestamp":"2025-07-12 14:42:58:4258"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `last_login_at` `last_login_at` DATETIME;","timestamp":"2025-07-12 14:42:58:4258"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `created_at` `created_at` DATETIME NOT NULL;","timestamp":"2025-07-12 14:42:59:4259"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `updated_at` `updated_at` DATETIME NOT NULL;","timestamp":"2025-07-12 14:42:59:4259"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SHOW INDEX FROM `users`","timestamp":"2025-07-12 14:42:59:4259"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT TABLE_NAME FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_TYPE = 'BASE TABLE' AND TABLE_NAME = 'processing_sessions' AND TABLE_SCHEMA = 'pdf'","timestamp":"2025-07-12 14:42:59:4259"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SHOW FULL COLUMNS FROM `processing_sessions`;","timestamp":"2025-07-12 14:42:59:4259"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT CONSTRAINT_NAME as constraint_name,CONSTRAINT_NAME as constraintName,CONSTRAINT_SCHEMA as constraintSchema,CONSTRAINT_SCHEMA as constraintCatalog,TABLE_NAME as tableName,TABLE_SCHEMA as tableSchema,TABLE_SCHEMA as tableCatalog,COLUMN_NAME as columnName,REFERENCED_TABLE_SCHEMA as referencedTableSchema,REFERENCED_TABLE_SCHEMA as referencedTableCatalog,REFERENCED_TABLE_NAME as referencedTableName,REFERENCED_COLUMN_NAME as referencedColumnName FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE where TABLE_NAME = 'processing_sessions' AND CONSTRAINT_NAME!='PRIMARY' AND CONSTRAINT_SCHEMA='pdf' AND REFERENCED_TABLE_NAME IS NOT NULL;","timestamp":"2025-07-12 14:42:59:4259"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT CONSTRAINT_CATALOG AS constraintCatalog, CONSTRAINT_NAME AS constraintName, CONSTRAINT_SCHEMA AS constraintSchema, CONSTRAINT_TYPE AS constraintType, TABLE_NAME AS tableName, TABLE_SCHEMA AS tableSchema from INFORMATION_SCHEMA.TABLE_CONSTRAINTS WHERE table_name='processing_sessions' AND constraint_name = 'processing_sessions_ibfk_1' AND TABLE_SCHEMA = 'pdf';","timestamp":"2025-07-12 14:42:59:4259"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `processing_sessions` DROP FOREIGN KEY `processing_sessions_ibfk_1`;","timestamp":"2025-07-12 14:42:59:4259"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `processing_sessions` ADD FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE ON UPDATE CASCADE;","timestamp":"2025-07-12 14:42:59:4259"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `processing_sessions` CHANGE `session_token` `session_token` VARCHAR(255) NOT NULL UNIQUE;","timestamp":"2025-07-12 14:43:02:432"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `processing_sessions` CHANGE `status` `status` ENUM('active', 'completed', 'expired') NOT NULL DEFAULT 'active';","timestamp":"2025-07-12 14:43:03:433"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `processing_sessions` CHANGE `files_processed` `files_processed` INTEGER NOT NULL DEFAULT 0;","timestamp":"2025-07-12 14:43:03:433"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `processing_sessions` CHANGE `quota_consumed` `quota_consumed` INTEGER NOT NULL DEFAULT 0;","timestamp":"2025-07-12 14:43:03:433"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `processing_sessions` CHANGE `can_download` `can_download` TINYINT(1) NOT NULL DEFAULT true;","timestamp":"2025-07-12 14:43:03:433"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `processing_sessions` CHANGE `expires_at` `expires_at` DATETIME NOT NULL;","timestamp":"2025-07-12 14:43:03:433"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `processing_sessions` CHANGE `created_at` `created_at` DATETIME NOT NULL;","timestamp":"2025-07-12 14:43:03:433"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `processing_sessions` CHANGE `updated_at` `updated_at` DATETIME NOT NULL;","timestamp":"2025-07-12 14:43:04:434"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SHOW INDEX FROM `processing_sessions`","timestamp":"2025-07-12 14:43:04:434"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT TABLE_NAME FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_TYPE = 'BASE TABLE' AND TABLE_NAME = 'usage_records' AND TABLE_SCHEMA = 'pdf'","timestamp":"2025-07-12 14:43:04:434"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SHOW FULL COLUMNS FROM `usage_records`;","timestamp":"2025-07-12 14:43:04:434"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT CONSTRAINT_NAME as constraint_name,CONSTRAINT_NAME as constraintName,CONSTRAINT_SCHEMA as constraintSchema,CONSTRAINT_SCHEMA as constraintCatalog,TABLE_NAME as tableName,TABLE_SCHEMA as tableSchema,TABLE_SCHEMA as tableCatalog,COLUMN_NAME as columnName,REFERENCED_TABLE_SCHEMA as referencedTableSchema,REFERENCED_TABLE_SCHEMA as referencedTableCatalog,REFERENCED_TABLE_NAME as referencedTableName,REFERENCED_COLUMN_NAME as referencedColumnName FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE where TABLE_NAME = 'usage_records' AND CONSTRAINT_NAME!='PRIMARY' AND CONSTRAINT_SCHEMA='pdf' AND REFERENCED_TABLE_NAME IS NOT NULL;","timestamp":"2025-07-12 14:43:04:434"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT CONSTRAINT_CATALOG AS constraintCatalog, CONSTRAINT_NAME AS constraintName, CONSTRAINT_SCHEMA AS constraintSchema, CONSTRAINT_TYPE AS constraintType, TABLE_NAME AS tableName, TABLE_SCHEMA AS tableSchema from INFORMATION_SCHEMA.TABLE_CONSTRAINTS WHERE table_name='usage_records' AND constraint_name = 'usage_records_ibfk_1' AND TABLE_SCHEMA = 'pdf';","timestamp":"2025-07-12 14:43:04:434"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `usage_records` DROP FOREIGN KEY `usage_records_ibfk_1`;","timestamp":"2025-07-12 14:43:04:434"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `usage_records` ADD FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE ON UPDATE CASCADE;","timestamp":"2025-07-12 14:43:04:434"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `usage_records` CHANGE `tool_name` `tool_name` VARCHAR(100) NOT NULL;","timestamp":"2025-07-12 14:43:06:436"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `usage_records` CHANGE `file_size_mb` `file_size_mb` DECIMAL(10,2) NOT NULL;","timestamp":"2025-07-12 14:43:06:436"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `usage_records` CHANGE `processing_time_seconds` `processing_time_seconds` INTEGER NOT NULL;","timestamp":"2025-07-12 14:43:06:436"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `usage_records` CHANGE `input_file_count` `input_file_count` INTEGER NOT NULL;","timestamp":"2025-07-12 14:43:06:436"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `usage_records` CHANGE `output_file_count` `output_file_count` INTEGER NOT NULL DEFAULT 0;","timestamp":"2025-07-12 14:43:07:437"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `usage_records` CHANGE `success` `success` TINYINT(1) NOT NULL DEFAULT true;","timestamp":"2025-07-12 14:43:07:437"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `usage_records` CHANGE `error_message` `error_message` TEXT;","timestamp":"2025-07-12 14:43:07:437"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `usage_records` CHANGE `parameters` `parameters` JSON;","timestamp":"2025-07-12 14:43:07:437"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `usage_records` CHANGE `created_at` `created_at` DATETIME NOT NULL;","timestamp":"2025-07-12 14:43:07:437"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `usage_records` CHANGE `updated_at` `updated_at` DATETIME NOT NULL;","timestamp":"2025-07-12 14:43:08:438"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SHOW INDEX FROM `usage_records`","timestamp":"2025-07-12 14:43:08:438"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mDatabase initialized successfully\u001b[39m","timestamp":"2025-07-12 14:43:08:438"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mServer running on port 3001\u001b[39m","timestamp":"2025-07-12 14:43:08:438"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mEnvironment: development\u001b[39m","timestamp":"2025-07-12 14:43:08:438"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mDatabase connected and synchronized\u001b[39m","timestamp":"2025-07-12 14:43:08:438"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT `id`, `email`, `password_hash`, `first_name`, `last_name`, `subscription_plan`, `is_verified`, `verification_token`, `reset_password_token`, `reset_password_expires`, `last_login_at`, `created_at` AS `createdAt`, `updated_at` AS `updatedAt` FROM `users` AS `User` WHERE `User`.`email` = '<EMAIL>';","timestamp":"2025-07-12 14:49:50:4950"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): UPDATE `users` SET `last_login_at`=?,`updated_at`=? WHERE `id` = ?","timestamp":"2025-07-12 14:49:50:4950"}
{"email":"<EMAIL>","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mUser logged in successfully\u001b[39m","timestamp":"2025-07-12 14:49:50:4950","userId":"5cf9306f-6b16-4a16-bc51-85aec3177a46"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT `id`, `email`, `password_hash`, `first_name`, `last_name`, `subscription_plan`, `is_verified`, `verification_token`, `reset_password_token`, `reset_password_expires`, `last_login_at`, `created_at` AS `createdAt`, `updated_at` AS `updatedAt` FROM `users` AS `User` WHERE `User`.`email` = '<EMAIL>';","timestamp":"2025-07-12 14:49:50:4950"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): UPDATE `users` SET `last_login_at`=?,`updated_at`=? WHERE `id` = ?","timestamp":"2025-07-12 14:49:51:4951"}
{"email":"<EMAIL>","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mUser logged in successfully\u001b[39m","timestamp":"2025-07-12 14:49:51:4951","userId":"5cf9306f-6b16-4a16-bc51-85aec3177a46"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT `id`, `user_id`, `tool_name`, `file_size_mb`, `processing_time_seconds`, `input_file_count`, `output_file_count`, `success`, `error_message`, `parameters`, `created_at` AS `createdAt`, `updated_at` AS `updatedAt` FROM `usage_records` AS `UsageRecord` WHERE `UsageRecord`.`user_id` = '5cf9306f-6b16-4a16-bc51-85aec3177a46' AND `UsageRecord`.`created_at` >= '2025-07-11 23:00:00';","timestamp":"2025-07-12 14:49:51:4951"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): INSERT INTO `processing_sessions` (`id`,`user_id`,`session_token`,`status`,`files_processed`,`quota_consumed`,`can_download`,`expires_at`,`created_at`,`updated_at`) VALUES (?,?,?,?,?,?,?,?,?,?);","timestamp":"2025-07-12 14:49:51:4951"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): UPDATE `processing_sessions` SET `status`=?,`updated_at`=? WHERE `expires_at` < '2025-07-12 14:42:55' AND `status` = ?","timestamp":"2025-07-12 15:42:55:4255"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mExpired sessions cleaned up\u001b[39m","timestamp":"2025-07-12 15:42:55:4255"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT 1+1 AS result","timestamp":"2025-07-12 16:22:01:221"}
{"database":"pdf","host":"127.0.0.1","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mDatabase connection established successfully\u001b[39m","port":3306,"timestamp":"2025-07-12 16:22:01:221"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT TABLE_NAME FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_TYPE = 'BASE TABLE' AND TABLE_NAME = 'users' AND TABLE_SCHEMA = 'pdf'","timestamp":"2025-07-12 16:22:01:221"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SHOW FULL COLUMNS FROM `users`;","timestamp":"2025-07-12 16:22:01:221"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT CONSTRAINT_NAME as constraint_name,CONSTRAINT_NAME as constraintName,CONSTRAINT_SCHEMA as constraintSchema,CONSTRAINT_SCHEMA as constraintCatalog,TABLE_NAME as tableName,TABLE_SCHEMA as tableSchema,TABLE_SCHEMA as tableCatalog,COLUMN_NAME as columnName,REFERENCED_TABLE_SCHEMA as referencedTableSchema,REFERENCED_TABLE_SCHEMA as referencedTableCatalog,REFERENCED_TABLE_NAME as referencedTableName,REFERENCED_COLUMN_NAME as referencedColumnName FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE where TABLE_NAME = 'users' AND CONSTRAINT_NAME!='PRIMARY' AND CONSTRAINT_SCHEMA='pdf' AND REFERENCED_TABLE_NAME IS NOT NULL;","timestamp":"2025-07-12 16:22:01:221"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `email` `email` VARCHAR(255) NOT NULL UNIQUE;","timestamp":"2025-07-12 16:22:01:221"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `password_hash` `password_hash` VARCHAR(255) NOT NULL;","timestamp":"2025-07-12 16:22:01:221"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `first_name` `first_name` VARCHAR(100) NOT NULL;","timestamp":"2025-07-12 16:22:02:222"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `last_name` `last_name` VARCHAR(100) NOT NULL;","timestamp":"2025-07-12 16:22:02:222"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `subscription_plan` `subscription_plan` ENUM('free', 'premium', 'enterprise') NOT NULL DEFAULT 'free';","timestamp":"2025-07-12 16:22:02:222"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `is_verified` `is_verified` TINYINT(1) NOT NULL DEFAULT false;","timestamp":"2025-07-12 16:22:02:222"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `verification_token` `verification_token` VARCHAR(255);","timestamp":"2025-07-12 16:22:04:224"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `reset_password_token` `reset_password_token` VARCHAR(255);","timestamp":"2025-07-12 16:22:04:224"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `reset_password_expires` `reset_password_expires` DATETIME;","timestamp":"2025-07-12 16:22:04:224"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `last_login_at` `last_login_at` DATETIME;","timestamp":"2025-07-12 16:22:04:224"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `created_at` `created_at` DATETIME NOT NULL;","timestamp":"2025-07-12 16:22:04:224"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `updated_at` `updated_at` DATETIME NOT NULL;","timestamp":"2025-07-12 16:22:04:224"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SHOW INDEX FROM `users`","timestamp":"2025-07-12 16:22:05:225"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT TABLE_NAME FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_TYPE = 'BASE TABLE' AND TABLE_NAME = 'processing_sessions' AND TABLE_SCHEMA = 'pdf'","timestamp":"2025-07-12 16:22:05:225"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SHOW FULL COLUMNS FROM `processing_sessions`;","timestamp":"2025-07-12 16:22:05:225"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT CONSTRAINT_NAME as constraint_name,CONSTRAINT_NAME as constraintName,CONSTRAINT_SCHEMA as constraintSchema,CONSTRAINT_SCHEMA as constraintCatalog,TABLE_NAME as tableName,TABLE_SCHEMA as tableSchema,TABLE_SCHEMA as tableCatalog,COLUMN_NAME as columnName,REFERENCED_TABLE_SCHEMA as referencedTableSchema,REFERENCED_TABLE_SCHEMA as referencedTableCatalog,REFERENCED_TABLE_NAME as referencedTableName,REFERENCED_COLUMN_NAME as referencedColumnName FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE where TABLE_NAME = 'processing_sessions' AND CONSTRAINT_NAME!='PRIMARY' AND CONSTRAINT_SCHEMA='pdf' AND REFERENCED_TABLE_NAME IS NOT NULL;","timestamp":"2025-07-12 16:22:05:225"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT CONSTRAINT_CATALOG AS constraintCatalog, CONSTRAINT_NAME AS constraintName, CONSTRAINT_SCHEMA AS constraintSchema, CONSTRAINT_TYPE AS constraintType, TABLE_NAME AS tableName, TABLE_SCHEMA AS tableSchema from INFORMATION_SCHEMA.TABLE_CONSTRAINTS WHERE table_name='processing_sessions' AND constraint_name = 'processing_sessions_ibfk_1' AND TABLE_SCHEMA = 'pdf';","timestamp":"2025-07-12 16:22:05:225"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `processing_sessions` DROP FOREIGN KEY `processing_sessions_ibfk_1`;","timestamp":"2025-07-12 16:22:05:225"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `processing_sessions` ADD FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE ON UPDATE CASCADE;","timestamp":"2025-07-12 16:22:05:225"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `processing_sessions` CHANGE `session_token` `session_token` VARCHAR(255) NOT NULL UNIQUE;","timestamp":"2025-07-12 16:22:07:227"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `processing_sessions` CHANGE `status` `status` ENUM('active', 'completed', 'expired') NOT NULL DEFAULT 'active';","timestamp":"2025-07-12 16:22:08:228"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `processing_sessions` CHANGE `files_processed` `files_processed` INTEGER NOT NULL DEFAULT 0;","timestamp":"2025-07-12 16:22:08:228"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `processing_sessions` CHANGE `quota_consumed` `quota_consumed` INTEGER NOT NULL DEFAULT 0;","timestamp":"2025-07-12 16:22:08:228"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `processing_sessions` CHANGE `can_download` `can_download` TINYINT(1) NOT NULL DEFAULT true;","timestamp":"2025-07-12 16:22:08:228"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `processing_sessions` CHANGE `expires_at` `expires_at` DATETIME NOT NULL;","timestamp":"2025-07-12 16:22:09:229"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `processing_sessions` CHANGE `created_at` `created_at` DATETIME NOT NULL;","timestamp":"2025-07-12 16:22:09:229"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `processing_sessions` CHANGE `updated_at` `updated_at` DATETIME NOT NULL;","timestamp":"2025-07-12 16:22:09:229"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SHOW INDEX FROM `processing_sessions`","timestamp":"2025-07-12 16:22:09:229"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT TABLE_NAME FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_TYPE = 'BASE TABLE' AND TABLE_NAME = 'usage_records' AND TABLE_SCHEMA = 'pdf'","timestamp":"2025-07-12 16:22:09:229"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SHOW FULL COLUMNS FROM `usage_records`;","timestamp":"2025-07-12 16:22:09:229"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT CONSTRAINT_NAME as constraint_name,CONSTRAINT_NAME as constraintName,CONSTRAINT_SCHEMA as constraintSchema,CONSTRAINT_SCHEMA as constraintCatalog,TABLE_NAME as tableName,TABLE_SCHEMA as tableSchema,TABLE_SCHEMA as tableCatalog,COLUMN_NAME as columnName,REFERENCED_TABLE_SCHEMA as referencedTableSchema,REFERENCED_TABLE_SCHEMA as referencedTableCatalog,REFERENCED_TABLE_NAME as referencedTableName,REFERENCED_COLUMN_NAME as referencedColumnName FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE where TABLE_NAME = 'usage_records' AND CONSTRAINT_NAME!='PRIMARY' AND CONSTRAINT_SCHEMA='pdf' AND REFERENCED_TABLE_NAME IS NOT NULL;","timestamp":"2025-07-12 16:22:09:229"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT CONSTRAINT_CATALOG AS constraintCatalog, CONSTRAINT_NAME AS constraintName, CONSTRAINT_SCHEMA AS constraintSchema, CONSTRAINT_TYPE AS constraintType, TABLE_NAME AS tableName, TABLE_SCHEMA AS tableSchema from INFORMATION_SCHEMA.TABLE_CONSTRAINTS WHERE table_name='usage_records' AND constraint_name = 'usage_records_ibfk_1' AND TABLE_SCHEMA = 'pdf';","timestamp":"2025-07-12 16:22:09:229"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `usage_records` DROP FOREIGN KEY `usage_records_ibfk_1`;","timestamp":"2025-07-12 16:22:09:229"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `usage_records` ADD FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE ON UPDATE CASCADE;","timestamp":"2025-07-12 16:22:09:229"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `usage_records` CHANGE `tool_name` `tool_name` VARCHAR(100) NOT NULL;","timestamp":"2025-07-12 16:22:11:2211"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `usage_records` CHANGE `file_size_mb` `file_size_mb` DECIMAL(10,2) NOT NULL;","timestamp":"2025-07-12 16:22:11:2211"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `usage_records` CHANGE `processing_time_seconds` `processing_time_seconds` INTEGER NOT NULL;","timestamp":"2025-07-12 16:22:11:2211"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `usage_records` CHANGE `input_file_count` `input_file_count` INTEGER NOT NULL;","timestamp":"2025-07-12 16:22:12:2212"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `usage_records` CHANGE `output_file_count` `output_file_count` INTEGER NOT NULL DEFAULT 0;","timestamp":"2025-07-12 16:22:12:2212"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `usage_records` CHANGE `success` `success` TINYINT(1) NOT NULL DEFAULT true;","timestamp":"2025-07-12 16:22:12:2212"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `usage_records` CHANGE `error_message` `error_message` TEXT;","timestamp":"2025-07-12 16:22:12:2212"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `usage_records` CHANGE `parameters` `parameters` JSON;","timestamp":"2025-07-12 16:22:12:2212"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `usage_records` CHANGE `created_at` `created_at` DATETIME NOT NULL;","timestamp":"2025-07-12 16:22:12:2212"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `usage_records` CHANGE `updated_at` `updated_at` DATETIME NOT NULL;","timestamp":"2025-07-12 16:22:12:2212"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SHOW INDEX FROM `usage_records`","timestamp":"2025-07-12 16:22:13:2213"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mDatabase initialized successfully\u001b[39m","timestamp":"2025-07-12 16:22:13:2213"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mServer running on port 3001\u001b[39m","timestamp":"2025-07-12 16:22:13:2213"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mEnvironment: development\u001b[39m","timestamp":"2025-07-12 16:22:13:2213"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mDatabase connected and synchronized\u001b[39m","timestamp":"2025-07-12 16:22:13:2213"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT `id`, `email`, `password_hash`, `first_name`, `last_name`, `subscription_plan`, `is_verified`, `verification_token`, `reset_password_token`, `reset_password_expires`, `last_login_at`, `created_at` AS `createdAt`, `updated_at` AS `updatedAt` FROM `users` AS `User` WHERE `User`.`email` = '<EMAIL>';","timestamp":"2025-07-12 16:22:22:2222"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): INSERT INTO `users` (`id`,`email`,`password_hash`,`first_name`,`last_name`,`subscription_plan`,`is_verified`,`created_at`,`updated_at`) VALUES (?,?,?,?,?,?,?,?,?);","timestamp":"2025-07-12 16:22:22:2222"}
{"email":"<EMAIL>","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mUser registered successfully\u001b[39m","timestamp":"2025-07-12 16:22:22:2222","userId":"5f5ae2ab-dafc-43f6-b992-3fd99f2d2265"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT `id`, `email`, `password_hash`, `first_name`, `last_name`, `subscription_plan`, `is_verified`, `verification_token`, `reset_password_token`, `reset_password_expires`, `last_login_at`, `created_at` AS `createdAt`, `updated_at` AS `updatedAt` FROM `users` AS `User` WHERE `User`.`email` = '<EMAIL>';","timestamp":"2025-07-12 16:22:22:2222"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): UPDATE `users` SET `last_login_at`=?,`updated_at`=? WHERE `id` = ?","timestamp":"2025-07-12 16:22:22:2222"}
{"email":"<EMAIL>","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mUser logged in successfully\u001b[39m","timestamp":"2025-07-12 16:22:23:2223","userId":"5f5ae2ab-dafc-43f6-b992-3fd99f2d2265"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT `id`, `email`, `password_hash`, `first_name`, `last_name`, `subscription_plan`, `is_verified`, `verification_token`, `reset_password_token`, `reset_password_expires`, `last_login_at`, `created_at` AS `createdAt`, `updated_at` AS `updatedAt` FROM `users` AS `User` WHERE `User`.`id` = '5f5ae2ab-dafc-43f6-b992-3fd99f2d2265';","timestamp":"2025-07-12 16:22:23:2223"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT `id`, `email`, `password_hash`, `first_name`, `last_name`, `subscription_plan`, `is_verified`, `verification_token`, `reset_password_token`, `reset_password_expires`, `last_login_at`, `created_at` AS `createdAt`, `updated_at` AS `updatedAt` FROM `users` AS `User` WHERE `User`.`id` = '5f5ae2ab-dafc-43f6-b992-3fd99f2d2265';","timestamp":"2025-07-12 16:22:23:2223"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): UPDATE `users` SET `first_name`=?,`last_name`=?,`updated_at`=? WHERE `id` = ?","timestamp":"2025-07-12 16:22:23:2223"}
{"email":"<EMAIL>","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mUser profile updated\u001b[39m","timestamp":"2025-07-12 16:22:23:2223","userId":"5f5ae2ab-dafc-43f6-b992-3fd99f2d2265"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT `id`, `user_id`, `tool_name`, `file_size_mb`, `processing_time_seconds`, `input_file_count`, `output_file_count`, `success`, `error_message`, `parameters`, `created_at` AS `createdAt`, `updated_at` AS `updatedAt` FROM `usage_records` AS `UsageRecord` WHERE `UsageRecord`.`user_id` = '5f5ae2ab-dafc-43f6-b992-3fd99f2d2265' AND `UsageRecord`.`created_at` >= '2025-07-11 23:00:00';","timestamp":"2025-07-12 16:22:23:2223"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT `id`, `user_id`, `tool_name`, `file_size_mb`, `processing_time_seconds`, `input_file_count`, `output_file_count`, `success`, `error_message`, `parameters`, `created_at` AS `createdAt`, `updated_at` AS `updatedAt` FROM `usage_records` AS `UsageRecord` WHERE `UsageRecord`.`user_id` = '5f5ae2ab-dafc-43f6-b992-3fd99f2d2265' AND `UsageRecord`.`created_at` >= '2025-07-11 23:00:00';","timestamp":"2025-07-12 16:22:23:2223"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): INSERT INTO `processing_sessions` (`id`,`user_id`,`session_token`,`status`,`files_processed`,`quota_consumed`,`can_download`,`expires_at`,`created_at`,`updated_at`) VALUES (?,?,?,?,?,?,?,?,?,?);","timestamp":"2025-07-12 16:22:23:2223"}
{"error":"\"parameters\" must be of type object","ip":"::1","isOperational":true,"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRequest error\u001b[39m","method":"POST","stack":"ValidationError: \"parameters\" must be of type object\n    at G:\\PDF Porject\\backend\\src\\middleware\\validation.ts:117:13\n    at Layer.handle [as handle_request] (G:\\PDF Porject\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (G:\\PDF Porject\\backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at validateFiles (G:\\PDF Porject\\backend\\src\\middleware\\validation.ts:151:3)\n    at Layer.handle [as handle_request] (G:\\PDF Porject\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (G:\\PDF Porject\\backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at done (G:\\PDF Porject\\backend\\node_modules\\multer\\lib\\make-middleware.js:47:7)\n    at indicateDone (G:\\PDF Porject\\backend\\node_modules\\multer\\lib\\make-middleware.js:51:68)\n    at Multipart.<anonymous> (G:\\PDF Porject\\backend\\node_modules\\multer\\lib\\make-middleware.js:168:7)\n    at Multipart.emit (node:events:518:28)","statusCode":400,"timestamp":"2025-07-12 16:22:23:2223","url":"/api/pdf-tools/process","userAgent":"axios/1.10.0"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT 1+1 AS result","timestamp":"2025-07-12 16:23:51:2351"}
{"database":"pdf","host":"127.0.0.1","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mDatabase connection established successfully\u001b[39m","port":3306,"timestamp":"2025-07-12 16:23:51:2351"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT TABLE_NAME FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_TYPE = 'BASE TABLE' AND TABLE_NAME = 'users' AND TABLE_SCHEMA = 'pdf'","timestamp":"2025-07-12 16:23:51:2351"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SHOW FULL COLUMNS FROM `users`;","timestamp":"2025-07-12 16:23:51:2351"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT CONSTRAINT_NAME as constraint_name,CONSTRAINT_NAME as constraintName,CONSTRAINT_SCHEMA as constraintSchema,CONSTRAINT_SCHEMA as constraintCatalog,TABLE_NAME as tableName,TABLE_SCHEMA as tableSchema,TABLE_SCHEMA as tableCatalog,COLUMN_NAME as columnName,REFERENCED_TABLE_SCHEMA as referencedTableSchema,REFERENCED_TABLE_SCHEMA as referencedTableCatalog,REFERENCED_TABLE_NAME as referencedTableName,REFERENCED_COLUMN_NAME as referencedColumnName FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE where TABLE_NAME = 'users' AND CONSTRAINT_NAME!='PRIMARY' AND CONSTRAINT_SCHEMA='pdf' AND REFERENCED_TABLE_NAME IS NOT NULL;","timestamp":"2025-07-12 16:23:51:2351"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `email` `email` VARCHAR(255) NOT NULL UNIQUE;","timestamp":"2025-07-12 16:23:51:2351"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `password_hash` `password_hash` VARCHAR(255) NOT NULL;","timestamp":"2025-07-12 16:23:52:2352"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `first_name` `first_name` VARCHAR(100) NOT NULL;","timestamp":"2025-07-12 16:23:52:2352"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `last_name` `last_name` VARCHAR(100) NOT NULL;","timestamp":"2025-07-12 16:23:52:2352"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `subscription_plan` `subscription_plan` ENUM('free', 'premium', 'enterprise') NOT NULL DEFAULT 'free';","timestamp":"2025-07-12 16:23:53:2353"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `is_verified` `is_verified` TINYINT(1) NOT NULL DEFAULT false;","timestamp":"2025-07-12 16:23:54:2354"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `verification_token` `verification_token` VARCHAR(255);","timestamp":"2025-07-12 16:23:54:2354"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `reset_password_token` `reset_password_token` VARCHAR(255);","timestamp":"2025-07-12 16:23:54:2354"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `reset_password_expires` `reset_password_expires` DATETIME;","timestamp":"2025-07-12 16:23:54:2354"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `last_login_at` `last_login_at` DATETIME;","timestamp":"2025-07-12 16:23:54:2354"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `created_at` `created_at` DATETIME NOT NULL;","timestamp":"2025-07-12 16:23:54:2354"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `updated_at` `updated_at` DATETIME NOT NULL;","timestamp":"2025-07-12 16:23:55:2355"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SHOW INDEX FROM `users`","timestamp":"2025-07-12 16:23:55:2355"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT TABLE_NAME FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_TYPE = 'BASE TABLE' AND TABLE_NAME = 'processing_sessions' AND TABLE_SCHEMA = 'pdf'","timestamp":"2025-07-12 16:23:55:2355"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SHOW FULL COLUMNS FROM `processing_sessions`;","timestamp":"2025-07-12 16:23:55:2355"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT CONSTRAINT_NAME as constraint_name,CONSTRAINT_NAME as constraintName,CONSTRAINT_SCHEMA as constraintSchema,CONSTRAINT_SCHEMA as constraintCatalog,TABLE_NAME as tableName,TABLE_SCHEMA as tableSchema,TABLE_SCHEMA as tableCatalog,COLUMN_NAME as columnName,REFERENCED_TABLE_SCHEMA as referencedTableSchema,REFERENCED_TABLE_SCHEMA as referencedTableCatalog,REFERENCED_TABLE_NAME as referencedTableName,REFERENCED_COLUMN_NAME as referencedColumnName FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE where TABLE_NAME = 'processing_sessions' AND CONSTRAINT_NAME!='PRIMARY' AND CONSTRAINT_SCHEMA='pdf' AND REFERENCED_TABLE_NAME IS NOT NULL;","timestamp":"2025-07-12 16:23:55:2355"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT CONSTRAINT_CATALOG AS constraintCatalog, CONSTRAINT_NAME AS constraintName, CONSTRAINT_SCHEMA AS constraintSchema, CONSTRAINT_TYPE AS constraintType, TABLE_NAME AS tableName, TABLE_SCHEMA AS tableSchema from INFORMATION_SCHEMA.TABLE_CONSTRAINTS WHERE table_name='processing_sessions' AND constraint_name = 'processing_sessions_ibfk_1' AND TABLE_SCHEMA = 'pdf';","timestamp":"2025-07-12 16:23:55:2355"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `processing_sessions` DROP FOREIGN KEY `processing_sessions_ibfk_1`;","timestamp":"2025-07-12 16:23:55:2355"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `processing_sessions` ADD FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE ON UPDATE CASCADE;","timestamp":"2025-07-12 16:23:55:2355"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `processing_sessions` CHANGE `session_token` `session_token` VARCHAR(255) NOT NULL UNIQUE;","timestamp":"2025-07-12 16:23:58:2358"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `processing_sessions` CHANGE `status` `status` ENUM('active', 'completed', 'expired') NOT NULL DEFAULT 'active';","timestamp":"2025-07-12 16:23:58:2358"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `processing_sessions` CHANGE `files_processed` `files_processed` INTEGER NOT NULL DEFAULT 0;","timestamp":"2025-07-12 16:23:58:2358"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `processing_sessions` CHANGE `quota_consumed` `quota_consumed` INTEGER NOT NULL DEFAULT 0;","timestamp":"2025-07-12 16:23:58:2358"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `processing_sessions` CHANGE `can_download` `can_download` TINYINT(1) NOT NULL DEFAULT true;","timestamp":"2025-07-12 16:23:59:2359"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `processing_sessions` CHANGE `expires_at` `expires_at` DATETIME NOT NULL;","timestamp":"2025-07-12 16:23:59:2359"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `processing_sessions` CHANGE `created_at` `created_at` DATETIME NOT NULL;","timestamp":"2025-07-12 16:23:59:2359"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `processing_sessions` CHANGE `updated_at` `updated_at` DATETIME NOT NULL;","timestamp":"2025-07-12 16:23:59:2359"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SHOW INDEX FROM `processing_sessions`","timestamp":"2025-07-12 16:23:59:2359"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT TABLE_NAME FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_TYPE = 'BASE TABLE' AND TABLE_NAME = 'usage_records' AND TABLE_SCHEMA = 'pdf'","timestamp":"2025-07-12 16:23:59:2359"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SHOW FULL COLUMNS FROM `usage_records`;","timestamp":"2025-07-12 16:23:59:2359"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT CONSTRAINT_NAME as constraint_name,CONSTRAINT_NAME as constraintName,CONSTRAINT_SCHEMA as constraintSchema,CONSTRAINT_SCHEMA as constraintCatalog,TABLE_NAME as tableName,TABLE_SCHEMA as tableSchema,TABLE_SCHEMA as tableCatalog,COLUMN_NAME as columnName,REFERENCED_TABLE_SCHEMA as referencedTableSchema,REFERENCED_TABLE_SCHEMA as referencedTableCatalog,REFERENCED_TABLE_NAME as referencedTableName,REFERENCED_COLUMN_NAME as referencedColumnName FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE where TABLE_NAME = 'usage_records' AND CONSTRAINT_NAME!='PRIMARY' AND CONSTRAINT_SCHEMA='pdf' AND REFERENCED_TABLE_NAME IS NOT NULL;","timestamp":"2025-07-12 16:23:59:2359"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT CONSTRAINT_CATALOG AS constraintCatalog, CONSTRAINT_NAME AS constraintName, CONSTRAINT_SCHEMA AS constraintSchema, CONSTRAINT_TYPE AS constraintType, TABLE_NAME AS tableName, TABLE_SCHEMA AS tableSchema from INFORMATION_SCHEMA.TABLE_CONSTRAINTS WHERE table_name='usage_records' AND constraint_name = 'usage_records_ibfk_1' AND TABLE_SCHEMA = 'pdf';","timestamp":"2025-07-12 16:23:59:2359"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `usage_records` DROP FOREIGN KEY `usage_records_ibfk_1`;","timestamp":"2025-07-12 16:23:59:2359"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `usage_records` ADD FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE ON UPDATE CASCADE;","timestamp":"2025-07-12 16:23:59:2359"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `usage_records` CHANGE `tool_name` `tool_name` VARCHAR(100) NOT NULL;","timestamp":"2025-07-12 16:24:02:242"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `usage_records` CHANGE `file_size_mb` `file_size_mb` DECIMAL(10,2) NOT NULL;","timestamp":"2025-07-12 16:24:02:242"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `usage_records` CHANGE `processing_time_seconds` `processing_time_seconds` INTEGER NOT NULL;","timestamp":"2025-07-12 16:24:02:242"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `usage_records` CHANGE `input_file_count` `input_file_count` INTEGER NOT NULL;","timestamp":"2025-07-12 16:24:02:242"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `usage_records` CHANGE `output_file_count` `output_file_count` INTEGER NOT NULL DEFAULT 0;","timestamp":"2025-07-12 16:24:02:242"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `usage_records` CHANGE `success` `success` TINYINT(1) NOT NULL DEFAULT true;","timestamp":"2025-07-12 16:24:03:243"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `usage_records` CHANGE `error_message` `error_message` TEXT;","timestamp":"2025-07-12 16:24:03:243"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `usage_records` CHANGE `parameters` `parameters` JSON;","timestamp":"2025-07-12 16:24:03:243"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `usage_records` CHANGE `created_at` `created_at` DATETIME NOT NULL;","timestamp":"2025-07-12 16:24:03:243"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `usage_records` CHANGE `updated_at` `updated_at` DATETIME NOT NULL;","timestamp":"2025-07-12 16:24:03:243"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SHOW INDEX FROM `usage_records`","timestamp":"2025-07-12 16:24:03:243"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mDatabase initialized successfully\u001b[39m","timestamp":"2025-07-12 16:24:03:243"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mServer running on port 3001\u001b[39m","timestamp":"2025-07-12 16:24:03:243"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mEnvironment: development\u001b[39m","timestamp":"2025-07-12 16:24:03:243"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mDatabase connected and synchronized\u001b[39m","timestamp":"2025-07-12 16:24:03:243"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT `id`, `email`, `password_hash`, `first_name`, `last_name`, `subscription_plan`, `is_verified`, `verification_token`, `reset_password_token`, `reset_password_expires`, `last_login_at`, `created_at` AS `createdAt`, `updated_at` AS `updatedAt` FROM `users` AS `User` WHERE `User`.`email` = '<EMAIL>';","timestamp":"2025-07-12 16:25:54:2554"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): INSERT INTO `users` (`id`,`email`,`password_hash`,`first_name`,`last_name`,`subscription_plan`,`is_verified`,`created_at`,`updated_at`) VALUES (?,?,?,?,?,?,?,?,?);","timestamp":"2025-07-12 16:25:54:2554"}
{"email":"<EMAIL>","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mUser registered successfully\u001b[39m","timestamp":"2025-07-12 16:25:54:2554","userId":"e8282b24-fd9b-414a-b003-054f971c9eac"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT `id`, `email`, `password_hash`, `first_name`, `last_name`, `subscription_plan`, `is_verified`, `verification_token`, `reset_password_token`, `reset_password_expires`, `last_login_at`, `created_at` AS `createdAt`, `updated_at` AS `updatedAt` FROM `users` AS `User` WHERE `User`.`email` = '<EMAIL>';","timestamp":"2025-07-12 16:25:54:2554"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): UPDATE `users` SET `last_login_at`=?,`updated_at`=? WHERE `id` = ?","timestamp":"2025-07-12 16:25:55:2555"}
{"email":"<EMAIL>","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mUser logged in successfully\u001b[39m","timestamp":"2025-07-12 16:25:55:2555","userId":"e8282b24-fd9b-414a-b003-054f971c9eac"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT `id`, `email`, `password_hash`, `first_name`, `last_name`, `subscription_plan`, `is_verified`, `verification_token`, `reset_password_token`, `reset_password_expires`, `last_login_at`, `created_at` AS `createdAt`, `updated_at` AS `updatedAt` FROM `users` AS `User` WHERE `User`.`id` = 'e8282b24-fd9b-414a-b003-054f971c9eac';","timestamp":"2025-07-12 16:25:55:2555"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT `id`, `email`, `password_hash`, `first_name`, `last_name`, `subscription_plan`, `is_verified`, `verification_token`, `reset_password_token`, `reset_password_expires`, `last_login_at`, `created_at` AS `createdAt`, `updated_at` AS `updatedAt` FROM `users` AS `User` WHERE `User`.`id` = 'e8282b24-fd9b-414a-b003-054f971c9eac';","timestamp":"2025-07-12 16:25:55:2555"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): UPDATE `users` SET `first_name`=?,`last_name`=?,`updated_at`=? WHERE `id` = ?","timestamp":"2025-07-12 16:25:55:2555"}
{"email":"<EMAIL>","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mUser profile updated\u001b[39m","timestamp":"2025-07-12 16:25:55:2555","userId":"e8282b24-fd9b-414a-b003-054f971c9eac"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT `id`, `user_id`, `tool_name`, `file_size_mb`, `processing_time_seconds`, `input_file_count`, `output_file_count`, `success`, `error_message`, `parameters`, `created_at` AS `createdAt`, `updated_at` AS `updatedAt` FROM `usage_records` AS `UsageRecord` WHERE `UsageRecord`.`user_id` = 'e8282b24-fd9b-414a-b003-054f971c9eac' AND `UsageRecord`.`created_at` >= '2025-07-11 23:00:00';","timestamp":"2025-07-12 16:25:55:2555"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT `id`, `user_id`, `tool_name`, `file_size_mb`, `processing_time_seconds`, `input_file_count`, `output_file_count`, `success`, `error_message`, `parameters`, `created_at` AS `createdAt`, `updated_at` AS `updatedAt` FROM `usage_records` AS `UsageRecord` WHERE `UsageRecord`.`user_id` = 'e8282b24-fd9b-414a-b003-054f971c9eac' AND `UsageRecord`.`created_at` >= '2025-07-11 23:00:00';","timestamp":"2025-07-12 16:25:55:2555"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): INSERT INTO `processing_sessions` (`id`,`user_id`,`session_token`,`status`,`files_processed`,`quota_consumed`,`can_download`,`expires_at`,`created_at`,`updated_at`) VALUES (?,?,?,?,?,?,?,?,?,?);","timestamp":"2025-07-12 16:25:55:2555"}
{"error":"\"parameters\" must be of type object","ip":"::1","isOperational":true,"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRequest error\u001b[39m","method":"POST","stack":"ValidationError: \"parameters\" must be of type object\n    at G:\\PDF Porject\\backend\\src\\middleware\\validation.ts:117:13\n    at Layer.handle [as handle_request] (G:\\PDF Porject\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (G:\\PDF Porject\\backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at validateFiles (G:\\PDF Porject\\backend\\src\\middleware\\validation.ts:151:3)\n    at Layer.handle [as handle_request] (G:\\PDF Porject\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (G:\\PDF Porject\\backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at done (G:\\PDF Porject\\backend\\node_modules\\multer\\lib\\make-middleware.js:47:7)\n    at indicateDone (G:\\PDF Porject\\backend\\node_modules\\multer\\lib\\make-middleware.js:51:68)\n    at Multipart.<anonymous> (G:\\PDF Porject\\backend\\node_modules\\multer\\lib\\make-middleware.js:168:7)\n    at Multipart.emit (node:events:518:28)","statusCode":400,"timestamp":"2025-07-12 16:25:55:2555","url":"/api/pdf-tools/process","userAgent":"axios/1.10.0"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT 1+1 AS result","timestamp":"2025-07-12 16:26:33:2633"}
{"database":"pdf","host":"127.0.0.1","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mDatabase connection established successfully\u001b[39m","port":3306,"timestamp":"2025-07-12 16:26:33:2633"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT TABLE_NAME FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_TYPE = 'BASE TABLE' AND TABLE_NAME = 'users' AND TABLE_SCHEMA = 'pdf'","timestamp":"2025-07-12 16:26:33:2633"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SHOW FULL COLUMNS FROM `users`;","timestamp":"2025-07-12 16:26:33:2633"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT CONSTRAINT_NAME as constraint_name,CONSTRAINT_NAME as constraintName,CONSTRAINT_SCHEMA as constraintSchema,CONSTRAINT_SCHEMA as constraintCatalog,TABLE_NAME as tableName,TABLE_SCHEMA as tableSchema,TABLE_SCHEMA as tableCatalog,COLUMN_NAME as columnName,REFERENCED_TABLE_SCHEMA as referencedTableSchema,REFERENCED_TABLE_SCHEMA as referencedTableCatalog,REFERENCED_TABLE_NAME as referencedTableName,REFERENCED_COLUMN_NAME as referencedColumnName FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE where TABLE_NAME = 'users' AND CONSTRAINT_NAME!='PRIMARY' AND CONSTRAINT_SCHEMA='pdf' AND REFERENCED_TABLE_NAME IS NOT NULL;","timestamp":"2025-07-12 16:26:33:2633"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `email` `email` VARCHAR(255) NOT NULL UNIQUE;","timestamp":"2025-07-12 16:26:33:2633"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `password_hash` `password_hash` VARCHAR(255) NOT NULL;","timestamp":"2025-07-12 16:26:33:2633"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `first_name` `first_name` VARCHAR(100) NOT NULL;","timestamp":"2025-07-12 16:26:33:2633"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `last_name` `last_name` VARCHAR(100) NOT NULL;","timestamp":"2025-07-12 16:26:34:2634"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `subscription_plan` `subscription_plan` ENUM('free', 'premium', 'enterprise') NOT NULL DEFAULT 'free';","timestamp":"2025-07-12 16:26:34:2634"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `is_verified` `is_verified` TINYINT(1) NOT NULL DEFAULT false;","timestamp":"2025-07-12 16:26:34:2634"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `verification_token` `verification_token` VARCHAR(255);","timestamp":"2025-07-12 16:26:36:2636"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `reset_password_token` `reset_password_token` VARCHAR(255);","timestamp":"2025-07-12 16:26:36:2636"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `reset_password_expires` `reset_password_expires` DATETIME;","timestamp":"2025-07-12 16:26:36:2636"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `last_login_at` `last_login_at` DATETIME;","timestamp":"2025-07-12 16:26:36:2636"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `created_at` `created_at` DATETIME NOT NULL;","timestamp":"2025-07-12 16:26:36:2636"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `updated_at` `updated_at` DATETIME NOT NULL;","timestamp":"2025-07-12 16:26:36:2636"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SHOW INDEX FROM `users`","timestamp":"2025-07-12 16:26:37:2637"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT TABLE_NAME FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_TYPE = 'BASE TABLE' AND TABLE_NAME = 'processing_sessions' AND TABLE_SCHEMA = 'pdf'","timestamp":"2025-07-12 16:26:37:2637"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SHOW FULL COLUMNS FROM `processing_sessions`;","timestamp":"2025-07-12 16:26:37:2637"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT CONSTRAINT_NAME as constraint_name,CONSTRAINT_NAME as constraintName,CONSTRAINT_SCHEMA as constraintSchema,CONSTRAINT_SCHEMA as constraintCatalog,TABLE_NAME as tableName,TABLE_SCHEMA as tableSchema,TABLE_SCHEMA as tableCatalog,COLUMN_NAME as columnName,REFERENCED_TABLE_SCHEMA as referencedTableSchema,REFERENCED_TABLE_SCHEMA as referencedTableCatalog,REFERENCED_TABLE_NAME as referencedTableName,REFERENCED_COLUMN_NAME as referencedColumnName FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE where TABLE_NAME = 'processing_sessions' AND CONSTRAINT_NAME!='PRIMARY' AND CONSTRAINT_SCHEMA='pdf' AND REFERENCED_TABLE_NAME IS NOT NULL;","timestamp":"2025-07-12 16:26:37:2637"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT CONSTRAINT_CATALOG AS constraintCatalog, CONSTRAINT_NAME AS constraintName, CONSTRAINT_SCHEMA AS constraintSchema, CONSTRAINT_TYPE AS constraintType, TABLE_NAME AS tableName, TABLE_SCHEMA AS tableSchema from INFORMATION_SCHEMA.TABLE_CONSTRAINTS WHERE table_name='processing_sessions' AND constraint_name = 'processing_sessions_ibfk_1' AND TABLE_SCHEMA = 'pdf';","timestamp":"2025-07-12 16:26:37:2637"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `processing_sessions` DROP FOREIGN KEY `processing_sessions_ibfk_1`;","timestamp":"2025-07-12 16:26:37:2637"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `processing_sessions` ADD FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE ON UPDATE CASCADE;","timestamp":"2025-07-12 16:26:37:2637"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `processing_sessions` CHANGE `session_token` `session_token` VARCHAR(255) NOT NULL UNIQUE;","timestamp":"2025-07-12 16:26:39:2639"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `processing_sessions` CHANGE `status` `status` ENUM('active', 'completed', 'expired') NOT NULL DEFAULT 'active';","timestamp":"2025-07-12 16:26:40:2640"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `processing_sessions` CHANGE `files_processed` `files_processed` INTEGER NOT NULL DEFAULT 0;","timestamp":"2025-07-12 16:26:40:2640"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `processing_sessions` CHANGE `quota_consumed` `quota_consumed` INTEGER NOT NULL DEFAULT 0;","timestamp":"2025-07-12 16:26:40:2640"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `processing_sessions` CHANGE `can_download` `can_download` TINYINT(1) NOT NULL DEFAULT true;","timestamp":"2025-07-12 16:26:40:2640"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `processing_sessions` CHANGE `expires_at` `expires_at` DATETIME NOT NULL;","timestamp":"2025-07-12 16:26:41:2641"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `processing_sessions` CHANGE `created_at` `created_at` DATETIME NOT NULL;","timestamp":"2025-07-12 16:26:41:2641"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `processing_sessions` CHANGE `updated_at` `updated_at` DATETIME NOT NULL;","timestamp":"2025-07-12 16:26:41:2641"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SHOW INDEX FROM `processing_sessions`","timestamp":"2025-07-12 16:26:41:2641"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT TABLE_NAME FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_TYPE = 'BASE TABLE' AND TABLE_NAME = 'usage_records' AND TABLE_SCHEMA = 'pdf'","timestamp":"2025-07-12 16:26:41:2641"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SHOW FULL COLUMNS FROM `usage_records`;","timestamp":"2025-07-12 16:26:41:2641"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT CONSTRAINT_NAME as constraint_name,CONSTRAINT_NAME as constraintName,CONSTRAINT_SCHEMA as constraintSchema,CONSTRAINT_SCHEMA as constraintCatalog,TABLE_NAME as tableName,TABLE_SCHEMA as tableSchema,TABLE_SCHEMA as tableCatalog,COLUMN_NAME as columnName,REFERENCED_TABLE_SCHEMA as referencedTableSchema,REFERENCED_TABLE_SCHEMA as referencedTableCatalog,REFERENCED_TABLE_NAME as referencedTableName,REFERENCED_COLUMN_NAME as referencedColumnName FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE where TABLE_NAME = 'usage_records' AND CONSTRAINT_NAME!='PRIMARY' AND CONSTRAINT_SCHEMA='pdf' AND REFERENCED_TABLE_NAME IS NOT NULL;","timestamp":"2025-07-12 16:26:41:2641"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT CONSTRAINT_CATALOG AS constraintCatalog, CONSTRAINT_NAME AS constraintName, CONSTRAINT_SCHEMA AS constraintSchema, CONSTRAINT_TYPE AS constraintType, TABLE_NAME AS tableName, TABLE_SCHEMA AS tableSchema from INFORMATION_SCHEMA.TABLE_CONSTRAINTS WHERE table_name='usage_records' AND constraint_name = 'usage_records_ibfk_1' AND TABLE_SCHEMA = 'pdf';","timestamp":"2025-07-12 16:26:41:2641"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `usage_records` DROP FOREIGN KEY `usage_records_ibfk_1`;","timestamp":"2025-07-12 16:26:41:2641"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `usage_records` ADD FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE ON UPDATE CASCADE;","timestamp":"2025-07-12 16:26:41:2641"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `usage_records` CHANGE `tool_name` `tool_name` VARCHAR(100) NOT NULL;","timestamp":"2025-07-12 16:26:43:2643"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `usage_records` CHANGE `file_size_mb` `file_size_mb` DECIMAL(10,2) NOT NULL;","timestamp":"2025-07-12 16:26:43:2643"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `usage_records` CHANGE `processing_time_seconds` `processing_time_seconds` INTEGER NOT NULL;","timestamp":"2025-07-12 16:26:43:2643"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `usage_records` CHANGE `input_file_count` `input_file_count` INTEGER NOT NULL;","timestamp":"2025-07-12 16:26:43:2643"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `usage_records` CHANGE `output_file_count` `output_file_count` INTEGER NOT NULL DEFAULT 0;","timestamp":"2025-07-12 16:26:43:2643"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `usage_records` CHANGE `success` `success` TINYINT(1) NOT NULL DEFAULT true;","timestamp":"2025-07-12 16:26:44:2644"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `usage_records` CHANGE `error_message` `error_message` TEXT;","timestamp":"2025-07-12 16:26:44:2644"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `usage_records` CHANGE `parameters` `parameters` JSON;","timestamp":"2025-07-12 16:26:44:2644"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `usage_records` CHANGE `created_at` `created_at` DATETIME NOT NULL;","timestamp":"2025-07-12 16:26:44:2644"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `usage_records` CHANGE `updated_at` `updated_at` DATETIME NOT NULL;","timestamp":"2025-07-12 16:26:44:2644"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SHOW INDEX FROM `usage_records`","timestamp":"2025-07-12 16:26:44:2644"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mDatabase initialized successfully\u001b[39m","timestamp":"2025-07-12 16:26:44:2644"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mServer running on port 3001\u001b[39m","timestamp":"2025-07-12 16:26:44:2644"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mEnvironment: development\u001b[39m","timestamp":"2025-07-12 16:26:44:2644"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mDatabase connected and synchronized\u001b[39m","timestamp":"2025-07-12 16:26:44:2644"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT `id`, `email`, `password_hash`, `first_name`, `last_name`, `subscription_plan`, `is_verified`, `verification_token`, `reset_password_token`, `reset_password_expires`, `last_login_at`, `created_at` AS `createdAt`, `updated_at` AS `updatedAt` FROM `users` AS `User` WHERE `User`.`email` = '<EMAIL>';","timestamp":"2025-07-12 16:36:22:3622"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): INSERT INTO `users` (`id`,`email`,`password_hash`,`first_name`,`last_name`,`subscription_plan`,`is_verified`,`created_at`,`updated_at`) VALUES (?,?,?,?,?,?,?,?,?);","timestamp":"2025-07-12 16:36:22:3622"}
{"email":"<EMAIL>","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mUser registered successfully\u001b[39m","timestamp":"2025-07-12 16:36:22:3622","userId":"89d3782a-cebc-4b7b-8700-a9785543ea45"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT `id`, `email`, `password_hash`, `first_name`, `last_name`, `subscription_plan`, `is_verified`, `verification_token`, `reset_password_token`, `reset_password_expires`, `last_login_at`, `created_at` AS `createdAt`, `updated_at` AS `updatedAt` FROM `users` AS `User` WHERE `User`.`email` = '<EMAIL>';","timestamp":"2025-07-12 16:36:22:3622"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): UPDATE `users` SET `last_login_at`=?,`updated_at`=? WHERE `id` = ?","timestamp":"2025-07-12 16:36:22:3622"}
{"email":"<EMAIL>","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mUser logged in successfully\u001b[39m","timestamp":"2025-07-12 16:36:22:3622","userId":"89d3782a-cebc-4b7b-8700-a9785543ea45"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT `id`, `email`, `password_hash`, `first_name`, `last_name`, `subscription_plan`, `is_verified`, `verification_token`, `reset_password_token`, `reset_password_expires`, `last_login_at`, `created_at` AS `createdAt`, `updated_at` AS `updatedAt` FROM `users` AS `User` WHERE `User`.`id` = '89d3782a-cebc-4b7b-8700-a9785543ea45';","timestamp":"2025-07-12 16:36:22:3622"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT `id`, `email`, `password_hash`, `first_name`, `last_name`, `subscription_plan`, `is_verified`, `verification_token`, `reset_password_token`, `reset_password_expires`, `last_login_at`, `created_at` AS `createdAt`, `updated_at` AS `updatedAt` FROM `users` AS `User` WHERE `User`.`id` = '89d3782a-cebc-4b7b-8700-a9785543ea45';","timestamp":"2025-07-12 16:36:22:3622"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): UPDATE `users` SET `first_name`=?,`last_name`=?,`updated_at`=? WHERE `id` = ?","timestamp":"2025-07-12 16:36:22:3622"}
{"email":"<EMAIL>","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mUser profile updated\u001b[39m","timestamp":"2025-07-12 16:36:22:3622","userId":"89d3782a-cebc-4b7b-8700-a9785543ea45"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT `id`, `user_id`, `tool_name`, `file_size_mb`, `processing_time_seconds`, `input_file_count`, `output_file_count`, `success`, `error_message`, `parameters`, `created_at` AS `createdAt`, `updated_at` AS `updatedAt` FROM `usage_records` AS `UsageRecord` WHERE `UsageRecord`.`user_id` = '89d3782a-cebc-4b7b-8700-a9785543ea45' AND `UsageRecord`.`created_at` >= '2025-07-11 23:00:00';","timestamp":"2025-07-12 16:36:22:3622"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT `id`, `user_id`, `tool_name`, `file_size_mb`, `processing_time_seconds`, `input_file_count`, `output_file_count`, `success`, `error_message`, `parameters`, `created_at` AS `createdAt`, `updated_at` AS `updatedAt` FROM `usage_records` AS `UsageRecord` WHERE `UsageRecord`.`user_id` = '89d3782a-cebc-4b7b-8700-a9785543ea45' AND `UsageRecord`.`created_at` >= '2025-07-11 23:00:00';","timestamp":"2025-07-12 16:36:22:3622"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): INSERT INTO `processing_sessions` (`id`,`user_id`,`session_token`,`status`,`files_processed`,`quota_consumed`,`can_download`,`expires_at`,`created_at`,`updated_at`) VALUES (?,?,?,?,?,?,?,?,?,?);","timestamp":"2025-07-12 16:36:22:3622"}
{"error":"\"parameters\" must be of type object","ip":"::1","isOperational":true,"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRequest error\u001b[39m","method":"POST","stack":"ValidationError: \"parameters\" must be of type object\n    at G:\\PDF Porject\\backend\\src\\middleware\\validation.ts:117:13\n    at Layer.handle [as handle_request] (G:\\PDF Porject\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (G:\\PDF Porject\\backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at validateFiles (G:\\PDF Porject\\backend\\src\\middleware\\validation.ts:151:3)\n    at Layer.handle [as handle_request] (G:\\PDF Porject\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (G:\\PDF Porject\\backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at done (G:\\PDF Porject\\backend\\node_modules\\multer\\lib\\make-middleware.js:47:7)\n    at indicateDone (G:\\PDF Porject\\backend\\node_modules\\multer\\lib\\make-middleware.js:51:68)\n    at Multipart.<anonymous> (G:\\PDF Porject\\backend\\node_modules\\multer\\lib\\make-middleware.js:168:7)\n    at Multipart.emit (node:events:518:28)","statusCode":400,"timestamp":"2025-07-12 16:36:23:3623","url":"/api/pdf-tools/process","userAgent":"axios/1.10.0"}
{"error":"\"password\" length must be at least 8 characters long, Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character","ip":"::1","isOperational":true,"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRequest error\u001b[39m","method":"POST","stack":"ValidationError: \"password\" length must be at least 8 characters long, Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character\n    at G:\\PDF Porject\\backend\\src\\middleware\\validation.ts:117:13\n    at Layer.handle [as handle_request] (G:\\PDF Porject\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (G:\\PDF Porject\\backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (G:\\PDF Porject\\backend\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (G:\\PDF Porject\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at G:\\PDF Porject\\backend\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (G:\\PDF Porject\\backend\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (G:\\PDF Porject\\backend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (G:\\PDF Porject\\backend\\node_modules\\express\\lib\\router\\index.js:175:3)\n    at router (G:\\PDF Porject\\backend\\node_modules\\express\\lib\\router\\index.js:47:12)","statusCode":400,"timestamp":"2025-07-12 16:37:21:3721","url":"/api/auth/register","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"error":"Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character","ip":"::1","isOperational":true,"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRequest error\u001b[39m","method":"POST","stack":"ValidationError: Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character\n    at G:\\PDF Porject\\backend\\src\\middleware\\validation.ts:117:13\n    at Layer.handle [as handle_request] (G:\\PDF Porject\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (G:\\PDF Porject\\backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (G:\\PDF Porject\\backend\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (G:\\PDF Porject\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at G:\\PDF Porject\\backend\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (G:\\PDF Porject\\backend\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (G:\\PDF Porject\\backend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (G:\\PDF Porject\\backend\\node_modules\\express\\lib\\router\\index.js:175:3)\n    at router (G:\\PDF Porject\\backend\\node_modules\\express\\lib\\router\\index.js:47:12)","statusCode":400,"timestamp":"2025-07-12 16:37:34:3734","url":"/api/auth/register","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"error":"Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character","ip":"::1","isOperational":true,"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRequest error\u001b[39m","method":"POST","stack":"ValidationError: Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character\n    at G:\\PDF Porject\\backend\\src\\middleware\\validation.ts:117:13\n    at Layer.handle [as handle_request] (G:\\PDF Porject\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (G:\\PDF Porject\\backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (G:\\PDF Porject\\backend\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (G:\\PDF Porject\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at G:\\PDF Porject\\backend\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (G:\\PDF Porject\\backend\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (G:\\PDF Porject\\backend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (G:\\PDF Porject\\backend\\node_modules\\express\\lib\\router\\index.js:175:3)\n    at router (G:\\PDF Porject\\backend\\node_modules\\express\\lib\\router\\index.js:47:12)","statusCode":400,"timestamp":"2025-07-12 16:37:48:3748","url":"/api/auth/register","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"error":"Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character","ip":"::1","isOperational":true,"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRequest error\u001b[39m","method":"POST","stack":"ValidationError: Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character\n    at G:\\PDF Porject\\backend\\src\\middleware\\validation.ts:117:13\n    at Layer.handle [as handle_request] (G:\\PDF Porject\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (G:\\PDF Porject\\backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (G:\\PDF Porject\\backend\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (G:\\PDF Porject\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at G:\\PDF Porject\\backend\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (G:\\PDF Porject\\backend\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (G:\\PDF Porject\\backend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (G:\\PDF Porject\\backend\\node_modules\\express\\lib\\router\\index.js:175:3)\n    at router (G:\\PDF Porject\\backend\\node_modules\\express\\lib\\router\\index.js:47:12)","statusCode":400,"timestamp":"2025-07-12 16:37:58:3758","url":"/api/auth/register","userAgent":"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT `id`, `email`, `password_hash`, `first_name`, `last_name`, `subscription_plan`, `is_verified`, `verification_token`, `reset_password_token`, `reset_password_expires`, `last_login_at`, `created_at` AS `createdAt`, `updated_at` AS `updatedAt` FROM `users` AS `User` WHERE `User`.`email` = '<EMAIL>';","timestamp":"2025-07-12 16:38:05:385"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): INSERT INTO `users` (`id`,`email`,`password_hash`,`first_name`,`last_name`,`subscription_plan`,`is_verified`,`created_at`,`updated_at`) VALUES (?,?,?,?,?,?,?,?,?);","timestamp":"2025-07-12 16:38:05:385"}
{"email":"<EMAIL>","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mUser registered successfully\u001b[39m","timestamp":"2025-07-12 16:38:05:385","userId":"60ef0f29-ca38-4a6a-86cb-256f9362aaa3"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT `id`, `user_id`, `tool_name`, `file_size_mb`, `processing_time_seconds`, `input_file_count`, `output_file_count`, `success`, `error_message`, `parameters`, `created_at` AS `createdAt`, `updated_at` AS `updatedAt` FROM `usage_records` AS `UsageRecord` WHERE `UsageRecord`.`user_id` = '60ef0f29-ca38-4a6a-86cb-256f9362aaa3' AND `UsageRecord`.`created_at` >= '2025-07-11 23:00:00';","timestamp":"2025-07-12 16:38:05:385"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT count(*) AS `count` FROM `usage_records` AS `UsageRecord` WHERE `UsageRecord`.`user_id` = '60ef0f29-ca38-4a6a-86cb-256f9362aaa3' AND `UsageRecord`.`created_at` >= '2025-07-05 15:38:05';","timestamp":"2025-07-12 16:38:05:385"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT `id`, `user_id`, `tool_name`, `file_size_mb`, `processing_time_seconds`, `input_file_count`, `output_file_count`, `success`, `error_message`, `parameters`, `created_at` AS `createdAt`, `updated_at` AS `updatedAt` FROM `usage_records` AS `UsageRecord` WHERE `UsageRecord`.`user_id` = '60ef0f29-ca38-4a6a-86cb-256f9362aaa3' AND `UsageRecord`.`created_at` >= '2025-07-05 15:38:05' ORDER BY `createdAt` DESC LIMIT 0, 50;","timestamp":"2025-07-12 16:38:05:385"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT `id`, `email`, `password_hash`, `first_name`, `last_name`, `subscription_plan`, `is_verified`, `verification_token`, `reset_password_token`, `reset_password_expires`, `last_login_at`, `created_at` AS `createdAt`, `updated_at` AS `updatedAt` FROM `users` AS `User` WHERE `User`.`id` = '60ef0f29-ca38-4a6a-86cb-256f9362aaa3';","timestamp":"2025-07-12 16:38:52:3852"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): UPDATE `users` SET `first_name`=?,`updated_at`=? WHERE `id` = ?","timestamp":"2025-07-12 16:38:52:3852"}
{"email":"<EMAIL>","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mUser profile updated\u001b[39m","timestamp":"2025-07-12 16:38:52:3852","userId":"60ef0f29-ca38-4a6a-86cb-256f9362aaa3"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT `id`, `user_id`, `tool_name`, `file_size_mb`, `processing_time_seconds`, `input_file_count`, `output_file_count`, `success`, `error_message`, `parameters`, `created_at` AS `createdAt`, `updated_at` AS `updatedAt` FROM `usage_records` AS `UsageRecord` WHERE `UsageRecord`.`user_id` = '60ef0f29-ca38-4a6a-86cb-256f9362aaa3' AND `UsageRecord`.`created_at` >= '2025-07-11 23:00:00';","timestamp":"2025-07-12 16:38:52:3852"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT count(*) AS `count` FROM `usage_records` AS `UsageRecord` WHERE `UsageRecord`.`user_id` = '60ef0f29-ca38-4a6a-86cb-256f9362aaa3' AND `UsageRecord`.`created_at` >= '2025-07-05 15:38:52';","timestamp":"2025-07-12 16:38:52:3852"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT `id`, `user_id`, `tool_name`, `file_size_mb`, `processing_time_seconds`, `input_file_count`, `output_file_count`, `success`, `error_message`, `parameters`, `created_at` AS `createdAt`, `updated_at` AS `updatedAt` FROM `usage_records` AS `UsageRecord` WHERE `UsageRecord`.`user_id` = '60ef0f29-ca38-4a6a-86cb-256f9362aaa3' AND `UsageRecord`.`created_at` >= '2025-07-05 15:38:52' ORDER BY `createdAt` DESC LIMIT 0, 50;","timestamp":"2025-07-12 16:38:52:3852"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT `id`, `email`, `password_hash`, `first_name`, `last_name`, `subscription_plan`, `is_verified`, `verification_token`, `reset_password_token`, `reset_password_expires`, `last_login_at`, `created_at` AS `createdAt`, `updated_at` AS `updatedAt` FROM `users` AS `User` WHERE `User`.`id` = '60ef0f29-ca38-4a6a-86cb-256f9362aaa3';","timestamp":"2025-07-12 16:38:55:3855"}
{"email":"<EMAIL>","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mUser profile updated\u001b[39m","timestamp":"2025-07-12 16:38:55:3855","userId":"60ef0f29-ca38-4a6a-86cb-256f9362aaa3"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT `id`, `user_id`, `tool_name`, `file_size_mb`, `processing_time_seconds`, `input_file_count`, `output_file_count`, `success`, `error_message`, `parameters`, `created_at` AS `createdAt`, `updated_at` AS `updatedAt` FROM `usage_records` AS `UsageRecord` WHERE `UsageRecord`.`user_id` = '60ef0f29-ca38-4a6a-86cb-256f9362aaa3' AND `UsageRecord`.`created_at` >= '2025-07-11 23:00:00';","timestamp":"2025-07-12 16:38:55:3855"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT count(*) AS `count` FROM `usage_records` AS `UsageRecord` WHERE `UsageRecord`.`user_id` = '60ef0f29-ca38-4a6a-86cb-256f9362aaa3' AND `UsageRecord`.`created_at` >= '2025-07-05 15:38:55';","timestamp":"2025-07-12 16:38:55:3855"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT `id`, `user_id`, `tool_name`, `file_size_mb`, `processing_time_seconds`, `input_file_count`, `output_file_count`, `success`, `error_message`, `parameters`, `created_at` AS `createdAt`, `updated_at` AS `updatedAt` FROM `usage_records` AS `UsageRecord` WHERE `UsageRecord`.`user_id` = '60ef0f29-ca38-4a6a-86cb-256f9362aaa3' AND `UsageRecord`.`created_at` >= '2025-07-05 15:38:55' ORDER BY `createdAt` DESC LIMIT 0, 50;","timestamp":"2025-07-12 16:38:55:3855"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT `id`, `email`, `password_hash`, `first_name`, `last_name`, `subscription_plan`, `is_verified`, `verification_token`, `reset_password_token`, `reset_password_expires`, `last_login_at`, `created_at` AS `createdAt`, `updated_at` AS `updatedAt` FROM `users` AS `User` WHERE `User`.`id` = '60ef0f29-ca38-4a6a-86cb-256f9362aaa3';","timestamp":"2025-07-12 16:38:59:3859"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): UPDATE `users` SET `last_name`=?,`updated_at`=? WHERE `id` = ?","timestamp":"2025-07-12 16:38:59:3859"}
{"email":"<EMAIL>","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mUser profile updated\u001b[39m","timestamp":"2025-07-12 16:38:59:3859","userId":"60ef0f29-ca38-4a6a-86cb-256f9362aaa3"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT `id`, `user_id`, `tool_name`, `file_size_mb`, `processing_time_seconds`, `input_file_count`, `output_file_count`, `success`, `error_message`, `parameters`, `created_at` AS `createdAt`, `updated_at` AS `updatedAt` FROM `usage_records` AS `UsageRecord` WHERE `UsageRecord`.`user_id` = '60ef0f29-ca38-4a6a-86cb-256f9362aaa3' AND `UsageRecord`.`created_at` >= '2025-07-11 23:00:00';","timestamp":"2025-07-12 16:38:59:3859"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT count(*) AS `count` FROM `usage_records` AS `UsageRecord` WHERE `UsageRecord`.`user_id` = '60ef0f29-ca38-4a6a-86cb-256f9362aaa3' AND `UsageRecord`.`created_at` >= '2025-07-05 15:38:59';","timestamp":"2025-07-12 16:38:59:3859"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT `id`, `user_id`, `tool_name`, `file_size_mb`, `processing_time_seconds`, `input_file_count`, `output_file_count`, `success`, `error_message`, `parameters`, `created_at` AS `createdAt`, `updated_at` AS `updatedAt` FROM `usage_records` AS `UsageRecord` WHERE `UsageRecord`.`user_id` = '60ef0f29-ca38-4a6a-86cb-256f9362aaa3' AND `UsageRecord`.`created_at` >= '2025-07-05 15:38:59' ORDER BY `createdAt` DESC LIMIT 0, 50;","timestamp":"2025-07-12 16:38:59:3859"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT `id`, `email`, `password_hash`, `first_name`, `last_name`, `subscription_plan`, `is_verified`, `verification_token`, `reset_password_token`, `reset_password_expires`, `last_login_at`, `created_at` AS `createdAt`, `updated_at` AS `updatedAt` FROM `users` AS `User` WHERE `User`.`id` = '60ef0f29-ca38-4a6a-86cb-256f9362aaa3';","timestamp":"2025-07-12 16:39:02:392"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT `id`, `email`, `password_hash`, `first_name`, `last_name`, `subscription_plan`, `is_verified`, `verification_token`, `reset_password_token`, `reset_password_expires`, `last_login_at`, `created_at` AS `createdAt`, `updated_at` AS `updatedAt` FROM `users` AS `User` WHERE `User`.`id` = '60ef0f29-ca38-4a6a-86cb-256f9362aaa3';","timestamp":"2025-07-12 16:39:02:392"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT `id`, `user_id`, `tool_name`, `file_size_mb`, `processing_time_seconds`, `input_file_count`, `output_file_count`, `success`, `error_message`, `parameters`, `created_at` AS `createdAt`, `updated_at` AS `updatedAt` FROM `usage_records` AS `UsageRecord` WHERE `UsageRecord`.`user_id` = '60ef0f29-ca38-4a6a-86cb-256f9362aaa3' AND `UsageRecord`.`created_at` >= '2025-07-11 23:00:00';","timestamp":"2025-07-12 16:39:02:392"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT count(*) AS `count` FROM `usage_records` AS `UsageRecord` WHERE `UsageRecord`.`user_id` = '60ef0f29-ca38-4a6a-86cb-256f9362aaa3' AND `UsageRecord`.`created_at` >= '2025-07-05 15:39:02';","timestamp":"2025-07-12 16:39:02:392"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT `id`, `user_id`, `tool_name`, `file_size_mb`, `processing_time_seconds`, `input_file_count`, `output_file_count`, `success`, `error_message`, `parameters`, `created_at` AS `createdAt`, `updated_at` AS `updatedAt` FROM `usage_records` AS `UsageRecord` WHERE `UsageRecord`.`user_id` = '60ef0f29-ca38-4a6a-86cb-256f9362aaa3' AND `UsageRecord`.`created_at` >= '2025-07-05 15:39:02' ORDER BY `createdAt` DESC LIMIT 0, 50;","timestamp":"2025-07-12 16:39:02:392"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT `id`, `email`, `password_hash`, `first_name`, `last_name`, `subscription_plan`, `is_verified`, `verification_token`, `reset_password_token`, `reset_password_expires`, `last_login_at`, `created_at` AS `createdAt`, `updated_at` AS `updatedAt` FROM `users` AS `User` WHERE `User`.`id` = '60ef0f29-ca38-4a6a-86cb-256f9362aaa3';","timestamp":"2025-07-12 16:39:05:395"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): UPDATE `users` SET `first_name`=?,`updated_at`=? WHERE `id` = ?","timestamp":"2025-07-12 16:39:05:395"}
{"email":"<EMAIL>","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mUser profile updated\u001b[39m","timestamp":"2025-07-12 16:39:05:395","userId":"60ef0f29-ca38-4a6a-86cb-256f9362aaa3"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT `id`, `email`, `password_hash`, `first_name`, `last_name`, `subscription_plan`, `is_verified`, `verification_token`, `reset_password_token`, `reset_password_expires`, `last_login_at`, `created_at` AS `createdAt`, `updated_at` AS `updatedAt` FROM `users` AS `User` WHERE `User`.`id` = '60ef0f29-ca38-4a6a-86cb-256f9362aaa3';","timestamp":"2025-07-12 16:39:06:396"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): UPDATE `users` SET `last_name`=?,`updated_at`=? WHERE `id` = ?","timestamp":"2025-07-12 16:39:06:396"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT `id`, `user_id`, `tool_name`, `file_size_mb`, `processing_time_seconds`, `input_file_count`, `output_file_count`, `success`, `error_message`, `parameters`, `created_at` AS `createdAt`, `updated_at` AS `updatedAt` FROM `usage_records` AS `UsageRecord` WHERE `UsageRecord`.`user_id` = '60ef0f29-ca38-4a6a-86cb-256f9362aaa3' AND `UsageRecord`.`created_at` >= '2025-07-11 23:00:00';","timestamp":"2025-07-12 16:39:06:396"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT count(*) AS `count` FROM `usage_records` AS `UsageRecord` WHERE `UsageRecord`.`user_id` = '60ef0f29-ca38-4a6a-86cb-256f9362aaa3' AND `UsageRecord`.`created_at` >= '2025-07-05 15:39:06';","timestamp":"2025-07-12 16:39:06:396"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT `id`, `user_id`, `tool_name`, `file_size_mb`, `processing_time_seconds`, `input_file_count`, `output_file_count`, `success`, `error_message`, `parameters`, `created_at` AS `createdAt`, `updated_at` AS `updatedAt` FROM `usage_records` AS `UsageRecord` WHERE `UsageRecord`.`user_id` = '60ef0f29-ca38-4a6a-86cb-256f9362aaa3' AND `UsageRecord`.`created_at` >= '2025-07-05 15:39:06' ORDER BY `createdAt` DESC LIMIT 0, 50;","timestamp":"2025-07-12 16:39:06:396"}
{"email":"<EMAIL>","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mUser profile updated\u001b[39m","timestamp":"2025-07-12 16:39:06:396","userId":"60ef0f29-ca38-4a6a-86cb-256f9362aaa3"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT `id`, `user_id`, `tool_name`, `file_size_mb`, `processing_time_seconds`, `input_file_count`, `output_file_count`, `success`, `error_message`, `parameters`, `created_at` AS `createdAt`, `updated_at` AS `updatedAt` FROM `usage_records` AS `UsageRecord` WHERE `UsageRecord`.`user_id` = '60ef0f29-ca38-4a6a-86cb-256f9362aaa3' AND `UsageRecord`.`created_at` >= '2025-07-11 23:00:00';","timestamp":"2025-07-12 16:39:06:396"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT count(*) AS `count` FROM `usage_records` AS `UsageRecord` WHERE `UsageRecord`.`user_id` = '60ef0f29-ca38-4a6a-86cb-256f9362aaa3' AND `UsageRecord`.`created_at` >= '2025-07-05 15:39:06';","timestamp":"2025-07-12 16:39:06:396"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT `id`, `user_id`, `tool_name`, `file_size_mb`, `processing_time_seconds`, `input_file_count`, `output_file_count`, `success`, `error_message`, `parameters`, `created_at` AS `createdAt`, `updated_at` AS `updatedAt` FROM `usage_records` AS `UsageRecord` WHERE `UsageRecord`.`user_id` = '60ef0f29-ca38-4a6a-86cb-256f9362aaa3' AND `UsageRecord`.`created_at` >= '2025-07-05 15:39:06' ORDER BY `createdAt` DESC LIMIT 0, 50;","timestamp":"2025-07-12 16:39:06:396"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT `id`, `email`, `password_hash`, `first_name`, `last_name`, `subscription_plan`, `is_verified`, `verification_token`, `reset_password_token`, `reset_password_expires`, `last_login_at`, `created_at` AS `createdAt`, `updated_at` AS `updatedAt` FROM `users` AS `User` WHERE `User`.`email` = '<EMAIL>';","timestamp":"2025-07-12 16:39:48:3948"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): INSERT INTO `users` (`id`,`email`,`password_hash`,`first_name`,`last_name`,`subscription_plan`,`is_verified`,`created_at`,`updated_at`) VALUES (?,?,?,?,?,?,?,?,?);","timestamp":"2025-07-12 16:39:49:3949"}
{"email":"<EMAIL>","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mUser registered successfully\u001b[39m","timestamp":"2025-07-12 16:39:49:3949","userId":"4c8d2427-f430-4985-b283-7295f5125db6"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT `id`, `user_id`, `tool_name`, `file_size_mb`, `processing_time_seconds`, `input_file_count`, `output_file_count`, `success`, `error_message`, `parameters`, `created_at` AS `createdAt`, `updated_at` AS `updatedAt` FROM `usage_records` AS `UsageRecord` WHERE `UsageRecord`.`user_id` = '4c8d2427-f430-4985-b283-7295f5125db6' AND `UsageRecord`.`created_at` >= '2025-07-11 23:00:00';","timestamp":"2025-07-12 16:39:49:3949"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT count(*) AS `count` FROM `usage_records` AS `UsageRecord` WHERE `UsageRecord`.`user_id` = '4c8d2427-f430-4985-b283-7295f5125db6' AND `UsageRecord`.`created_at` >= '2025-07-05 15:39:49';","timestamp":"2025-07-12 16:39:49:3949"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT `id`, `user_id`, `tool_name`, `file_size_mb`, `processing_time_seconds`, `input_file_count`, `output_file_count`, `success`, `error_message`, `parameters`, `created_at` AS `createdAt`, `updated_at` AS `updatedAt` FROM `usage_records` AS `UsageRecord` WHERE `UsageRecord`.`user_id` = '4c8d2427-f430-4985-b283-7295f5125db6' AND `UsageRecord`.`created_at` >= '2025-07-05 15:39:49' ORDER BY `createdAt` DESC LIMIT 0, 50;","timestamp":"2025-07-12 16:39:49:3949"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT `id`, `email`, `password_hash`, `first_name`, `last_name`, `subscription_plan`, `is_verified`, `verification_token`, `reset_password_token`, `reset_password_expires`, `last_login_at`, `created_at` AS `createdAt`, `updated_at` AS `updatedAt` FROM `users` AS `User` WHERE `User`.`id` = '4c8d2427-f430-4985-b283-7295f5125db6';","timestamp":"2025-07-12 16:45:25:4525"}
{"email":"<EMAIL>","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mUser profile updated\u001b[39m","timestamp":"2025-07-12 16:45:25:4525","userId":"4c8d2427-f430-4985-b283-7295f5125db6"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT `id`, `user_id`, `tool_name`, `file_size_mb`, `processing_time_seconds`, `input_file_count`, `output_file_count`, `success`, `error_message`, `parameters`, `created_at` AS `createdAt`, `updated_at` AS `updatedAt` FROM `usage_records` AS `UsageRecord` WHERE `UsageRecord`.`user_id` = '4c8d2427-f430-4985-b283-7295f5125db6' AND `UsageRecord`.`created_at` >= '2025-07-11 23:00:00';","timestamp":"2025-07-12 16:45:25:4525"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT count(*) AS `count` FROM `usage_records` AS `UsageRecord` WHERE `UsageRecord`.`user_id` = '4c8d2427-f430-4985-b283-7295f5125db6' AND `UsageRecord`.`created_at` >= '2025-07-05 15:45:25';","timestamp":"2025-07-12 16:45:25:4525"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT `id`, `user_id`, `tool_name`, `file_size_mb`, `processing_time_seconds`, `input_file_count`, `output_file_count`, `success`, `error_message`, `parameters`, `created_at` AS `createdAt`, `updated_at` AS `updatedAt` FROM `usage_records` AS `UsageRecord` WHERE `UsageRecord`.`user_id` = '4c8d2427-f430-4985-b283-7295f5125db6' AND `UsageRecord`.`created_at` >= '2025-07-05 15:45:25' ORDER BY `createdAt` DESC LIMIT 0, 50;","timestamp":"2025-07-12 16:45:25:4525"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT `id`, `email`, `password_hash`, `first_name`, `last_name`, `subscription_plan`, `is_verified`, `verification_token`, `reset_password_token`, `reset_password_expires`, `last_login_at`, `created_at` AS `createdAt`, `updated_at` AS `updatedAt` FROM `users` AS `User` WHERE `User`.`id` = '4c8d2427-f430-4985-b283-7295f5125db6';","timestamp":"2025-07-12 16:45:28:4528"}
{"email":"<EMAIL>","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mUser profile updated\u001b[39m","timestamp":"2025-07-12 16:45:28:4528","userId":"4c8d2427-f430-4985-b283-7295f5125db6"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT `id`, `user_id`, `tool_name`, `file_size_mb`, `processing_time_seconds`, `input_file_count`, `output_file_count`, `success`, `error_message`, `parameters`, `created_at` AS `createdAt`, `updated_at` AS `updatedAt` FROM `usage_records` AS `UsageRecord` WHERE `UsageRecord`.`user_id` = '4c8d2427-f430-4985-b283-7295f5125db6' AND `UsageRecord`.`created_at` >= '2025-07-11 23:00:00';","timestamp":"2025-07-12 16:45:28:4528"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT count(*) AS `count` FROM `usage_records` AS `UsageRecord` WHERE `UsageRecord`.`user_id` = '4c8d2427-f430-4985-b283-7295f5125db6' AND `UsageRecord`.`created_at` >= '2025-07-05 15:45:28';","timestamp":"2025-07-12 16:45:28:4528"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT `id`, `user_id`, `tool_name`, `file_size_mb`, `processing_time_seconds`, `input_file_count`, `output_file_count`, `success`, `error_message`, `parameters`, `created_at` AS `createdAt`, `updated_at` AS `updatedAt` FROM `usage_records` AS `UsageRecord` WHERE `UsageRecord`.`user_id` = '4c8d2427-f430-4985-b283-7295f5125db6' AND `UsageRecord`.`created_at` >= '2025-07-05 15:45:28' ORDER BY `createdAt` DESC LIMIT 0, 50;","timestamp":"2025-07-12 16:45:28:4528"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT `id`, `email`, `password_hash`, `first_name`, `last_name`, `subscription_plan`, `is_verified`, `verification_token`, `reset_password_token`, `reset_password_expires`, `last_login_at`, `created_at` AS `createdAt`, `updated_at` AS `updatedAt` FROM `users` AS `User` WHERE `User`.`id` = '4c8d2427-f430-4985-b283-7295f5125db6';","timestamp":"2025-07-12 16:45:32:4532"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT `id`, `email`, `password_hash`, `first_name`, `last_name`, `subscription_plan`, `is_verified`, `verification_token`, `reset_password_token`, `reset_password_expires`, `last_login_at`, `created_at` AS `createdAt`, `updated_at` AS `updatedAt` FROM `users` AS `User` WHERE `User`.`id` = '4c8d2427-f430-4985-b283-7295f5125db6';","timestamp":"2025-07-12 16:45:32:4532"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT `id`, `user_id`, `tool_name`, `file_size_mb`, `processing_time_seconds`, `input_file_count`, `output_file_count`, `success`, `error_message`, `parameters`, `created_at` AS `createdAt`, `updated_at` AS `updatedAt` FROM `usage_records` AS `UsageRecord` WHERE `UsageRecord`.`user_id` = '4c8d2427-f430-4985-b283-7295f5125db6' AND `UsageRecord`.`created_at` >= '2025-07-11 23:00:00';","timestamp":"2025-07-12 16:45:32:4532"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT count(*) AS `count` FROM `usage_records` AS `UsageRecord` WHERE `UsageRecord`.`user_id` = '4c8d2427-f430-4985-b283-7295f5125db6' AND `UsageRecord`.`created_at` >= '2025-07-05 15:45:32';","timestamp":"2025-07-12 16:45:32:4532"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT `id`, `user_id`, `tool_name`, `file_size_mb`, `processing_time_seconds`, `input_file_count`, `output_file_count`, `success`, `error_message`, `parameters`, `created_at` AS `createdAt`, `updated_at` AS `updatedAt` FROM `usage_records` AS `UsageRecord` WHERE `UsageRecord`.`user_id` = '4c8d2427-f430-4985-b283-7295f5125db6' AND `UsageRecord`.`created_at` >= '2025-07-05 15:45:32' ORDER BY `createdAt` DESC LIMIT 0, 50;","timestamp":"2025-07-12 16:45:32:4532"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT `id`, `user_id`, `tool_name`, `file_size_mb`, `processing_time_seconds`, `input_file_count`, `output_file_count`, `success`, `error_message`, `parameters`, `created_at` AS `createdAt`, `updated_at` AS `updatedAt` FROM `usage_records` AS `UsageRecord` WHERE `UsageRecord`.`user_id` = '4c8d2427-f430-4985-b283-7295f5125db6' AND `UsageRecord`.`created_at` >= '2025-07-11 23:00:00';","timestamp":"2025-07-12 16:45:32:4532"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT count(*) AS `count` FROM `usage_records` AS `UsageRecord` WHERE `UsageRecord`.`user_id` = '4c8d2427-f430-4985-b283-7295f5125db6' AND `UsageRecord`.`created_at` >= '2025-07-05 15:45:32';","timestamp":"2025-07-12 16:45:32:4532"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT `id`, `user_id`, `tool_name`, `file_size_mb`, `processing_time_seconds`, `input_file_count`, `output_file_count`, `success`, `error_message`, `parameters`, `created_at` AS `createdAt`, `updated_at` AS `updatedAt` FROM `usage_records` AS `UsageRecord` WHERE `UsageRecord`.`user_id` = '4c8d2427-f430-4985-b283-7295f5125db6' AND `UsageRecord`.`created_at` >= '2025-07-05 15:45:32' ORDER BY `createdAt` DESC LIMIT 0, 50;","timestamp":"2025-07-12 16:45:32:4532"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT count(*) AS `count` FROM `usage_records` AS `UsageRecord` WHERE `UsageRecord`.`user_id` = '4c8d2427-f430-4985-b283-7295f5125db6' AND `UsageRecord`.`created_at` >= '2025-06-12 15:45:37';","timestamp":"2025-07-12 16:45:37:4537"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT `id`, `user_id`, `tool_name`, `file_size_mb`, `processing_time_seconds`, `input_file_count`, `output_file_count`, `success`, `error_message`, `parameters`, `created_at` AS `createdAt`, `updated_at` AS `updatedAt` FROM `usage_records` AS `UsageRecord` WHERE `UsageRecord`.`user_id` = '4c8d2427-f430-4985-b283-7295f5125db6' AND `UsageRecord`.`created_at` >= '2025-06-12 15:45:37' ORDER BY `createdAt` DESC LIMIT 0, 50;","timestamp":"2025-07-12 16:45:37:4537"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT `id`, `email`, `password_hash`, `first_name`, `last_name`, `subscription_plan`, `is_verified`, `verification_token`, `reset_password_token`, `reset_password_expires`, `last_login_at`, `created_at` AS `createdAt`, `updated_at` AS `updatedAt` FROM `users` AS `User` WHERE `User`.`id` = '4c8d2427-f430-4985-b283-7295f5125db6';","timestamp":"2025-07-12 16:45:42:4542"}
{"email":"<EMAIL>","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mUser profile updated\u001b[39m","timestamp":"2025-07-12 16:45:42:4542","userId":"4c8d2427-f430-4985-b283-7295f5125db6"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT `id`, `user_id`, `tool_name`, `file_size_mb`, `processing_time_seconds`, `input_file_count`, `output_file_count`, `success`, `error_message`, `parameters`, `created_at` AS `createdAt`, `updated_at` AS `updatedAt` FROM `usage_records` AS `UsageRecord` WHERE `UsageRecord`.`user_id` = '4c8d2427-f430-4985-b283-7295f5125db6' AND `UsageRecord`.`created_at` >= '2025-07-11 23:00:00';","timestamp":"2025-07-12 16:45:42:4542"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT count(*) AS `count` FROM `usage_records` AS `UsageRecord` WHERE `UsageRecord`.`user_id` = '4c8d2427-f430-4985-b283-7295f5125db6' AND `UsageRecord`.`created_at` >= '2025-07-05 15:45:42';","timestamp":"2025-07-12 16:45:42:4542"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT `id`, `user_id`, `tool_name`, `file_size_mb`, `processing_time_seconds`, `input_file_count`, `output_file_count`, `success`, `error_message`, `parameters`, `created_at` AS `createdAt`, `updated_at` AS `updatedAt` FROM `usage_records` AS `UsageRecord` WHERE `UsageRecord`.`user_id` = '4c8d2427-f430-4985-b283-7295f5125db6' AND `UsageRecord`.`created_at` >= '2025-07-05 15:45:42' ORDER BY `createdAt` DESC LIMIT 0, 50;","timestamp":"2025-07-12 16:45:42:4542"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT `id`, `email`, `password_hash`, `first_name`, `last_name`, `subscription_plan`, `is_verified`, `verification_token`, `reset_password_token`, `reset_password_expires`, `last_login_at`, `created_at` AS `createdAt`, `updated_at` AS `updatedAt` FROM `users` AS `User` WHERE `User`.`id` = '4c8d2427-f430-4985-b283-7295f5125db6';","timestamp":"2025-07-12 16:45:43:4543"}
{"email":"<EMAIL>","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mUser profile updated\u001b[39m","timestamp":"2025-07-12 16:45:43:4543","userId":"4c8d2427-f430-4985-b283-7295f5125db6"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT `id`, `user_id`, `tool_name`, `file_size_mb`, `processing_time_seconds`, `input_file_count`, `output_file_count`, `success`, `error_message`, `parameters`, `created_at` AS `createdAt`, `updated_at` AS `updatedAt` FROM `usage_records` AS `UsageRecord` WHERE `UsageRecord`.`user_id` = '4c8d2427-f430-4985-b283-7295f5125db6' AND `UsageRecord`.`created_at` >= '2025-07-11 23:00:00';","timestamp":"2025-07-12 16:45:43:4543"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT count(*) AS `count` FROM `usage_records` AS `UsageRecord` WHERE `UsageRecord`.`user_id` = '4c8d2427-f430-4985-b283-7295f5125db6' AND `UsageRecord`.`created_at` >= '2025-07-05 15:45:43';","timestamp":"2025-07-12 16:45:43:4543"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT `id`, `user_id`, `tool_name`, `file_size_mb`, `processing_time_seconds`, `input_file_count`, `output_file_count`, `success`, `error_message`, `parameters`, `created_at` AS `createdAt`, `updated_at` AS `updatedAt` FROM `usage_records` AS `UsageRecord` WHERE `UsageRecord`.`user_id` = '4c8d2427-f430-4985-b283-7295f5125db6' AND `UsageRecord`.`created_at` >= '2025-07-05 15:45:43' ORDER BY `createdAt` DESC LIMIT 0, 50;","timestamp":"2025-07-12 16:45:43:4543"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT `id`, `email`, `password_hash`, `first_name`, `last_name`, `subscription_plan`, `is_verified`, `verification_token`, `reset_password_token`, `reset_password_expires`, `last_login_at`, `created_at` AS `createdAt`, `updated_at` AS `updatedAt` FROM `users` AS `User` WHERE `User`.`id` = '4c8d2427-f430-4985-b283-7295f5125db6';","timestamp":"2025-07-12 16:45:47:4547"}
{"email":"<EMAIL>","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mUser profile updated\u001b[39m","timestamp":"2025-07-12 16:45:47:4547","userId":"4c8d2427-f430-4985-b283-7295f5125db6"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT `id`, `user_id`, `tool_name`, `file_size_mb`, `processing_time_seconds`, `input_file_count`, `output_file_count`, `success`, `error_message`, `parameters`, `created_at` AS `createdAt`, `updated_at` AS `updatedAt` FROM `usage_records` AS `UsageRecord` WHERE `UsageRecord`.`user_id` = '4c8d2427-f430-4985-b283-7295f5125db6' AND `UsageRecord`.`created_at` >= '2025-07-11 23:00:00';","timestamp":"2025-07-12 16:45:47:4547"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT count(*) AS `count` FROM `usage_records` AS `UsageRecord` WHERE `UsageRecord`.`user_id` = '4c8d2427-f430-4985-b283-7295f5125db6' AND `UsageRecord`.`created_at` >= '2025-07-05 15:45:47';","timestamp":"2025-07-12 16:45:47:4547"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT `id`, `user_id`, `tool_name`, `file_size_mb`, `processing_time_seconds`, `input_file_count`, `output_file_count`, `success`, `error_message`, `parameters`, `created_at` AS `createdAt`, `updated_at` AS `updatedAt` FROM `usage_records` AS `UsageRecord` WHERE `UsageRecord`.`user_id` = '4c8d2427-f430-4985-b283-7295f5125db6' AND `UsageRecord`.`created_at` >= '2025-07-05 15:45:47' ORDER BY `createdAt` DESC LIMIT 0, 50;","timestamp":"2025-07-12 16:45:47:4547"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT `id`, `email`, `password_hash`, `first_name`, `last_name`, `subscription_plan`, `is_verified`, `verification_token`, `reset_password_token`, `reset_password_expires`, `last_login_at`, `created_at` AS `createdAt`, `updated_at` AS `updatedAt` FROM `users` AS `User` WHERE `User`.`id` = '4c8d2427-f430-4985-b283-7295f5125db6';","timestamp":"2025-07-12 16:45:47:4547"}
{"email":"<EMAIL>","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mUser profile updated\u001b[39m","timestamp":"2025-07-12 16:45:47:4547","userId":"4c8d2427-f430-4985-b283-7295f5125db6"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT `id`, `user_id`, `tool_name`, `file_size_mb`, `processing_time_seconds`, `input_file_count`, `output_file_count`, `success`, `error_message`, `parameters`, `created_at` AS `createdAt`, `updated_at` AS `updatedAt` FROM `usage_records` AS `UsageRecord` WHERE `UsageRecord`.`user_id` = '4c8d2427-f430-4985-b283-7295f5125db6' AND `UsageRecord`.`created_at` >= '2025-07-11 23:00:00';","timestamp":"2025-07-12 16:45:47:4547"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT count(*) AS `count` FROM `usage_records` AS `UsageRecord` WHERE `UsageRecord`.`user_id` = '4c8d2427-f430-4985-b283-7295f5125db6' AND `UsageRecord`.`created_at` >= '2025-07-05 15:45:47';","timestamp":"2025-07-12 16:45:47:4547"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT `id`, `user_id`, `tool_name`, `file_size_mb`, `processing_time_seconds`, `input_file_count`, `output_file_count`, `success`, `error_message`, `parameters`, `created_at` AS `createdAt`, `updated_at` AS `updatedAt` FROM `usage_records` AS `UsageRecord` WHERE `UsageRecord`.`user_id` = '4c8d2427-f430-4985-b283-7295f5125db6' AND `UsageRecord`.`created_at` >= '2025-07-05 15:45:47' ORDER BY `createdAt` DESC LIMIT 0, 50;","timestamp":"2025-07-12 16:45:47:4547"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT `id`, `email`, `password_hash`, `first_name`, `last_name`, `subscription_plan`, `is_verified`, `verification_token`, `reset_password_token`, `reset_password_expires`, `last_login_at`, `created_at` AS `createdAt`, `updated_at` AS `updatedAt` FROM `users` AS `User` WHERE `User`.`id` = '4c8d2427-f430-4985-b283-7295f5125db6';","timestamp":"2025-07-12 16:45:48:4548"}
{"email":"<EMAIL>","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mUser profile updated\u001b[39m","timestamp":"2025-07-12 16:45:48:4548","userId":"4c8d2427-f430-4985-b283-7295f5125db6"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT `id`, `user_id`, `tool_name`, `file_size_mb`, `processing_time_seconds`, `input_file_count`, `output_file_count`, `success`, `error_message`, `parameters`, `created_at` AS `createdAt`, `updated_at` AS `updatedAt` FROM `usage_records` AS `UsageRecord` WHERE `UsageRecord`.`user_id` = '4c8d2427-f430-4985-b283-7295f5125db6' AND `UsageRecord`.`created_at` >= '2025-07-11 23:00:00';","timestamp":"2025-07-12 16:45:48:4548"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT count(*) AS `count` FROM `usage_records` AS `UsageRecord` WHERE `UsageRecord`.`user_id` = '4c8d2427-f430-4985-b283-7295f5125db6' AND `UsageRecord`.`created_at` >= '2025-07-05 15:45:48';","timestamp":"2025-07-12 16:45:48:4548"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT `id`, `user_id`, `tool_name`, `file_size_mb`, `processing_time_seconds`, `input_file_count`, `output_file_count`, `success`, `error_message`, `parameters`, `created_at` AS `createdAt`, `updated_at` AS `updatedAt` FROM `usage_records` AS `UsageRecord` WHERE `UsageRecord`.`user_id` = '4c8d2427-f430-4985-b283-7295f5125db6' AND `UsageRecord`.`created_at` >= '2025-07-05 15:45:48' ORDER BY `createdAt` DESC LIMIT 0, 50;","timestamp":"2025-07-12 16:45:48:4548"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT `id`, `email`, `password_hash`, `first_name`, `last_name`, `subscription_plan`, `is_verified`, `verification_token`, `reset_password_token`, `reset_password_expires`, `last_login_at`, `created_at` AS `createdAt`, `updated_at` AS `updatedAt` FROM `users` AS `User` WHERE `User`.`id` = '4c8d2427-f430-4985-b283-7295f5125db6';","timestamp":"2025-07-12 16:45:48:4548"}
{"email":"<EMAIL>","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mUser profile updated\u001b[39m","timestamp":"2025-07-12 16:45:48:4548","userId":"4c8d2427-f430-4985-b283-7295f5125db6"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT `id`, `user_id`, `tool_name`, `file_size_mb`, `processing_time_seconds`, `input_file_count`, `output_file_count`, `success`, `error_message`, `parameters`, `created_at` AS `createdAt`, `updated_at` AS `updatedAt` FROM `usage_records` AS `UsageRecord` WHERE `UsageRecord`.`user_id` = '4c8d2427-f430-4985-b283-7295f5125db6' AND `UsageRecord`.`created_at` >= '2025-07-11 23:00:00';","timestamp":"2025-07-12 16:45:48:4548"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT count(*) AS `count` FROM `usage_records` AS `UsageRecord` WHERE `UsageRecord`.`user_id` = '4c8d2427-f430-4985-b283-7295f5125db6' AND `UsageRecord`.`created_at` >= '2025-07-05 15:45:48';","timestamp":"2025-07-12 16:45:48:4548"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT `id`, `user_id`, `tool_name`, `file_size_mb`, `processing_time_seconds`, `input_file_count`, `output_file_count`, `success`, `error_message`, `parameters`, `created_at` AS `createdAt`, `updated_at` AS `updatedAt` FROM `usage_records` AS `UsageRecord` WHERE `UsageRecord`.`user_id` = '4c8d2427-f430-4985-b283-7295f5125db6' AND `UsageRecord`.`created_at` >= '2025-07-05 15:45:48' ORDER BY `createdAt` DESC LIMIT 0, 50;","timestamp":"2025-07-12 16:45:48:4548"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT `id`, `email`, `password_hash`, `first_name`, `last_name`, `subscription_plan`, `is_verified`, `verification_token`, `reset_password_token`, `reset_password_expires`, `last_login_at`, `created_at` AS `createdAt`, `updated_at` AS `updatedAt` FROM `users` AS `User` WHERE `User`.`id` = '4c8d2427-f430-4985-b283-7295f5125db6';","timestamp":"2025-07-12 16:45:49:4549"}
{"email":"<EMAIL>","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mUser profile updated\u001b[39m","timestamp":"2025-07-12 16:45:49:4549","userId":"4c8d2427-f430-4985-b283-7295f5125db6"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT `id`, `user_id`, `tool_name`, `file_size_mb`, `processing_time_seconds`, `input_file_count`, `output_file_count`, `success`, `error_message`, `parameters`, `created_at` AS `createdAt`, `updated_at` AS `updatedAt` FROM `usage_records` AS `UsageRecord` WHERE `UsageRecord`.`user_id` = '4c8d2427-f430-4985-b283-7295f5125db6' AND `UsageRecord`.`created_at` >= '2025-07-11 23:00:00';","timestamp":"2025-07-12 16:45:49:4549"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT count(*) AS `count` FROM `usage_records` AS `UsageRecord` WHERE `UsageRecord`.`user_id` = '4c8d2427-f430-4985-b283-7295f5125db6' AND `UsageRecord`.`created_at` >= '2025-07-05 15:45:49';","timestamp":"2025-07-12 16:45:49:4549"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT `id`, `user_id`, `tool_name`, `file_size_mb`, `processing_time_seconds`, `input_file_count`, `output_file_count`, `success`, `error_message`, `parameters`, `created_at` AS `createdAt`, `updated_at` AS `updatedAt` FROM `usage_records` AS `UsageRecord` WHERE `UsageRecord`.`user_id` = '4c8d2427-f430-4985-b283-7295f5125db6' AND `UsageRecord`.`created_at` >= '2025-07-05 15:45:49' ORDER BY `createdAt` DESC LIMIT 0, 50;","timestamp":"2025-07-12 16:45:49:4549"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT `id`, `email`, `password_hash`, `first_name`, `last_name`, `subscription_plan`, `is_verified`, `verification_token`, `reset_password_token`, `reset_password_expires`, `last_login_at`, `created_at` AS `createdAt`, `updated_at` AS `updatedAt` FROM `users` AS `User` WHERE `User`.`email` = '<EMAIL>';","timestamp":"2025-07-12 16:46:16:4616"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): INSERT INTO `users` (`id`,`email`,`password_hash`,`first_name`,`last_name`,`subscription_plan`,`is_verified`,`created_at`,`updated_at`) VALUES (?,?,?,?,?,?,?,?,?);","timestamp":"2025-07-12 16:46:17:4617"}
{"email":"<EMAIL>","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mUser registered successfully\u001b[39m","timestamp":"2025-07-12 16:46:17:4617","userId":"00be1cfe-3d52-4ce1-a628-6948690387d7"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT `id`, `user_id`, `tool_name`, `file_size_mb`, `processing_time_seconds`, `input_file_count`, `output_file_count`, `success`, `error_message`, `parameters`, `created_at` AS `createdAt`, `updated_at` AS `updatedAt` FROM `usage_records` AS `UsageRecord` WHERE `UsageRecord`.`user_id` = '00be1cfe-3d52-4ce1-a628-6948690387d7' AND `UsageRecord`.`created_at` >= '2025-07-11 23:00:00';","timestamp":"2025-07-12 16:46:17:4617"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT `id`, `user_id`, `tool_name`, `file_size_mb`, `processing_time_seconds`, `input_file_count`, `output_file_count`, `success`, `error_message`, `parameters`, `created_at` AS `createdAt`, `updated_at` AS `updatedAt` FROM `usage_records` AS `UsageRecord` WHERE `UsageRecord`.`user_id` = '00be1cfe-3d52-4ce1-a628-6948690387d7' AND `UsageRecord`.`created_at` >= '2025-07-11 23:00:00';","timestamp":"2025-07-12 16:46:17:4617"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): INSERT INTO `processing_sessions` (`id`,`user_id`,`session_token`,`status`,`files_processed`,`quota_consumed`,`can_download`,`expires_at`,`created_at`,`updated_at`) VALUES (?,?,?,?,?,?,?,?,?,?);","timestamp":"2025-07-12 16:46:17:4617"}
{"error":"Authorization header is required","ip":"::1","isOperational":true,"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRequest error\u001b[39m","method":"POST","stack":"AuthenticationError: Authorization header is required\n    at Function.extractTokenFromHeader (G:\\PDF Porject\\backend\\src\\middleware\\auth.ts:67:13)\n    at authenticate (G:\\PDF Porject\\backend\\src\\middleware\\auth.ts:82:31)\n    at Layer.handle [as handle_request] (G:\\PDF Porject\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (G:\\PDF Porject\\backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (G:\\PDF Porject\\backend\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (G:\\PDF Porject\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at G:\\PDF Porject\\backend\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (G:\\PDF Porject\\backend\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (G:\\PDF Porject\\backend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (G:\\PDF Porject\\backend\\node_modules\\express\\lib\\router\\index.js:175:3)","statusCode":401,"timestamp":"2025-07-12 16:46:17:4617","url":"/api/pdf-tools/check-quota","userAgent":"axios/1.10.0"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT `id`, `email`, `password_hash`, `first_name`, `last_name`, `subscription_plan`, `is_verified`, `verification_token`, `reset_password_token`, `reset_password_expires`, `last_login_at`, `created_at` AS `createdAt`, `updated_at` AS `updatedAt` FROM `users` AS `User` WHERE `User`.`email` = '<EMAIL>';","timestamp":"2025-07-12 16:46:17:4617"}
{"error":"Invalid email or password","ip":"::1","isOperational":true,"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRequest error\u001b[39m","method":"POST","stack":"AuthenticationError: Invalid email or password\n    at G:\\PDF Porject\\backend\\src\\routes\\auth.ts:87:13\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","statusCode":401,"timestamp":"2025-07-12 16:46:17:4617","url":"/api/auth/login","userAgent":"axios/1.10.0"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT 1+1 AS result","timestamp":"2025-07-12 16:46:34:4634"}
{"database":"pdf","host":"127.0.0.1","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mDatabase connection established successfully\u001b[39m","port":3306,"timestamp":"2025-07-12 16:46:34:4634"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT TABLE_NAME FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_TYPE = 'BASE TABLE' AND TABLE_NAME = 'users' AND TABLE_SCHEMA = 'pdf'","timestamp":"2025-07-12 16:46:34:4634"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SHOW FULL COLUMNS FROM `users`;","timestamp":"2025-07-12 16:46:34:4634"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT CONSTRAINT_NAME as constraint_name,CONSTRAINT_NAME as constraintName,CONSTRAINT_SCHEMA as constraintSchema,CONSTRAINT_SCHEMA as constraintCatalog,TABLE_NAME as tableName,TABLE_SCHEMA as tableSchema,TABLE_SCHEMA as tableCatalog,COLUMN_NAME as columnName,REFERENCED_TABLE_SCHEMA as referencedTableSchema,REFERENCED_TABLE_SCHEMA as referencedTableCatalog,REFERENCED_TABLE_NAME as referencedTableName,REFERENCED_COLUMN_NAME as referencedColumnName FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE where TABLE_NAME = 'users' AND CONSTRAINT_NAME!='PRIMARY' AND CONSTRAINT_SCHEMA='pdf' AND REFERENCED_TABLE_NAME IS NOT NULL;","timestamp":"2025-07-12 16:46:34:4634"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `email` `email` VARCHAR(255) NOT NULL UNIQUE;","timestamp":"2025-07-12 16:46:34:4634"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `password_hash` `password_hash` VARCHAR(255) NOT NULL;","timestamp":"2025-07-12 16:46:35:4635"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `first_name` `first_name` VARCHAR(100) NOT NULL;","timestamp":"2025-07-12 16:46:35:4635"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `last_name` `last_name` VARCHAR(100) NOT NULL;","timestamp":"2025-07-12 16:46:35:4635"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `subscription_plan` `subscription_plan` ENUM('free', 'premium', 'enterprise') NOT NULL DEFAULT 'free';","timestamp":"2025-07-12 16:46:35:4635"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `is_verified` `is_verified` TINYINT(1) NOT NULL DEFAULT false;","timestamp":"2025-07-12 16:46:36:4636"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `verification_token` `verification_token` VARCHAR(255);","timestamp":"2025-07-12 16:46:36:4636"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `reset_password_token` `reset_password_token` VARCHAR(255);","timestamp":"2025-07-12 16:46:36:4636"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `reset_password_expires` `reset_password_expires` DATETIME;","timestamp":"2025-07-12 16:46:36:4636"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `last_login_at` `last_login_at` DATETIME;","timestamp":"2025-07-12 16:46:36:4636"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `created_at` `created_at` DATETIME NOT NULL;","timestamp":"2025-07-12 16:46:37:4637"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `updated_at` `updated_at` DATETIME NOT NULL;","timestamp":"2025-07-12 16:46:37:4637"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SHOW INDEX FROM `users`","timestamp":"2025-07-12 16:46:37:4637"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT TABLE_NAME FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_TYPE = 'BASE TABLE' AND TABLE_NAME = 'processing_sessions' AND TABLE_SCHEMA = 'pdf'","timestamp":"2025-07-12 16:46:37:4637"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SHOW FULL COLUMNS FROM `processing_sessions`;","timestamp":"2025-07-12 16:46:37:4637"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT CONSTRAINT_NAME as constraint_name,CONSTRAINT_NAME as constraintName,CONSTRAINT_SCHEMA as constraintSchema,CONSTRAINT_SCHEMA as constraintCatalog,TABLE_NAME as tableName,TABLE_SCHEMA as tableSchema,TABLE_SCHEMA as tableCatalog,COLUMN_NAME as columnName,REFERENCED_TABLE_SCHEMA as referencedTableSchema,REFERENCED_TABLE_SCHEMA as referencedTableCatalog,REFERENCED_TABLE_NAME as referencedTableName,REFERENCED_COLUMN_NAME as referencedColumnName FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE where TABLE_NAME = 'processing_sessions' AND CONSTRAINT_NAME!='PRIMARY' AND CONSTRAINT_SCHEMA='pdf' AND REFERENCED_TABLE_NAME IS NOT NULL;","timestamp":"2025-07-12 16:46:37:4637"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT CONSTRAINT_CATALOG AS constraintCatalog, CONSTRAINT_NAME AS constraintName, CONSTRAINT_SCHEMA AS constraintSchema, CONSTRAINT_TYPE AS constraintType, TABLE_NAME AS tableName, TABLE_SCHEMA AS tableSchema from INFORMATION_SCHEMA.TABLE_CONSTRAINTS WHERE table_name='processing_sessions' AND constraint_name = 'processing_sessions_ibfk_1' AND TABLE_SCHEMA = 'pdf';","timestamp":"2025-07-12 16:46:37:4637"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `processing_sessions` DROP FOREIGN KEY `processing_sessions_ibfk_1`;","timestamp":"2025-07-12 16:46:37:4637"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `processing_sessions` ADD FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE ON UPDATE CASCADE;","timestamp":"2025-07-12 16:46:37:4637"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `processing_sessions` CHANGE `session_token` `session_token` VARCHAR(255) NOT NULL UNIQUE;","timestamp":"2025-07-12 16:46:40:4640"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `processing_sessions` CHANGE `status` `status` ENUM('active', 'completed', 'expired') NOT NULL DEFAULT 'active';","timestamp":"2025-07-12 16:46:40:4640"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `processing_sessions` CHANGE `files_processed` `files_processed` INTEGER NOT NULL DEFAULT 0;","timestamp":"2025-07-12 16:46:40:4640"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `processing_sessions` CHANGE `quota_consumed` `quota_consumed` INTEGER NOT NULL DEFAULT 0;","timestamp":"2025-07-12 16:46:41:4641"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `processing_sessions` CHANGE `can_download` `can_download` TINYINT(1) NOT NULL DEFAULT true;","timestamp":"2025-07-12 16:46:41:4641"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `processing_sessions` CHANGE `expires_at` `expires_at` DATETIME NOT NULL;","timestamp":"2025-07-12 16:46:41:4641"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `processing_sessions` CHANGE `created_at` `created_at` DATETIME NOT NULL;","timestamp":"2025-07-12 16:46:41:4641"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `processing_sessions` CHANGE `updated_at` `updated_at` DATETIME NOT NULL;","timestamp":"2025-07-12 16:46:41:4641"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SHOW INDEX FROM `processing_sessions`","timestamp":"2025-07-12 16:46:41:4641"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT TABLE_NAME FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_TYPE = 'BASE TABLE' AND TABLE_NAME = 'usage_records' AND TABLE_SCHEMA = 'pdf'","timestamp":"2025-07-12 16:46:42:4642"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SHOW FULL COLUMNS FROM `usage_records`;","timestamp":"2025-07-12 16:46:42:4642"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT CONSTRAINT_NAME as constraint_name,CONSTRAINT_NAME as constraintName,CONSTRAINT_SCHEMA as constraintSchema,CONSTRAINT_SCHEMA as constraintCatalog,TABLE_NAME as tableName,TABLE_SCHEMA as tableSchema,TABLE_SCHEMA as tableCatalog,COLUMN_NAME as columnName,REFERENCED_TABLE_SCHEMA as referencedTableSchema,REFERENCED_TABLE_SCHEMA as referencedTableCatalog,REFERENCED_TABLE_NAME as referencedTableName,REFERENCED_COLUMN_NAME as referencedColumnName FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE where TABLE_NAME = 'usage_records' AND CONSTRAINT_NAME!='PRIMARY' AND CONSTRAINT_SCHEMA='pdf' AND REFERENCED_TABLE_NAME IS NOT NULL;","timestamp":"2025-07-12 16:46:42:4642"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT CONSTRAINT_CATALOG AS constraintCatalog, CONSTRAINT_NAME AS constraintName, CONSTRAINT_SCHEMA AS constraintSchema, CONSTRAINT_TYPE AS constraintType, TABLE_NAME AS tableName, TABLE_SCHEMA AS tableSchema from INFORMATION_SCHEMA.TABLE_CONSTRAINTS WHERE table_name='usage_records' AND constraint_name = 'usage_records_ibfk_1' AND TABLE_SCHEMA = 'pdf';","timestamp":"2025-07-12 16:46:42:4642"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `usage_records` DROP FOREIGN KEY `usage_records_ibfk_1`;","timestamp":"2025-07-12 16:46:42:4642"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `usage_records` ADD FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE ON UPDATE CASCADE;","timestamp":"2025-07-12 16:46:42:4642"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `usage_records` CHANGE `tool_name` `tool_name` VARCHAR(100) NOT NULL;","timestamp":"2025-07-12 16:46:43:4643"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `usage_records` CHANGE `file_size_mb` `file_size_mb` DECIMAL(10,2) NOT NULL;","timestamp":"2025-07-12 16:46:44:4644"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `usage_records` CHANGE `processing_time_seconds` `processing_time_seconds` INTEGER NOT NULL;","timestamp":"2025-07-12 16:46:44:4644"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `usage_records` CHANGE `input_file_count` `input_file_count` INTEGER NOT NULL;","timestamp":"2025-07-12 16:46:44:4644"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `usage_records` CHANGE `output_file_count` `output_file_count` INTEGER NOT NULL DEFAULT 0;","timestamp":"2025-07-12 16:46:44:4644"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `usage_records` CHANGE `success` `success` TINYINT(1) NOT NULL DEFAULT true;","timestamp":"2025-07-12 16:46:44:4644"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `usage_records` CHANGE `error_message` `error_message` TEXT;","timestamp":"2025-07-12 16:46:44:4644"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT 1+1 AS result","timestamp":"2025-07-12 16:46:48:4648"}
{"database":"pdf","host":"127.0.0.1","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mDatabase connection established successfully\u001b[39m","port":3306,"timestamp":"2025-07-12 16:46:48:4648"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT TABLE_NAME FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_TYPE = 'BASE TABLE' AND TABLE_NAME = 'users' AND TABLE_SCHEMA = 'pdf'","timestamp":"2025-07-12 16:46:48:4648"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SHOW FULL COLUMNS FROM `users`;","timestamp":"2025-07-12 16:46:48:4648"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT CONSTRAINT_NAME as constraint_name,CONSTRAINT_NAME as constraintName,CONSTRAINT_SCHEMA as constraintSchema,CONSTRAINT_SCHEMA as constraintCatalog,TABLE_NAME as tableName,TABLE_SCHEMA as tableSchema,TABLE_SCHEMA as tableCatalog,COLUMN_NAME as columnName,REFERENCED_TABLE_SCHEMA as referencedTableSchema,REFERENCED_TABLE_SCHEMA as referencedTableCatalog,REFERENCED_TABLE_NAME as referencedTableName,REFERENCED_COLUMN_NAME as referencedColumnName FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE where TABLE_NAME = 'users' AND CONSTRAINT_NAME!='PRIMARY' AND CONSTRAINT_SCHEMA='pdf' AND REFERENCED_TABLE_NAME IS NOT NULL;","timestamp":"2025-07-12 16:46:48:4648"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `email` `email` VARCHAR(255) NOT NULL UNIQUE;","timestamp":"2025-07-12 16:46:48:4648"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `password_hash` `password_hash` VARCHAR(255) NOT NULL;","timestamp":"2025-07-12 16:46:49:4649"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `first_name` `first_name` VARCHAR(100) NOT NULL;","timestamp":"2025-07-12 16:46:50:4650"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `last_name` `last_name` VARCHAR(100) NOT NULL;","timestamp":"2025-07-12 16:46:50:4650"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `subscription_plan` `subscription_plan` ENUM('free', 'premium', 'enterprise') NOT NULL DEFAULT 'free';","timestamp":"2025-07-12 16:46:50:4650"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `is_verified` `is_verified` TINYINT(1) NOT NULL DEFAULT false;","timestamp":"2025-07-12 16:46:51:4651"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `verification_token` `verification_token` VARCHAR(255);","timestamp":"2025-07-12 16:46:51:4651"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `reset_password_token` `reset_password_token` VARCHAR(255);","timestamp":"2025-07-12 16:46:51:4651"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `reset_password_expires` `reset_password_expires` DATETIME;","timestamp":"2025-07-12 16:46:52:4652"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `last_login_at` `last_login_at` DATETIME;","timestamp":"2025-07-12 16:46:52:4652"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `created_at` `created_at` DATETIME NOT NULL;","timestamp":"2025-07-12 16:46:52:4652"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `updated_at` `updated_at` DATETIME NOT NULL;","timestamp":"2025-07-12 16:46:53:4653"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SHOW INDEX FROM `users`","timestamp":"2025-07-12 16:46:53:4653"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT TABLE_NAME FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_TYPE = 'BASE TABLE' AND TABLE_NAME = 'processing_sessions' AND TABLE_SCHEMA = 'pdf'","timestamp":"2025-07-12 16:46:53:4653"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SHOW FULL COLUMNS FROM `processing_sessions`;","timestamp":"2025-07-12 16:46:53:4653"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT CONSTRAINT_NAME as constraint_name,CONSTRAINT_NAME as constraintName,CONSTRAINT_SCHEMA as constraintSchema,CONSTRAINT_SCHEMA as constraintCatalog,TABLE_NAME as tableName,TABLE_SCHEMA as tableSchema,TABLE_SCHEMA as tableCatalog,COLUMN_NAME as columnName,REFERENCED_TABLE_SCHEMA as referencedTableSchema,REFERENCED_TABLE_SCHEMA as referencedTableCatalog,REFERENCED_TABLE_NAME as referencedTableName,REFERENCED_COLUMN_NAME as referencedColumnName FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE where TABLE_NAME = 'processing_sessions' AND CONSTRAINT_NAME!='PRIMARY' AND CONSTRAINT_SCHEMA='pdf' AND REFERENCED_TABLE_NAME IS NOT NULL;","timestamp":"2025-07-12 16:46:53:4653"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT CONSTRAINT_CATALOG AS constraintCatalog, CONSTRAINT_NAME AS constraintName, CONSTRAINT_SCHEMA AS constraintSchema, CONSTRAINT_TYPE AS constraintType, TABLE_NAME AS tableName, TABLE_SCHEMA AS tableSchema from INFORMATION_SCHEMA.TABLE_CONSTRAINTS WHERE table_name='processing_sessions' AND constraint_name = 'processing_sessions_ibfk_1' AND TABLE_SCHEMA = 'pdf';","timestamp":"2025-07-12 16:46:53:4653"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `processing_sessions` DROP FOREIGN KEY `processing_sessions_ibfk_1`;","timestamp":"2025-07-12 16:46:53:4653"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `processing_sessions` ADD FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE ON UPDATE CASCADE;","timestamp":"2025-07-12 16:46:53:4653"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `processing_sessions` CHANGE `session_token` `session_token` VARCHAR(255) NOT NULL UNIQUE;","timestamp":"2025-07-12 16:46:56:4656"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `processing_sessions` CHANGE `status` `status` ENUM('active', 'completed', 'expired') NOT NULL DEFAULT 'active';","timestamp":"2025-07-12 16:46:57:4657"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `processing_sessions` CHANGE `files_processed` `files_processed` INTEGER NOT NULL DEFAULT 0;","timestamp":"2025-07-12 16:46:57:4657"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `processing_sessions` CHANGE `quota_consumed` `quota_consumed` INTEGER NOT NULL DEFAULT 0;","timestamp":"2025-07-12 16:46:57:4657"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `processing_sessions` CHANGE `can_download` `can_download` TINYINT(1) NOT NULL DEFAULT true;","timestamp":"2025-07-12 16:46:57:4657"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `processing_sessions` CHANGE `expires_at` `expires_at` DATETIME NOT NULL;","timestamp":"2025-07-12 16:46:58:4658"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `processing_sessions` CHANGE `created_at` `created_at` DATETIME NOT NULL;","timestamp":"2025-07-12 16:46:58:4658"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `processing_sessions` CHANGE `updated_at` `updated_at` DATETIME NOT NULL;","timestamp":"2025-07-12 16:46:58:4658"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SHOW INDEX FROM `processing_sessions`","timestamp":"2025-07-12 16:46:58:4658"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT TABLE_NAME FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_TYPE = 'BASE TABLE' AND TABLE_NAME = 'usage_records' AND TABLE_SCHEMA = 'pdf'","timestamp":"2025-07-12 16:46:58:4658"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SHOW FULL COLUMNS FROM `usage_records`;","timestamp":"2025-07-12 16:46:58:4658"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT CONSTRAINT_NAME as constraint_name,CONSTRAINT_NAME as constraintName,CONSTRAINT_SCHEMA as constraintSchema,CONSTRAINT_SCHEMA as constraintCatalog,TABLE_NAME as tableName,TABLE_SCHEMA as tableSchema,TABLE_SCHEMA as tableCatalog,COLUMN_NAME as columnName,REFERENCED_TABLE_SCHEMA as referencedTableSchema,REFERENCED_TABLE_SCHEMA as referencedTableCatalog,REFERENCED_TABLE_NAME as referencedTableName,REFERENCED_COLUMN_NAME as referencedColumnName FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE where TABLE_NAME = 'usage_records' AND CONSTRAINT_NAME!='PRIMARY' AND CONSTRAINT_SCHEMA='pdf' AND REFERENCED_TABLE_NAME IS NOT NULL;","timestamp":"2025-07-12 16:46:58:4658"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT CONSTRAINT_CATALOG AS constraintCatalog, CONSTRAINT_NAME AS constraintName, CONSTRAINT_SCHEMA AS constraintSchema, CONSTRAINT_TYPE AS constraintType, TABLE_NAME AS tableName, TABLE_SCHEMA AS tableSchema from INFORMATION_SCHEMA.TABLE_CONSTRAINTS WHERE table_name='usage_records' AND constraint_name = 'usage_records_ibfk_1' AND TABLE_SCHEMA = 'pdf';","timestamp":"2025-07-12 16:46:58:4658"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `usage_records` DROP FOREIGN KEY `usage_records_ibfk_1`;","timestamp":"2025-07-12 16:46:58:4658"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `usage_records` ADD FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE ON UPDATE CASCADE;","timestamp":"2025-07-12 16:46:58:4658"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `usage_records` CHANGE `tool_name` `tool_name` VARCHAR(100) NOT NULL;","timestamp":"2025-07-12 16:47:01:471"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `usage_records` CHANGE `file_size_mb` `file_size_mb` DECIMAL(10,2) NOT NULL;","timestamp":"2025-07-12 16:47:01:471"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `usage_records` CHANGE `processing_time_seconds` `processing_time_seconds` INTEGER NOT NULL;","timestamp":"2025-07-12 16:47:01:471"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `usage_records` CHANGE `input_file_count` `input_file_count` INTEGER NOT NULL;","timestamp":"2025-07-12 16:47:01:471"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `usage_records` CHANGE `output_file_count` `output_file_count` INTEGER NOT NULL DEFAULT 0;","timestamp":"2025-07-12 16:47:01:471"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `usage_records` CHANGE `success` `success` TINYINT(1) NOT NULL DEFAULT true;","timestamp":"2025-07-12 16:47:01:471"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT 1+1 AS result","timestamp":"2025-07-12 16:47:06:476"}
{"database":"pdf","host":"127.0.0.1","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mDatabase connection established successfully\u001b[39m","port":3306,"timestamp":"2025-07-12 16:47:06:476"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT TABLE_NAME FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_TYPE = 'BASE TABLE' AND TABLE_NAME = 'users' AND TABLE_SCHEMA = 'pdf'","timestamp":"2025-07-12 16:47:06:476"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SHOW FULL COLUMNS FROM `users`;","timestamp":"2025-07-12 16:47:06:476"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT CONSTRAINT_NAME as constraint_name,CONSTRAINT_NAME as constraintName,CONSTRAINT_SCHEMA as constraintSchema,CONSTRAINT_SCHEMA as constraintCatalog,TABLE_NAME as tableName,TABLE_SCHEMA as tableSchema,TABLE_SCHEMA as tableCatalog,COLUMN_NAME as columnName,REFERENCED_TABLE_SCHEMA as referencedTableSchema,REFERENCED_TABLE_SCHEMA as referencedTableCatalog,REFERENCED_TABLE_NAME as referencedTableName,REFERENCED_COLUMN_NAME as referencedColumnName FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE where TABLE_NAME = 'users' AND CONSTRAINT_NAME!='PRIMARY' AND CONSTRAINT_SCHEMA='pdf' AND REFERENCED_TABLE_NAME IS NOT NULL;","timestamp":"2025-07-12 16:47:06:476"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `email` `email` VARCHAR(255) NOT NULL UNIQUE;","timestamp":"2025-07-12 16:47:06:476"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `password_hash` `password_hash` VARCHAR(255) NOT NULL;","timestamp":"2025-07-12 16:47:07:477"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `first_name` `first_name` VARCHAR(100) NOT NULL;","timestamp":"2025-07-12 16:47:08:478"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `last_name` `last_name` VARCHAR(100) NOT NULL;","timestamp":"2025-07-12 16:47:08:478"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `subscription_plan` `subscription_plan` ENUM('free', 'premium', 'enterprise') NOT NULL DEFAULT 'free';","timestamp":"2025-07-12 16:47:09:479"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `is_verified` `is_verified` TINYINT(1) NOT NULL DEFAULT false;","timestamp":"2025-07-12 16:47:09:479"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `verification_token` `verification_token` VARCHAR(255);","timestamp":"2025-07-12 16:47:09:479"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `reset_password_token` `reset_password_token` VARCHAR(255);","timestamp":"2025-07-12 16:47:09:479"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `reset_password_expires` `reset_password_expires` DATETIME;","timestamp":"2025-07-12 16:47:09:479"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `last_login_at` `last_login_at` DATETIME;","timestamp":"2025-07-12 16:47:09:479"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `created_at` `created_at` DATETIME NOT NULL;","timestamp":"2025-07-12 16:47:10:4710"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `updated_at` `updated_at` DATETIME NOT NULL;","timestamp":"2025-07-12 16:47:10:4710"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SHOW INDEX FROM `users`","timestamp":"2025-07-12 16:47:10:4710"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT TABLE_NAME FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_TYPE = 'BASE TABLE' AND TABLE_NAME = 'processing_sessions' AND TABLE_SCHEMA = 'pdf'","timestamp":"2025-07-12 16:47:10:4710"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SHOW FULL COLUMNS FROM `processing_sessions`;","timestamp":"2025-07-12 16:47:10:4710"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT CONSTRAINT_NAME as constraint_name,CONSTRAINT_NAME as constraintName,CONSTRAINT_SCHEMA as constraintSchema,CONSTRAINT_SCHEMA as constraintCatalog,TABLE_NAME as tableName,TABLE_SCHEMA as tableSchema,TABLE_SCHEMA as tableCatalog,COLUMN_NAME as columnName,REFERENCED_TABLE_SCHEMA as referencedTableSchema,REFERENCED_TABLE_SCHEMA as referencedTableCatalog,REFERENCED_TABLE_NAME as referencedTableName,REFERENCED_COLUMN_NAME as referencedColumnName FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE where TABLE_NAME = 'processing_sessions' AND CONSTRAINT_NAME!='PRIMARY' AND CONSTRAINT_SCHEMA='pdf' AND REFERENCED_TABLE_NAME IS NOT NULL;","timestamp":"2025-07-12 16:47:10:4710"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT CONSTRAINT_CATALOG AS constraintCatalog, CONSTRAINT_NAME AS constraintName, CONSTRAINT_SCHEMA AS constraintSchema, CONSTRAINT_TYPE AS constraintType, TABLE_NAME AS tableName, TABLE_SCHEMA AS tableSchema from INFORMATION_SCHEMA.TABLE_CONSTRAINTS WHERE table_name='processing_sessions' AND constraint_name = 'processing_sessions_ibfk_1' AND TABLE_SCHEMA = 'pdf';","timestamp":"2025-07-12 16:47:10:4710"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `processing_sessions` DROP FOREIGN KEY `processing_sessions_ibfk_1`;","timestamp":"2025-07-12 16:47:10:4710"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `processing_sessions` ADD FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE ON UPDATE CASCADE;","timestamp":"2025-07-12 16:47:10:4710"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `processing_sessions` CHANGE `session_token` `session_token` VARCHAR(255) NOT NULL UNIQUE;","timestamp":"2025-07-12 16:47:14:4714"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `processing_sessions` CHANGE `status` `status` ENUM('active', 'completed', 'expired') NOT NULL DEFAULT 'active';","timestamp":"2025-07-12 16:47:14:4714"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `processing_sessions` CHANGE `files_processed` `files_processed` INTEGER NOT NULL DEFAULT 0;","timestamp":"2025-07-12 16:47:15:4715"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `processing_sessions` CHANGE `quota_consumed` `quota_consumed` INTEGER NOT NULL DEFAULT 0;","timestamp":"2025-07-12 16:47:15:4715"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `processing_sessions` CHANGE `can_download` `can_download` TINYINT(1) NOT NULL DEFAULT true;","timestamp":"2025-07-12 16:47:15:4715"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `processing_sessions` CHANGE `expires_at` `expires_at` DATETIME NOT NULL;","timestamp":"2025-07-12 16:47:15:4715"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `processing_sessions` CHANGE `created_at` `created_at` DATETIME NOT NULL;","timestamp":"2025-07-12 16:47:15:4715"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `processing_sessions` CHANGE `updated_at` `updated_at` DATETIME NOT NULL;","timestamp":"2025-07-12 16:47:15:4715"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SHOW INDEX FROM `processing_sessions`","timestamp":"2025-07-12 16:47:16:4716"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT TABLE_NAME FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_TYPE = 'BASE TABLE' AND TABLE_NAME = 'usage_records' AND TABLE_SCHEMA = 'pdf'","timestamp":"2025-07-12 16:47:16:4716"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SHOW FULL COLUMNS FROM `usage_records`;","timestamp":"2025-07-12 16:47:16:4716"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT CONSTRAINT_NAME as constraint_name,CONSTRAINT_NAME as constraintName,CONSTRAINT_SCHEMA as constraintSchema,CONSTRAINT_SCHEMA as constraintCatalog,TABLE_NAME as tableName,TABLE_SCHEMA as tableSchema,TABLE_SCHEMA as tableCatalog,COLUMN_NAME as columnName,REFERENCED_TABLE_SCHEMA as referencedTableSchema,REFERENCED_TABLE_SCHEMA as referencedTableCatalog,REFERENCED_TABLE_NAME as referencedTableName,REFERENCED_COLUMN_NAME as referencedColumnName FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE where TABLE_NAME = 'usage_records' AND CONSTRAINT_NAME!='PRIMARY' AND CONSTRAINT_SCHEMA='pdf' AND REFERENCED_TABLE_NAME IS NOT NULL;","timestamp":"2025-07-12 16:47:16:4716"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT CONSTRAINT_CATALOG AS constraintCatalog, CONSTRAINT_NAME AS constraintName, CONSTRAINT_SCHEMA AS constraintSchema, CONSTRAINT_TYPE AS constraintType, TABLE_NAME AS tableName, TABLE_SCHEMA AS tableSchema from INFORMATION_SCHEMA.TABLE_CONSTRAINTS WHERE table_name='usage_records' AND constraint_name = 'usage_records_ibfk_1' AND TABLE_SCHEMA = 'pdf';","timestamp":"2025-07-12 16:47:16:4716"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `usage_records` DROP FOREIGN KEY `usage_records_ibfk_1`;","timestamp":"2025-07-12 16:47:16:4716"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `usage_records` ADD FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE ON UPDATE CASCADE;","timestamp":"2025-07-12 16:47:16:4716"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `usage_records` CHANGE `tool_name` `tool_name` VARCHAR(100) NOT NULL;","timestamp":"2025-07-12 16:47:18:4718"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `usage_records` CHANGE `file_size_mb` `file_size_mb` DECIMAL(10,2) NOT NULL;","timestamp":"2025-07-12 16:47:18:4718"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `usage_records` CHANGE `processing_time_seconds` `processing_time_seconds` INTEGER NOT NULL;","timestamp":"2025-07-12 16:47:18:4718"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `usage_records` CHANGE `input_file_count` `input_file_count` INTEGER NOT NULL;","timestamp":"2025-07-12 16:47:19:4719"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `usage_records` CHANGE `output_file_count` `output_file_count` INTEGER NOT NULL DEFAULT 0;","timestamp":"2025-07-12 16:47:19:4719"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `usage_records` CHANGE `success` `success` TINYINT(1) NOT NULL DEFAULT true;","timestamp":"2025-07-12 16:47:19:4719"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `usage_records` CHANGE `error_message` `error_message` TEXT;","timestamp":"2025-07-12 16:47:19:4719"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `usage_records` CHANGE `parameters` `parameters` JSON;","timestamp":"2025-07-12 16:47:19:4719"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `usage_records` CHANGE `created_at` `created_at` DATETIME NOT NULL;","timestamp":"2025-07-12 16:47:19:4719"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `usage_records` CHANGE `updated_at` `updated_at` DATETIME NOT NULL;","timestamp":"2025-07-12 16:47:19:4719"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SHOW INDEX FROM `usage_records`","timestamp":"2025-07-12 16:47:19:4719"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mDatabase initialized successfully\u001b[39m","timestamp":"2025-07-12 16:47:19:4719"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mServer running on port 3001\u001b[39m","timestamp":"2025-07-12 16:47:20:4720"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mEnvironment: development\u001b[39m","timestamp":"2025-07-12 16:47:20:4720"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mDatabase connected and synchronized\u001b[39m","timestamp":"2025-07-12 16:47:20:4720"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT 1+1 AS result","timestamp":"2025-07-12 16:47:25:4725"}
{"database":"pdf","host":"127.0.0.1","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mDatabase connection established successfully\u001b[39m","port":3306,"timestamp":"2025-07-12 16:47:25:4725"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT TABLE_NAME FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_TYPE = 'BASE TABLE' AND TABLE_NAME = 'users' AND TABLE_SCHEMA = 'pdf'","timestamp":"2025-07-12 16:47:25:4725"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SHOW FULL COLUMNS FROM `users`;","timestamp":"2025-07-12 16:47:25:4725"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT CONSTRAINT_NAME as constraint_name,CONSTRAINT_NAME as constraintName,CONSTRAINT_SCHEMA as constraintSchema,CONSTRAINT_SCHEMA as constraintCatalog,TABLE_NAME as tableName,TABLE_SCHEMA as tableSchema,TABLE_SCHEMA as tableCatalog,COLUMN_NAME as columnName,REFERENCED_TABLE_SCHEMA as referencedTableSchema,REFERENCED_TABLE_SCHEMA as referencedTableCatalog,REFERENCED_TABLE_NAME as referencedTableName,REFERENCED_COLUMN_NAME as referencedColumnName FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE where TABLE_NAME = 'users' AND CONSTRAINT_NAME!='PRIMARY' AND CONSTRAINT_SCHEMA='pdf' AND REFERENCED_TABLE_NAME IS NOT NULL;","timestamp":"2025-07-12 16:47:25:4725"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `email` `email` VARCHAR(255) NOT NULL UNIQUE;","timestamp":"2025-07-12 16:47:25:4725"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `password_hash` `password_hash` VARCHAR(255) NOT NULL;","timestamp":"2025-07-12 16:47:26:4726"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `first_name` `first_name` VARCHAR(100) NOT NULL;","timestamp":"2025-07-12 16:47:26:4726"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `last_name` `last_name` VARCHAR(100) NOT NULL;","timestamp":"2025-07-12 16:47:26:4726"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `subscription_plan` `subscription_plan` ENUM('free', 'premium', 'enterprise') NOT NULL DEFAULT 'free';","timestamp":"2025-07-12 16:47:26:4726"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `is_verified` `is_verified` TINYINT(1) NOT NULL DEFAULT false;","timestamp":"2025-07-12 16:47:26:4726"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `verification_token` `verification_token` VARCHAR(255);","timestamp":"2025-07-12 16:47:26:4726"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `reset_password_token` `reset_password_token` VARCHAR(255);","timestamp":"2025-07-12 16:47:27:4727"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `reset_password_expires` `reset_password_expires` DATETIME;","timestamp":"2025-07-12 16:47:27:4727"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `last_login_at` `last_login_at` DATETIME;","timestamp":"2025-07-12 16:47:27:4727"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `created_at` `created_at` DATETIME NOT NULL;","timestamp":"2025-07-12 16:47:27:4727"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `updated_at` `updated_at` DATETIME NOT NULL;","timestamp":"2025-07-12 16:47:27:4727"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SHOW INDEX FROM `users`","timestamp":"2025-07-12 16:47:28:4728"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT TABLE_NAME FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_TYPE = 'BASE TABLE' AND TABLE_NAME = 'processing_sessions' AND TABLE_SCHEMA = 'pdf'","timestamp":"2025-07-12 16:47:28:4728"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SHOW FULL COLUMNS FROM `processing_sessions`;","timestamp":"2025-07-12 16:47:28:4728"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT CONSTRAINT_NAME as constraint_name,CONSTRAINT_NAME as constraintName,CONSTRAINT_SCHEMA as constraintSchema,CONSTRAINT_SCHEMA as constraintCatalog,TABLE_NAME as tableName,TABLE_SCHEMA as tableSchema,TABLE_SCHEMA as tableCatalog,COLUMN_NAME as columnName,REFERENCED_TABLE_SCHEMA as referencedTableSchema,REFERENCED_TABLE_SCHEMA as referencedTableCatalog,REFERENCED_TABLE_NAME as referencedTableName,REFERENCED_COLUMN_NAME as referencedColumnName FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE where TABLE_NAME = 'processing_sessions' AND CONSTRAINT_NAME!='PRIMARY' AND CONSTRAINT_SCHEMA='pdf' AND REFERENCED_TABLE_NAME IS NOT NULL;","timestamp":"2025-07-12 16:47:28:4728"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT CONSTRAINT_CATALOG AS constraintCatalog, CONSTRAINT_NAME AS constraintName, CONSTRAINT_SCHEMA AS constraintSchema, CONSTRAINT_TYPE AS constraintType, TABLE_NAME AS tableName, TABLE_SCHEMA AS tableSchema from INFORMATION_SCHEMA.TABLE_CONSTRAINTS WHERE table_name='processing_sessions' AND constraint_name = 'processing_sessions_ibfk_1' AND TABLE_SCHEMA = 'pdf';","timestamp":"2025-07-12 16:47:28:4728"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `processing_sessions` DROP FOREIGN KEY `processing_sessions_ibfk_1`;","timestamp":"2025-07-12 16:47:28:4728"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `processing_sessions` ADD FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE ON UPDATE CASCADE;","timestamp":"2025-07-12 16:47:28:4728"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT 1+1 AS result","timestamp":"2025-07-12 16:48:04:484"}
{"database":"pdf","host":"127.0.0.1","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mDatabase connection established successfully\u001b[39m","port":3306,"timestamp":"2025-07-12 16:48:04:484"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT TABLE_NAME FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_TYPE = 'BASE TABLE' AND TABLE_NAME = 'users' AND TABLE_SCHEMA = 'pdf'","timestamp":"2025-07-12 16:48:04:484"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SHOW FULL COLUMNS FROM `users`;","timestamp":"2025-07-12 16:48:04:484"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT CONSTRAINT_NAME as constraint_name,CONSTRAINT_NAME as constraintName,CONSTRAINT_SCHEMA as constraintSchema,CONSTRAINT_SCHEMA as constraintCatalog,TABLE_NAME as tableName,TABLE_SCHEMA as tableSchema,TABLE_SCHEMA as tableCatalog,COLUMN_NAME as columnName,REFERENCED_TABLE_SCHEMA as referencedTableSchema,REFERENCED_TABLE_SCHEMA as referencedTableCatalog,REFERENCED_TABLE_NAME as referencedTableName,REFERENCED_COLUMN_NAME as referencedColumnName FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE where TABLE_NAME = 'users' AND CONSTRAINT_NAME!='PRIMARY' AND CONSTRAINT_SCHEMA='pdf' AND REFERENCED_TABLE_NAME IS NOT NULL;","timestamp":"2025-07-12 16:48:04:484"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `email` `email` VARCHAR(255) NOT NULL UNIQUE;","timestamp":"2025-07-12 16:48:04:484"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `password_hash` `password_hash` VARCHAR(255) NOT NULL;","timestamp":"2025-07-12 16:48:05:485"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `first_name` `first_name` VARCHAR(100) NOT NULL;","timestamp":"2025-07-12 16:48:05:485"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `last_name` `last_name` VARCHAR(100) NOT NULL;","timestamp":"2025-07-12 16:48:05:485"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `subscription_plan` `subscription_plan` ENUM('free', 'premium', 'enterprise') NOT NULL DEFAULT 'free';","timestamp":"2025-07-12 16:48:05:485"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `is_verified` `is_verified` TINYINT(1) NOT NULL DEFAULT false;","timestamp":"2025-07-12 16:48:06:486"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `verification_token` `verification_token` VARCHAR(255);","timestamp":"2025-07-12 16:48:06:486"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `reset_password_token` `reset_password_token` VARCHAR(255);","timestamp":"2025-07-12 16:48:06:486"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `reset_password_expires` `reset_password_expires` DATETIME;","timestamp":"2025-07-12 16:48:06:486"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `last_login_at` `last_login_at` DATETIME;","timestamp":"2025-07-12 16:48:06:486"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `created_at` `created_at` DATETIME NOT NULL;","timestamp":"2025-07-12 16:48:06:486"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `updated_at` `updated_at` DATETIME NOT NULL;","timestamp":"2025-07-12 16:48:07:487"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SHOW INDEX FROM `users`","timestamp":"2025-07-12 16:48:07:487"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT TABLE_NAME FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_TYPE = 'BASE TABLE' AND TABLE_NAME = 'processing_sessions' AND TABLE_SCHEMA = 'pdf'","timestamp":"2025-07-12 16:48:07:487"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SHOW FULL COLUMNS FROM `processing_sessions`;","timestamp":"2025-07-12 16:48:07:487"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT CONSTRAINT_NAME as constraint_name,CONSTRAINT_NAME as constraintName,CONSTRAINT_SCHEMA as constraintSchema,CONSTRAINT_SCHEMA as constraintCatalog,TABLE_NAME as tableName,TABLE_SCHEMA as tableSchema,TABLE_SCHEMA as tableCatalog,COLUMN_NAME as columnName,REFERENCED_TABLE_SCHEMA as referencedTableSchema,REFERENCED_TABLE_SCHEMA as referencedTableCatalog,REFERENCED_TABLE_NAME as referencedTableName,REFERENCED_COLUMN_NAME as referencedColumnName FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE where TABLE_NAME = 'processing_sessions' AND CONSTRAINT_NAME!='PRIMARY' AND CONSTRAINT_SCHEMA='pdf' AND REFERENCED_TABLE_NAME IS NOT NULL;","timestamp":"2025-07-12 16:48:07:487"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT CONSTRAINT_CATALOG AS constraintCatalog, CONSTRAINT_NAME AS constraintName, CONSTRAINT_SCHEMA AS constraintSchema, CONSTRAINT_TYPE AS constraintType, TABLE_NAME AS tableName, TABLE_SCHEMA AS tableSchema from INFORMATION_SCHEMA.TABLE_CONSTRAINTS WHERE table_name='processing_sessions' AND constraint_name = 'processing_sessions_ibfk_1' AND TABLE_SCHEMA = 'pdf';","timestamp":"2025-07-12 16:48:07:487"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `processing_sessions` DROP FOREIGN KEY `processing_sessions_ibfk_1`;","timestamp":"2025-07-12 16:48:07:487"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `processing_sessions` ADD FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE ON UPDATE CASCADE;","timestamp":"2025-07-12 16:48:07:487"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `processing_sessions` CHANGE `session_token` `session_token` VARCHAR(255) NOT NULL UNIQUE;","timestamp":"2025-07-12 16:48:10:4810"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `processing_sessions` CHANGE `status` `status` ENUM('active', 'completed', 'expired') NOT NULL DEFAULT 'active';","timestamp":"2025-07-12 16:48:11:4811"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `processing_sessions` CHANGE `files_processed` `files_processed` INTEGER NOT NULL DEFAULT 0;","timestamp":"2025-07-12 16:48:11:4811"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT 1+1 AS result","timestamp":"2025-07-12 16:48:31:4831"}
{"database":"pdf","host":"127.0.0.1","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mDatabase connection established successfully\u001b[39m","port":3306,"timestamp":"2025-07-12 16:48:31:4831"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT TABLE_NAME FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_TYPE = 'BASE TABLE' AND TABLE_NAME = 'users' AND TABLE_SCHEMA = 'pdf'","timestamp":"2025-07-12 16:48:31:4831"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SHOW FULL COLUMNS FROM `users`;","timestamp":"2025-07-12 16:48:31:4831"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT CONSTRAINT_NAME as constraint_name,CONSTRAINT_NAME as constraintName,CONSTRAINT_SCHEMA as constraintSchema,CONSTRAINT_SCHEMA as constraintCatalog,TABLE_NAME as tableName,TABLE_SCHEMA as tableSchema,TABLE_SCHEMA as tableCatalog,COLUMN_NAME as columnName,REFERENCED_TABLE_SCHEMA as referencedTableSchema,REFERENCED_TABLE_SCHEMA as referencedTableCatalog,REFERENCED_TABLE_NAME as referencedTableName,REFERENCED_COLUMN_NAME as referencedColumnName FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE where TABLE_NAME = 'users' AND CONSTRAINT_NAME!='PRIMARY' AND CONSTRAINT_SCHEMA='pdf' AND REFERENCED_TABLE_NAME IS NOT NULL;","timestamp":"2025-07-12 16:48:31:4831"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `email` `email` VARCHAR(255) NOT NULL UNIQUE;","timestamp":"2025-07-12 16:48:31:4831"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `password_hash` `password_hash` VARCHAR(255) NOT NULL;","timestamp":"2025-07-12 16:48:32:4832"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `first_name` `first_name` VARCHAR(100) NOT NULL;","timestamp":"2025-07-12 16:48:32:4832"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `last_name` `last_name` VARCHAR(100) NOT NULL;","timestamp":"2025-07-12 16:48:32:4832"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `subscription_plan` `subscription_plan` ENUM('free', 'premium', 'enterprise') NOT NULL DEFAULT 'free';","timestamp":"2025-07-12 16:48:32:4832"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `is_verified` `is_verified` TINYINT(1) NOT NULL DEFAULT false;","timestamp":"2025-07-12 16:48:33:4833"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `verification_token` `verification_token` VARCHAR(255);","timestamp":"2025-07-12 16:48:34:4834"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `reset_password_token` `reset_password_token` VARCHAR(255);","timestamp":"2025-07-12 16:48:34:4834"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `reset_password_expires` `reset_password_expires` DATETIME;","timestamp":"2025-07-12 16:48:35:4835"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `last_login_at` `last_login_at` DATETIME;","timestamp":"2025-07-12 16:48:35:4835"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `created_at` `created_at` DATETIME NOT NULL;","timestamp":"2025-07-12 16:48:35:4835"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `updated_at` `updated_at` DATETIME NOT NULL;","timestamp":"2025-07-12 16:48:35:4835"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SHOW INDEX FROM `users`","timestamp":"2025-07-12 16:48:35:4835"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT TABLE_NAME FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_TYPE = 'BASE TABLE' AND TABLE_NAME = 'processing_sessions' AND TABLE_SCHEMA = 'pdf'","timestamp":"2025-07-12 16:48:35:4835"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SHOW FULL COLUMNS FROM `processing_sessions`;","timestamp":"2025-07-12 16:48:35:4835"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT CONSTRAINT_NAME as constraint_name,CONSTRAINT_NAME as constraintName,CONSTRAINT_SCHEMA as constraintSchema,CONSTRAINT_SCHEMA as constraintCatalog,TABLE_NAME as tableName,TABLE_SCHEMA as tableSchema,TABLE_SCHEMA as tableCatalog,COLUMN_NAME as columnName,REFERENCED_TABLE_SCHEMA as referencedTableSchema,REFERENCED_TABLE_SCHEMA as referencedTableCatalog,REFERENCED_TABLE_NAME as referencedTableName,REFERENCED_COLUMN_NAME as referencedColumnName FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE where TABLE_NAME = 'processing_sessions' AND CONSTRAINT_NAME!='PRIMARY' AND CONSTRAINT_SCHEMA='pdf' AND REFERENCED_TABLE_NAME IS NOT NULL;","timestamp":"2025-07-12 16:48:35:4835"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT CONSTRAINT_CATALOG AS constraintCatalog, CONSTRAINT_NAME AS constraintName, CONSTRAINT_SCHEMA AS constraintSchema, CONSTRAINT_TYPE AS constraintType, TABLE_NAME AS tableName, TABLE_SCHEMA AS tableSchema from INFORMATION_SCHEMA.TABLE_CONSTRAINTS WHERE table_name='processing_sessions' AND constraint_name = 'processing_sessions_ibfk_1' AND TABLE_SCHEMA = 'pdf';","timestamp":"2025-07-12 16:48:35:4835"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `processing_sessions` DROP FOREIGN KEY `processing_sessions_ibfk_1`;","timestamp":"2025-07-12 16:48:35:4835"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `processing_sessions` ADD FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE ON UPDATE CASCADE;","timestamp":"2025-07-12 16:48:35:4835"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT 1+1 AS result","timestamp":"2025-07-12 16:48:41:4841"}
{"database":"pdf","host":"127.0.0.1","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mDatabase connection established successfully\u001b[39m","port":3306,"timestamp":"2025-07-12 16:48:41:4841"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT TABLE_NAME FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_TYPE = 'BASE TABLE' AND TABLE_NAME = 'users' AND TABLE_SCHEMA = 'pdf'","timestamp":"2025-07-12 16:48:41:4841"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SHOW FULL COLUMNS FROM `users`;","timestamp":"2025-07-12 16:48:41:4841"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT CONSTRAINT_NAME as constraint_name,CONSTRAINT_NAME as constraintName,CONSTRAINT_SCHEMA as constraintSchema,CONSTRAINT_SCHEMA as constraintCatalog,TABLE_NAME as tableName,TABLE_SCHEMA as tableSchema,TABLE_SCHEMA as tableCatalog,COLUMN_NAME as columnName,REFERENCED_TABLE_SCHEMA as referencedTableSchema,REFERENCED_TABLE_SCHEMA as referencedTableCatalog,REFERENCED_TABLE_NAME as referencedTableName,REFERENCED_COLUMN_NAME as referencedColumnName FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE where TABLE_NAME = 'users' AND CONSTRAINT_NAME!='PRIMARY' AND CONSTRAINT_SCHEMA='pdf' AND REFERENCED_TABLE_NAME IS NOT NULL;","timestamp":"2025-07-12 16:48:41:4841"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `email` `email` VARCHAR(255) NOT NULL UNIQUE;","timestamp":"2025-07-12 16:48:41:4841"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `password_hash` `password_hash` VARCHAR(255) NOT NULL;","timestamp":"2025-07-12 16:48:41:4841"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `first_name` `first_name` VARCHAR(100) NOT NULL;","timestamp":"2025-07-12 16:48:42:4842"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `last_name` `last_name` VARCHAR(100) NOT NULL;","timestamp":"2025-07-12 16:48:42:4842"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `subscription_plan` `subscription_plan` ENUM('free', 'premium', 'enterprise') NOT NULL DEFAULT 'free';","timestamp":"2025-07-12 16:48:42:4842"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `is_verified` `is_verified` TINYINT(1) NOT NULL DEFAULT false;","timestamp":"2025-07-12 16:48:42:4842"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `verification_token` `verification_token` VARCHAR(255);","timestamp":"2025-07-12 16:48:42:4842"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `reset_password_token` `reset_password_token` VARCHAR(255);","timestamp":"2025-07-12 16:48:43:4843"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `reset_password_expires` `reset_password_expires` DATETIME;","timestamp":"2025-07-12 16:48:43:4843"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `last_login_at` `last_login_at` DATETIME;","timestamp":"2025-07-12 16:48:43:4843"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `created_at` `created_at` DATETIME NOT NULL;","timestamp":"2025-07-12 16:48:43:4843"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `updated_at` `updated_at` DATETIME NOT NULL;","timestamp":"2025-07-12 16:48:43:4843"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SHOW INDEX FROM `users`","timestamp":"2025-07-12 16:48:43:4843"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT TABLE_NAME FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_TYPE = 'BASE TABLE' AND TABLE_NAME = 'processing_sessions' AND TABLE_SCHEMA = 'pdf'","timestamp":"2025-07-12 16:48:43:4843"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SHOW FULL COLUMNS FROM `processing_sessions`;","timestamp":"2025-07-12 16:48:43:4843"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT CONSTRAINT_NAME as constraint_name,CONSTRAINT_NAME as constraintName,CONSTRAINT_SCHEMA as constraintSchema,CONSTRAINT_SCHEMA as constraintCatalog,TABLE_NAME as tableName,TABLE_SCHEMA as tableSchema,TABLE_SCHEMA as tableCatalog,COLUMN_NAME as columnName,REFERENCED_TABLE_SCHEMA as referencedTableSchema,REFERENCED_TABLE_SCHEMA as referencedTableCatalog,REFERENCED_TABLE_NAME as referencedTableName,REFERENCED_COLUMN_NAME as referencedColumnName FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE where TABLE_NAME = 'processing_sessions' AND CONSTRAINT_NAME!='PRIMARY' AND CONSTRAINT_SCHEMA='pdf' AND REFERENCED_TABLE_NAME IS NOT NULL;","timestamp":"2025-07-12 16:48:43:4843"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT CONSTRAINT_CATALOG AS constraintCatalog, CONSTRAINT_NAME AS constraintName, CONSTRAINT_SCHEMA AS constraintSchema, CONSTRAINT_TYPE AS constraintType, TABLE_NAME AS tableName, TABLE_SCHEMA AS tableSchema from INFORMATION_SCHEMA.TABLE_CONSTRAINTS WHERE table_name='processing_sessions' AND constraint_name = 'processing_sessions_ibfk_1' AND TABLE_SCHEMA = 'pdf';","timestamp":"2025-07-12 16:48:43:4843"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `processing_sessions` DROP FOREIGN KEY `processing_sessions_ibfk_1`;","timestamp":"2025-07-12 16:48:43:4843"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `processing_sessions` ADD FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE ON UPDATE CASCADE;","timestamp":"2025-07-12 16:48:44:4844"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `processing_sessions` CHANGE `session_token` `session_token` VARCHAR(255) NOT NULL UNIQUE;","timestamp":"2025-07-12 16:48:48:4848"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `processing_sessions` CHANGE `status` `status` ENUM('active', 'completed', 'expired') NOT NULL DEFAULT 'active';","timestamp":"2025-07-12 16:48:48:4848"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `processing_sessions` CHANGE `files_processed` `files_processed` INTEGER NOT NULL DEFAULT 0;","timestamp":"2025-07-12 16:48:49:4849"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `processing_sessions` CHANGE `quota_consumed` `quota_consumed` INTEGER NOT NULL DEFAULT 0;","timestamp":"2025-07-12 16:48:49:4849"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `processing_sessions` CHANGE `can_download` `can_download` TINYINT(1) NOT NULL DEFAULT true;","timestamp":"2025-07-12 16:48:49:4849"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `processing_sessions` CHANGE `expires_at` `expires_at` DATETIME NOT NULL;","timestamp":"2025-07-12 16:48:49:4849"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `processing_sessions` CHANGE `created_at` `created_at` DATETIME NOT NULL;","timestamp":"2025-07-12 16:48:49:4849"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `processing_sessions` CHANGE `updated_at` `updated_at` DATETIME NOT NULL;","timestamp":"2025-07-12 16:48:49:4849"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SHOW INDEX FROM `processing_sessions`","timestamp":"2025-07-12 16:48:49:4849"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT TABLE_NAME FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_TYPE = 'BASE TABLE' AND TABLE_NAME = 'usage_records' AND TABLE_SCHEMA = 'pdf'","timestamp":"2025-07-12 16:48:49:4849"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SHOW FULL COLUMNS FROM `usage_records`;","timestamp":"2025-07-12 16:48:49:4849"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT CONSTRAINT_NAME as constraint_name,CONSTRAINT_NAME as constraintName,CONSTRAINT_SCHEMA as constraintSchema,CONSTRAINT_SCHEMA as constraintCatalog,TABLE_NAME as tableName,TABLE_SCHEMA as tableSchema,TABLE_SCHEMA as tableCatalog,COLUMN_NAME as columnName,REFERENCED_TABLE_SCHEMA as referencedTableSchema,REFERENCED_TABLE_SCHEMA as referencedTableCatalog,REFERENCED_TABLE_NAME as referencedTableName,REFERENCED_COLUMN_NAME as referencedColumnName FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE where TABLE_NAME = 'usage_records' AND CONSTRAINT_NAME!='PRIMARY' AND CONSTRAINT_SCHEMA='pdf' AND REFERENCED_TABLE_NAME IS NOT NULL;","timestamp":"2025-07-12 16:48:49:4849"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT CONSTRAINT_CATALOG AS constraintCatalog, CONSTRAINT_NAME AS constraintName, CONSTRAINT_SCHEMA AS constraintSchema, CONSTRAINT_TYPE AS constraintType, TABLE_NAME AS tableName, TABLE_SCHEMA AS tableSchema from INFORMATION_SCHEMA.TABLE_CONSTRAINTS WHERE table_name='usage_records' AND constraint_name = 'usage_records_ibfk_1' AND TABLE_SCHEMA = 'pdf';","timestamp":"2025-07-12 16:48:50:4850"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `usage_records` DROP FOREIGN KEY `usage_records_ibfk_1`;","timestamp":"2025-07-12 16:48:50:4850"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `usage_records` ADD FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE ON UPDATE CASCADE;","timestamp":"2025-07-12 16:48:50:4850"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `usage_records` CHANGE `tool_name` `tool_name` VARCHAR(100) NOT NULL;","timestamp":"2025-07-12 16:48:52:4852"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `usage_records` CHANGE `file_size_mb` `file_size_mb` DECIMAL(10,2) NOT NULL;","timestamp":"2025-07-12 16:48:52:4852"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `usage_records` CHANGE `processing_time_seconds` `processing_time_seconds` INTEGER NOT NULL;","timestamp":"2025-07-12 16:48:52:4852"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `usage_records` CHANGE `input_file_count` `input_file_count` INTEGER NOT NULL;","timestamp":"2025-07-12 16:48:53:4853"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `usage_records` CHANGE `output_file_count` `output_file_count` INTEGER NOT NULL DEFAULT 0;","timestamp":"2025-07-12 16:48:53:4853"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `usage_records` CHANGE `success` `success` TINYINT(1) NOT NULL DEFAULT true;","timestamp":"2025-07-12 16:48:53:4853"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `usage_records` CHANGE `error_message` `error_message` TEXT;","timestamp":"2025-07-12 16:48:53:4853"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `usage_records` CHANGE `parameters` `parameters` JSON;","timestamp":"2025-07-12 16:48:53:4853"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `usage_records` CHANGE `created_at` `created_at` DATETIME NOT NULL;","timestamp":"2025-07-12 16:48:53:4853"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `usage_records` CHANGE `updated_at` `updated_at` DATETIME NOT NULL;","timestamp":"2025-07-12 16:48:54:4854"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SHOW INDEX FROM `usage_records`","timestamp":"2025-07-12 16:48:54:4854"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mDatabase initialized successfully\u001b[39m","timestamp":"2025-07-12 16:48:54:4854"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mServer running on port 3001\u001b[39m","timestamp":"2025-07-12 16:48:54:4854"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mEnvironment: development\u001b[39m","timestamp":"2025-07-12 16:48:54:4854"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mDatabase connected and synchronized\u001b[39m","timestamp":"2025-07-12 16:48:54:4854"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT 1+1 AS result","timestamp":"2025-07-12 16:49:37:4937"}
{"database":"pdf","host":"127.0.0.1","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mDatabase connection established successfully\u001b[39m","port":3306,"timestamp":"2025-07-12 16:49:37:4937"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT TABLE_NAME FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_TYPE = 'BASE TABLE' AND TABLE_NAME = 'users' AND TABLE_SCHEMA = 'pdf'","timestamp":"2025-07-12 16:49:37:4937"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SHOW FULL COLUMNS FROM `users`;","timestamp":"2025-07-12 16:49:37:4937"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT CONSTRAINT_NAME as constraint_name,CONSTRAINT_NAME as constraintName,CONSTRAINT_SCHEMA as constraintSchema,CONSTRAINT_SCHEMA as constraintCatalog,TABLE_NAME as tableName,TABLE_SCHEMA as tableSchema,TABLE_SCHEMA as tableCatalog,COLUMN_NAME as columnName,REFERENCED_TABLE_SCHEMA as referencedTableSchema,REFERENCED_TABLE_SCHEMA as referencedTableCatalog,REFERENCED_TABLE_NAME as referencedTableName,REFERENCED_COLUMN_NAME as referencedColumnName FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE where TABLE_NAME = 'users' AND CONSTRAINT_NAME!='PRIMARY' AND CONSTRAINT_SCHEMA='pdf' AND REFERENCED_TABLE_NAME IS NOT NULL;","timestamp":"2025-07-12 16:49:37:4937"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `email` `email` VARCHAR(255) NOT NULL UNIQUE;","timestamp":"2025-07-12 16:49:37:4937"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `password_hash` `password_hash` VARCHAR(255) NOT NULL;","timestamp":"2025-07-12 16:49:37:4937"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `first_name` `first_name` VARCHAR(100) NOT NULL;","timestamp":"2025-07-12 16:49:37:4937"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `last_name` `last_name` VARCHAR(100) NOT NULL;","timestamp":"2025-07-12 16:49:37:4937"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `subscription_plan` `subscription_plan` ENUM('free', 'premium', 'enterprise') NOT NULL DEFAULT 'free';","timestamp":"2025-07-12 16:49:37:4937"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `is_verified` `is_verified` TINYINT(1) NOT NULL DEFAULT false;","timestamp":"2025-07-12 16:49:38:4938"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `verification_token` `verification_token` VARCHAR(255);","timestamp":"2025-07-12 16:49:38:4938"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `reset_password_token` `reset_password_token` VARCHAR(255);","timestamp":"2025-07-12 16:49:39:4939"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `reset_password_expires` `reset_password_expires` DATETIME;","timestamp":"2025-07-12 16:49:40:4940"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `last_login_at` `last_login_at` DATETIME;","timestamp":"2025-07-12 16:49:40:4940"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `created_at` `created_at` DATETIME NOT NULL;","timestamp":"2025-07-12 16:49:40:4940"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `updated_at` `updated_at` DATETIME NOT NULL;","timestamp":"2025-07-12 16:49:40:4940"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SHOW INDEX FROM `users`","timestamp":"2025-07-12 16:49:40:4940"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT TABLE_NAME FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_TYPE = 'BASE TABLE' AND TABLE_NAME = 'processing_sessions' AND TABLE_SCHEMA = 'pdf'","timestamp":"2025-07-12 16:49:40:4940"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SHOW FULL COLUMNS FROM `processing_sessions`;","timestamp":"2025-07-12 16:49:40:4940"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT CONSTRAINT_NAME as constraint_name,CONSTRAINT_NAME as constraintName,CONSTRAINT_SCHEMA as constraintSchema,CONSTRAINT_SCHEMA as constraintCatalog,TABLE_NAME as tableName,TABLE_SCHEMA as tableSchema,TABLE_SCHEMA as tableCatalog,COLUMN_NAME as columnName,REFERENCED_TABLE_SCHEMA as referencedTableSchema,REFERENCED_TABLE_SCHEMA as referencedTableCatalog,REFERENCED_TABLE_NAME as referencedTableName,REFERENCED_COLUMN_NAME as referencedColumnName FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE where TABLE_NAME = 'processing_sessions' AND CONSTRAINT_NAME!='PRIMARY' AND CONSTRAINT_SCHEMA='pdf' AND REFERENCED_TABLE_NAME IS NOT NULL;","timestamp":"2025-07-12 16:49:40:4940"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT CONSTRAINT_CATALOG AS constraintCatalog, CONSTRAINT_NAME AS constraintName, CONSTRAINT_SCHEMA AS constraintSchema, CONSTRAINT_TYPE AS constraintType, TABLE_NAME AS tableName, TABLE_SCHEMA AS tableSchema from INFORMATION_SCHEMA.TABLE_CONSTRAINTS WHERE table_name='processing_sessions' AND constraint_name = 'processing_sessions_ibfk_1' AND TABLE_SCHEMA = 'pdf';","timestamp":"2025-07-12 16:49:40:4940"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `processing_sessions` DROP FOREIGN KEY `processing_sessions_ibfk_1`;","timestamp":"2025-07-12 16:49:40:4940"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `processing_sessions` ADD FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE ON UPDATE CASCADE;","timestamp":"2025-07-12 16:49:41:4941"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `processing_sessions` CHANGE `session_token` `session_token` VARCHAR(255) NOT NULL UNIQUE;","timestamp":"2025-07-12 16:49:45:4945"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `processing_sessions` CHANGE `status` `status` ENUM('active', 'completed', 'expired') NOT NULL DEFAULT 'active';","timestamp":"2025-07-12 16:49:45:4945"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `processing_sessions` CHANGE `files_processed` `files_processed` INTEGER NOT NULL DEFAULT 0;","timestamp":"2025-07-12 16:49:45:4945"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `processing_sessions` CHANGE `quota_consumed` `quota_consumed` INTEGER NOT NULL DEFAULT 0;","timestamp":"2025-07-12 16:49:45:4945"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `processing_sessions` CHANGE `can_download` `can_download` TINYINT(1) NOT NULL DEFAULT true;","timestamp":"2025-07-12 16:49:46:4946"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `processing_sessions` CHANGE `expires_at` `expires_at` DATETIME NOT NULL;","timestamp":"2025-07-12 16:49:46:4946"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `processing_sessions` CHANGE `created_at` `created_at` DATETIME NOT NULL;","timestamp":"2025-07-12 16:49:46:4946"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `processing_sessions` CHANGE `updated_at` `updated_at` DATETIME NOT NULL;","timestamp":"2025-07-12 16:49:46:4946"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SHOW INDEX FROM `processing_sessions`","timestamp":"2025-07-12 16:49:46:4946"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT TABLE_NAME FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_TYPE = 'BASE TABLE' AND TABLE_NAME = 'usage_records' AND TABLE_SCHEMA = 'pdf'","timestamp":"2025-07-12 16:49:46:4946"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SHOW FULL COLUMNS FROM `usage_records`;","timestamp":"2025-07-12 16:49:46:4946"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT CONSTRAINT_NAME as constraint_name,CONSTRAINT_NAME as constraintName,CONSTRAINT_SCHEMA as constraintSchema,CONSTRAINT_SCHEMA as constraintCatalog,TABLE_NAME as tableName,TABLE_SCHEMA as tableSchema,TABLE_SCHEMA as tableCatalog,COLUMN_NAME as columnName,REFERENCED_TABLE_SCHEMA as referencedTableSchema,REFERENCED_TABLE_SCHEMA as referencedTableCatalog,REFERENCED_TABLE_NAME as referencedTableName,REFERENCED_COLUMN_NAME as referencedColumnName FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE where TABLE_NAME = 'usage_records' AND CONSTRAINT_NAME!='PRIMARY' AND CONSTRAINT_SCHEMA='pdf' AND REFERENCED_TABLE_NAME IS NOT NULL;","timestamp":"2025-07-12 16:49:46:4946"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT CONSTRAINT_CATALOG AS constraintCatalog, CONSTRAINT_NAME AS constraintName, CONSTRAINT_SCHEMA AS constraintSchema, CONSTRAINT_TYPE AS constraintType, TABLE_NAME AS tableName, TABLE_SCHEMA AS tableSchema from INFORMATION_SCHEMA.TABLE_CONSTRAINTS WHERE table_name='usage_records' AND constraint_name = 'usage_records_ibfk_1' AND TABLE_SCHEMA = 'pdf';","timestamp":"2025-07-12 16:49:46:4946"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `usage_records` DROP FOREIGN KEY `usage_records_ibfk_1`;","timestamp":"2025-07-12 16:49:46:4946"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `usage_records` ADD FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE ON UPDATE CASCADE;","timestamp":"2025-07-12 16:49:46:4946"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `usage_records` CHANGE `tool_name` `tool_name` VARCHAR(100) NOT NULL;","timestamp":"2025-07-12 16:49:49:4949"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `usage_records` CHANGE `file_size_mb` `file_size_mb` DECIMAL(10,2) NOT NULL;","timestamp":"2025-07-12 16:49:49:4949"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `usage_records` CHANGE `processing_time_seconds` `processing_time_seconds` INTEGER NOT NULL;","timestamp":"2025-07-12 16:49:50:4950"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `usage_records` CHANGE `input_file_count` `input_file_count` INTEGER NOT NULL;","timestamp":"2025-07-12 16:49:50:4950"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `usage_records` CHANGE `output_file_count` `output_file_count` INTEGER NOT NULL DEFAULT 0;","timestamp":"2025-07-12 16:49:50:4950"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `usage_records` CHANGE `success` `success` TINYINT(1) NOT NULL DEFAULT true;","timestamp":"2025-07-12 16:49:50:4950"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `usage_records` CHANGE `error_message` `error_message` TEXT;","timestamp":"2025-07-12 16:49:50:4950"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `usage_records` CHANGE `parameters` `parameters` JSON;","timestamp":"2025-07-12 16:49:50:4950"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `usage_records` CHANGE `created_at` `created_at` DATETIME NOT NULL;","timestamp":"2025-07-12 16:49:50:4950"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `usage_records` CHANGE `updated_at` `updated_at` DATETIME NOT NULL;","timestamp":"2025-07-12 16:49:51:4951"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SHOW INDEX FROM `usage_records`","timestamp":"2025-07-12 16:49:51:4951"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mDatabase initialized successfully\u001b[39m","timestamp":"2025-07-12 16:49:51:4951"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mServer running on port 3001\u001b[39m","timestamp":"2025-07-12 16:49:51:4951"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mEnvironment: development\u001b[39m","timestamp":"2025-07-12 16:49:51:4951"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mDatabase connected and synchronized\u001b[39m","timestamp":"2025-07-12 16:49:51:4951"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT `id`, `user_id`, `tool_name`, `file_size_mb`, `processing_time_seconds`, `input_file_count`, `output_file_count`, `success`, `error_message`, `parameters`, `created_at` AS `createdAt`, `updated_at` AS `updatedAt` FROM `usage_records` AS `UsageRecord` WHERE `UsageRecord`.`user_id` = '60ef0f29-ca38-4a6a-86cb-256f9362aaa3' AND `UsageRecord`.`created_at` >= '2025-07-11 23:00:00';","timestamp":"2025-07-12 16:49:54:4954"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT count(*) AS `count` FROM `usage_records` AS `UsageRecord` WHERE `UsageRecord`.`user_id` = '60ef0f29-ca38-4a6a-86cb-256f9362aaa3' AND `UsageRecord`.`created_at` >= '2025-07-05 15:49:54';","timestamp":"2025-07-12 16:49:54:4954"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT `id`, `user_id`, `tool_name`, `file_size_mb`, `processing_time_seconds`, `input_file_count`, `output_file_count`, `success`, `error_message`, `parameters`, `created_at` AS `createdAt`, `updated_at` AS `updatedAt` FROM `usage_records` AS `UsageRecord` WHERE `UsageRecord`.`user_id` = '60ef0f29-ca38-4a6a-86cb-256f9362aaa3' AND `UsageRecord`.`created_at` >= '2025-07-05 15:49:54' ORDER BY `createdAt` DESC LIMIT 0, 50;","timestamp":"2025-07-12 16:49:54:4954"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT `id`, `email`, `password_hash`, `first_name`, `last_name`, `subscription_plan`, `is_verified`, `verification_token`, `reset_password_token`, `reset_password_expires`, `last_login_at`, `created_at` AS `createdAt`, `updated_at` AS `updatedAt` FROM `users` AS `User` WHERE `User`.`email` = '<EMAIL>';","timestamp":"2025-07-12 16:50:28:5028"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): INSERT INTO `users` (`id`,`email`,`password_hash`,`first_name`,`last_name`,`subscription_plan`,`is_verified`,`created_at`,`updated_at`) VALUES (?,?,?,?,?,?,?,?,?);","timestamp":"2025-07-12 16:50:28:5028"}
{"email":"<EMAIL>","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mUser registered successfully\u001b[39m","timestamp":"2025-07-12 16:50:28:5028","userId":"30f9b227-0d10-4ebc-87a6-4ff93482b115"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT `id`, `user_id`, `tool_name`, `file_size_mb`, `processing_time_seconds`, `input_file_count`, `output_file_count`, `success`, `error_message`, `parameters`, `created_at` AS `createdAt`, `updated_at` AS `updatedAt` FROM `usage_records` AS `UsageRecord` WHERE `UsageRecord`.`user_id` = '30f9b227-0d10-4ebc-87a6-4ff93482b115' AND `UsageRecord`.`created_at` >= '2025-07-11 23:00:00';","timestamp":"2025-07-12 16:50:28:5028"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT `id`, `user_id`, `tool_name`, `file_size_mb`, `processing_time_seconds`, `input_file_count`, `output_file_count`, `success`, `error_message`, `parameters`, `created_at` AS `createdAt`, `updated_at` AS `updatedAt` FROM `usage_records` AS `UsageRecord` WHERE `UsageRecord`.`user_id` = '30f9b227-0d10-4ebc-87a6-4ff93482b115' AND `UsageRecord`.`created_at` >= '2025-07-11 23:00:00';","timestamp":"2025-07-12 16:50:28:5028"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): INSERT INTO `processing_sessions` (`id`,`user_id`,`session_token`,`status`,`files_processed`,`quota_consumed`,`can_download`,`expires_at`,`created_at`,`updated_at`) VALUES (?,?,?,?,?,?,?,?,?,?);","timestamp":"2025-07-12 16:50:28:5028"}
{"error":"\"parameters\" must be of type object","ip":"::1","isOperational":true,"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRequest error\u001b[39m","method":"POST","stack":"ValidationError: \"parameters\" must be of type object\n    at G:\\PDF Porject\\backend\\src\\middleware\\validation.ts:117:13\n    at Layer.handle [as handle_request] (G:\\PDF Porject\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (G:\\PDF Porject\\backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at validateFiles (G:\\PDF Porject\\backend\\src\\middleware\\validation.ts:151:3)\n    at Layer.handle [as handle_request] (G:\\PDF Porject\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (G:\\PDF Porject\\backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at done (G:\\PDF Porject\\backend\\node_modules\\multer\\lib\\make-middleware.js:47:7)\n    at indicateDone (G:\\PDF Porject\\backend\\node_modules\\multer\\lib\\make-middleware.js:51:68)\n    at Multipart.<anonymous> (G:\\PDF Porject\\backend\\node_modules\\multer\\lib\\make-middleware.js:168:7)\n    at Multipart.emit (node:events:518:28)","statusCode":400,"timestamp":"2025-07-12 16:50:29:5029","url":"/api/pdf-tools/process","userAgent":"axios/1.10.0"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT `id`, `email`, `password_hash`, `first_name`, `last_name`, `subscription_plan`, `is_verified`, `verification_token`, `reset_password_token`, `reset_password_expires`, `last_login_at`, `created_at` AS `createdAt`, `updated_at` AS `updatedAt` FROM `users` AS `User` WHERE `User`.`email` = '<EMAIL>';","timestamp":"2025-07-12 16:50:29:5029"}
{"error":"Invalid email or password","ip":"::1","isOperational":true,"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRequest error\u001b[39m","method":"POST","stack":"AuthenticationError: Invalid email or password\n    at G:\\PDF Porject\\backend\\src\\routes\\auth.ts:87:13\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","statusCode":401,"timestamp":"2025-07-12 16:50:29:5029","url":"/api/auth/login","userAgent":"axios/1.10.0"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT 1+1 AS result","timestamp":"2025-07-12 16:50:46:5046"}
{"database":"pdf","host":"127.0.0.1","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mDatabase connection established successfully\u001b[39m","port":3306,"timestamp":"2025-07-12 16:50:46:5046"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT TABLE_NAME FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_TYPE = 'BASE TABLE' AND TABLE_NAME = 'users' AND TABLE_SCHEMA = 'pdf'","timestamp":"2025-07-12 16:50:46:5046"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SHOW FULL COLUMNS FROM `users`;","timestamp":"2025-07-12 16:50:46:5046"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT CONSTRAINT_NAME as constraint_name,CONSTRAINT_NAME as constraintName,CONSTRAINT_SCHEMA as constraintSchema,CONSTRAINT_SCHEMA as constraintCatalog,TABLE_NAME as tableName,TABLE_SCHEMA as tableSchema,TABLE_SCHEMA as tableCatalog,COLUMN_NAME as columnName,REFERENCED_TABLE_SCHEMA as referencedTableSchema,REFERENCED_TABLE_SCHEMA as referencedTableCatalog,REFERENCED_TABLE_NAME as referencedTableName,REFERENCED_COLUMN_NAME as referencedColumnName FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE where TABLE_NAME = 'users' AND CONSTRAINT_NAME!='PRIMARY' AND CONSTRAINT_SCHEMA='pdf' AND REFERENCED_TABLE_NAME IS NOT NULL;","timestamp":"2025-07-12 16:50:46:5046"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `email` `email` VARCHAR(255) NOT NULL UNIQUE;","timestamp":"2025-07-12 16:50:46:5046"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `password_hash` `password_hash` VARCHAR(255) NOT NULL;","timestamp":"2025-07-12 16:50:47:5047"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `first_name` `first_name` VARCHAR(100) NOT NULL;","timestamp":"2025-07-12 16:50:47:5047"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `last_name` `last_name` VARCHAR(100) NOT NULL;","timestamp":"2025-07-12 16:50:47:5047"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `subscription_plan` `subscription_plan` ENUM('free', 'premium', 'enterprise') NOT NULL DEFAULT 'free';","timestamp":"2025-07-12 16:50:47:5047"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `is_verified` `is_verified` TINYINT(1) NOT NULL DEFAULT false;","timestamp":"2025-07-12 16:50:47:5047"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `verification_token` `verification_token` VARCHAR(255);","timestamp":"2025-07-12 16:50:48:5048"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `reset_password_token` `reset_password_token` VARCHAR(255);","timestamp":"2025-07-12 16:50:48:5048"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `reset_password_expires` `reset_password_expires` DATETIME;","timestamp":"2025-07-12 16:50:48:5048"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `last_login_at` `last_login_at` DATETIME;","timestamp":"2025-07-12 16:50:48:5048"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `created_at` `created_at` DATETIME NOT NULL;","timestamp":"2025-07-12 16:50:48:5048"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `updated_at` `updated_at` DATETIME NOT NULL;","timestamp":"2025-07-12 16:50:48:5048"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SHOW INDEX FROM `users`","timestamp":"2025-07-12 16:50:49:5049"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT TABLE_NAME FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_TYPE = 'BASE TABLE' AND TABLE_NAME = 'processing_sessions' AND TABLE_SCHEMA = 'pdf'","timestamp":"2025-07-12 16:50:49:5049"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SHOW FULL COLUMNS FROM `processing_sessions`;","timestamp":"2025-07-12 16:50:49:5049"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT CONSTRAINT_NAME as constraint_name,CONSTRAINT_NAME as constraintName,CONSTRAINT_SCHEMA as constraintSchema,CONSTRAINT_SCHEMA as constraintCatalog,TABLE_NAME as tableName,TABLE_SCHEMA as tableSchema,TABLE_SCHEMA as tableCatalog,COLUMN_NAME as columnName,REFERENCED_TABLE_SCHEMA as referencedTableSchema,REFERENCED_TABLE_SCHEMA as referencedTableCatalog,REFERENCED_TABLE_NAME as referencedTableName,REFERENCED_COLUMN_NAME as referencedColumnName FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE where TABLE_NAME = 'processing_sessions' AND CONSTRAINT_NAME!='PRIMARY' AND CONSTRAINT_SCHEMA='pdf' AND REFERENCED_TABLE_NAME IS NOT NULL;","timestamp":"2025-07-12 16:50:49:5049"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT CONSTRAINT_CATALOG AS constraintCatalog, CONSTRAINT_NAME AS constraintName, CONSTRAINT_SCHEMA AS constraintSchema, CONSTRAINT_TYPE AS constraintType, TABLE_NAME AS tableName, TABLE_SCHEMA AS tableSchema from INFORMATION_SCHEMA.TABLE_CONSTRAINTS WHERE table_name='processing_sessions' AND constraint_name = 'processing_sessions_ibfk_1' AND TABLE_SCHEMA = 'pdf';","timestamp":"2025-07-12 16:50:49:5049"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `processing_sessions` DROP FOREIGN KEY `processing_sessions_ibfk_1`;","timestamp":"2025-07-12 16:50:49:5049"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `processing_sessions` ADD FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE ON UPDATE CASCADE;","timestamp":"2025-07-12 16:50:49:5049"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `processing_sessions` CHANGE `session_token` `session_token` VARCHAR(255) NOT NULL UNIQUE;","timestamp":"2025-07-12 16:50:53:5053"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `processing_sessions` CHANGE `status` `status` ENUM('active', 'completed', 'expired') NOT NULL DEFAULT 'active';","timestamp":"2025-07-12 16:50:53:5053"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `processing_sessions` CHANGE `files_processed` `files_processed` INTEGER NOT NULL DEFAULT 0;","timestamp":"2025-07-12 16:50:53:5053"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `processing_sessions` CHANGE `quota_consumed` `quota_consumed` INTEGER NOT NULL DEFAULT 0;","timestamp":"2025-07-12 16:50:53:5053"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `processing_sessions` CHANGE `can_download` `can_download` TINYINT(1) NOT NULL DEFAULT true;","timestamp":"2025-07-12 16:50:53:5053"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `processing_sessions` CHANGE `expires_at` `expires_at` DATETIME NOT NULL;","timestamp":"2025-07-12 16:50:54:5054"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `processing_sessions` CHANGE `created_at` `created_at` DATETIME NOT NULL;","timestamp":"2025-07-12 16:50:54:5054"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `processing_sessions` CHANGE `updated_at` `updated_at` DATETIME NOT NULL;","timestamp":"2025-07-12 16:50:54:5054"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SHOW INDEX FROM `processing_sessions`","timestamp":"2025-07-12 16:50:54:5054"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT TABLE_NAME FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_TYPE = 'BASE TABLE' AND TABLE_NAME = 'usage_records' AND TABLE_SCHEMA = 'pdf'","timestamp":"2025-07-12 16:50:54:5054"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SHOW FULL COLUMNS FROM `usage_records`;","timestamp":"2025-07-12 16:50:54:5054"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT CONSTRAINT_NAME as constraint_name,CONSTRAINT_NAME as constraintName,CONSTRAINT_SCHEMA as constraintSchema,CONSTRAINT_SCHEMA as constraintCatalog,TABLE_NAME as tableName,TABLE_SCHEMA as tableSchema,TABLE_SCHEMA as tableCatalog,COLUMN_NAME as columnName,REFERENCED_TABLE_SCHEMA as referencedTableSchema,REFERENCED_TABLE_SCHEMA as referencedTableCatalog,REFERENCED_TABLE_NAME as referencedTableName,REFERENCED_COLUMN_NAME as referencedColumnName FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE where TABLE_NAME = 'usage_records' AND CONSTRAINT_NAME!='PRIMARY' AND CONSTRAINT_SCHEMA='pdf' AND REFERENCED_TABLE_NAME IS NOT NULL;","timestamp":"2025-07-12 16:50:54:5054"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT CONSTRAINT_CATALOG AS constraintCatalog, CONSTRAINT_NAME AS constraintName, CONSTRAINT_SCHEMA AS constraintSchema, CONSTRAINT_TYPE AS constraintType, TABLE_NAME AS tableName, TABLE_SCHEMA AS tableSchema from INFORMATION_SCHEMA.TABLE_CONSTRAINTS WHERE table_name='usage_records' AND constraint_name = 'usage_records_ibfk_1' AND TABLE_SCHEMA = 'pdf';","timestamp":"2025-07-12 16:50:54:5054"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `usage_records` DROP FOREIGN KEY `usage_records_ibfk_1`;","timestamp":"2025-07-12 16:50:54:5054"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `usage_records` ADD FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE ON UPDATE CASCADE;","timestamp":"2025-07-12 16:50:54:5054"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `usage_records` CHANGE `tool_name` `tool_name` VARCHAR(100) NOT NULL;","timestamp":"2025-07-12 16:50:56:5056"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `usage_records` CHANGE `file_size_mb` `file_size_mb` DECIMAL(10,2) NOT NULL;","timestamp":"2025-07-12 16:50:57:5057"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `usage_records` CHANGE `processing_time_seconds` `processing_time_seconds` INTEGER NOT NULL;","timestamp":"2025-07-12 16:50:57:5057"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `usage_records` CHANGE `input_file_count` `input_file_count` INTEGER NOT NULL;","timestamp":"2025-07-12 16:50:57:5057"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `usage_records` CHANGE `output_file_count` `output_file_count` INTEGER NOT NULL DEFAULT 0;","timestamp":"2025-07-12 16:50:57:5057"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `usage_records` CHANGE `success` `success` TINYINT(1) NOT NULL DEFAULT true;","timestamp":"2025-07-12 16:50:57:5057"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `usage_records` CHANGE `error_message` `error_message` TEXT;","timestamp":"2025-07-12 16:50:57:5057"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `usage_records` CHANGE `parameters` `parameters` JSON;","timestamp":"2025-07-12 16:50:57:5057"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `usage_records` CHANGE `created_at` `created_at` DATETIME NOT NULL;","timestamp":"2025-07-12 16:50:58:5058"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `usage_records` CHANGE `updated_at` `updated_at` DATETIME NOT NULL;","timestamp":"2025-07-12 16:50:58:5058"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SHOW INDEX FROM `usage_records`","timestamp":"2025-07-12 16:50:58:5058"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mDatabase initialized successfully\u001b[39m","timestamp":"2025-07-12 16:50:58:5058"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mServer running on port 3001\u001b[39m","timestamp":"2025-07-12 16:50:58:5058"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mEnvironment: development\u001b[39m","timestamp":"2025-07-12 16:50:58:5058"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mDatabase connected and synchronized\u001b[39m","timestamp":"2025-07-12 16:50:58:5058"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT 1+1 AS result","timestamp":"2025-07-12 16:51:03:513"}
{"database":"pdf","host":"127.0.0.1","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mDatabase connection established successfully\u001b[39m","port":3306,"timestamp":"2025-07-12 16:51:03:513"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT TABLE_NAME FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_TYPE = 'BASE TABLE' AND TABLE_NAME = 'users' AND TABLE_SCHEMA = 'pdf'","timestamp":"2025-07-12 16:51:03:513"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SHOW FULL COLUMNS FROM `users`;","timestamp":"2025-07-12 16:51:03:513"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT CONSTRAINT_NAME as constraint_name,CONSTRAINT_NAME as constraintName,CONSTRAINT_SCHEMA as constraintSchema,CONSTRAINT_SCHEMA as constraintCatalog,TABLE_NAME as tableName,TABLE_SCHEMA as tableSchema,TABLE_SCHEMA as tableCatalog,COLUMN_NAME as columnName,REFERENCED_TABLE_SCHEMA as referencedTableSchema,REFERENCED_TABLE_SCHEMA as referencedTableCatalog,REFERENCED_TABLE_NAME as referencedTableName,REFERENCED_COLUMN_NAME as referencedColumnName FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE where TABLE_NAME = 'users' AND CONSTRAINT_NAME!='PRIMARY' AND CONSTRAINT_SCHEMA='pdf' AND REFERENCED_TABLE_NAME IS NOT NULL;","timestamp":"2025-07-12 16:51:03:513"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `email` `email` VARCHAR(255) NOT NULL UNIQUE;","timestamp":"2025-07-12 16:51:03:513"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `password_hash` `password_hash` VARCHAR(255) NOT NULL;","timestamp":"2025-07-12 16:51:04:514"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `first_name` `first_name` VARCHAR(100) NOT NULL;","timestamp":"2025-07-12 16:51:05:515"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `last_name` `last_name` VARCHAR(100) NOT NULL;","timestamp":"2025-07-12 16:51:05:515"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `subscription_plan` `subscription_plan` ENUM('free', 'premium', 'enterprise') NOT NULL DEFAULT 'free';","timestamp":"2025-07-12 16:51:05:515"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `is_verified` `is_verified` TINYINT(1) NOT NULL DEFAULT false;","timestamp":"2025-07-12 16:51:05:515"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `verification_token` `verification_token` VARCHAR(255);","timestamp":"2025-07-12 16:51:05:515"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `reset_password_token` `reset_password_token` VARCHAR(255);","timestamp":"2025-07-12 16:51:06:516"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `reset_password_expires` `reset_password_expires` DATETIME;","timestamp":"2025-07-12 16:51:06:516"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `last_login_at` `last_login_at` DATETIME;","timestamp":"2025-07-12 16:51:06:516"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `created_at` `created_at` DATETIME NOT NULL;","timestamp":"2025-07-12 16:51:06:516"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `updated_at` `updated_at` DATETIME NOT NULL;","timestamp":"2025-07-12 16:51:06:516"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SHOW INDEX FROM `users`","timestamp":"2025-07-12 16:51:06:516"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT TABLE_NAME FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_TYPE = 'BASE TABLE' AND TABLE_NAME = 'processing_sessions' AND TABLE_SCHEMA = 'pdf'","timestamp":"2025-07-12 16:51:06:516"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SHOW FULL COLUMNS FROM `processing_sessions`;","timestamp":"2025-07-12 16:51:06:516"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT CONSTRAINT_NAME as constraint_name,CONSTRAINT_NAME as constraintName,CONSTRAINT_SCHEMA as constraintSchema,CONSTRAINT_SCHEMA as constraintCatalog,TABLE_NAME as tableName,TABLE_SCHEMA as tableSchema,TABLE_SCHEMA as tableCatalog,COLUMN_NAME as columnName,REFERENCED_TABLE_SCHEMA as referencedTableSchema,REFERENCED_TABLE_SCHEMA as referencedTableCatalog,REFERENCED_TABLE_NAME as referencedTableName,REFERENCED_COLUMN_NAME as referencedColumnName FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE where TABLE_NAME = 'processing_sessions' AND CONSTRAINT_NAME!='PRIMARY' AND CONSTRAINT_SCHEMA='pdf' AND REFERENCED_TABLE_NAME IS NOT NULL;","timestamp":"2025-07-12 16:51:06:516"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT CONSTRAINT_CATALOG AS constraintCatalog, CONSTRAINT_NAME AS constraintName, CONSTRAINT_SCHEMA AS constraintSchema, CONSTRAINT_TYPE AS constraintType, TABLE_NAME AS tableName, TABLE_SCHEMA AS tableSchema from INFORMATION_SCHEMA.TABLE_CONSTRAINTS WHERE table_name='processing_sessions' AND constraint_name = 'processing_sessions_ibfk_1' AND TABLE_SCHEMA = 'pdf';","timestamp":"2025-07-12 16:51:06:516"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `processing_sessions` DROP FOREIGN KEY `processing_sessions_ibfk_1`;","timestamp":"2025-07-12 16:51:06:516"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `processing_sessions` ADD FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE ON UPDATE CASCADE;","timestamp":"2025-07-12 16:51:07:517"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `processing_sessions` CHANGE `session_token` `session_token` VARCHAR(255) NOT NULL UNIQUE;","timestamp":"2025-07-12 16:51:11:5111"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `processing_sessions` CHANGE `status` `status` ENUM('active', 'completed', 'expired') NOT NULL DEFAULT 'active';","timestamp":"2025-07-12 16:51:11:5111"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `processing_sessions` CHANGE `files_processed` `files_processed` INTEGER NOT NULL DEFAULT 0;","timestamp":"2025-07-12 16:51:11:5111"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `processing_sessions` CHANGE `quota_consumed` `quota_consumed` INTEGER NOT NULL DEFAULT 0;","timestamp":"2025-07-12 16:51:12:5112"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `processing_sessions` CHANGE `can_download` `can_download` TINYINT(1) NOT NULL DEFAULT true;","timestamp":"2025-07-12 16:51:12:5112"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `processing_sessions` CHANGE `expires_at` `expires_at` DATETIME NOT NULL;","timestamp":"2025-07-12 16:51:12:5112"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `processing_sessions` CHANGE `created_at` `created_at` DATETIME NOT NULL;","timestamp":"2025-07-12 16:51:12:5112"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `processing_sessions` CHANGE `updated_at` `updated_at` DATETIME NOT NULL;","timestamp":"2025-07-12 16:51:12:5112"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SHOW INDEX FROM `processing_sessions`","timestamp":"2025-07-12 16:51:12:5112"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT TABLE_NAME FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_TYPE = 'BASE TABLE' AND TABLE_NAME = 'usage_records' AND TABLE_SCHEMA = 'pdf'","timestamp":"2025-07-12 16:51:13:5113"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SHOW FULL COLUMNS FROM `usage_records`;","timestamp":"2025-07-12 16:51:13:5113"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT CONSTRAINT_NAME as constraint_name,CONSTRAINT_NAME as constraintName,CONSTRAINT_SCHEMA as constraintSchema,CONSTRAINT_SCHEMA as constraintCatalog,TABLE_NAME as tableName,TABLE_SCHEMA as tableSchema,TABLE_SCHEMA as tableCatalog,COLUMN_NAME as columnName,REFERENCED_TABLE_SCHEMA as referencedTableSchema,REFERENCED_TABLE_SCHEMA as referencedTableCatalog,REFERENCED_TABLE_NAME as referencedTableName,REFERENCED_COLUMN_NAME as referencedColumnName FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE where TABLE_NAME = 'usage_records' AND CONSTRAINT_NAME!='PRIMARY' AND CONSTRAINT_SCHEMA='pdf' AND REFERENCED_TABLE_NAME IS NOT NULL;","timestamp":"2025-07-12 16:51:13:5113"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT CONSTRAINT_CATALOG AS constraintCatalog, CONSTRAINT_NAME AS constraintName, CONSTRAINT_SCHEMA AS constraintSchema, CONSTRAINT_TYPE AS constraintType, TABLE_NAME AS tableName, TABLE_SCHEMA AS tableSchema from INFORMATION_SCHEMA.TABLE_CONSTRAINTS WHERE table_name='usage_records' AND constraint_name = 'usage_records_ibfk_1' AND TABLE_SCHEMA = 'pdf';","timestamp":"2025-07-12 16:51:13:5113"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `usage_records` DROP FOREIGN KEY `usage_records_ibfk_1`;","timestamp":"2025-07-12 16:51:13:5113"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `usage_records` ADD FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE ON UPDATE CASCADE;","timestamp":"2025-07-12 16:51:13:5113"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `usage_records` CHANGE `tool_name` `tool_name` VARCHAR(100) NOT NULL;","timestamp":"2025-07-12 16:51:15:5115"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `usage_records` CHANGE `file_size_mb` `file_size_mb` DECIMAL(10,2) NOT NULL;","timestamp":"2025-07-12 16:51:15:5115"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `usage_records` CHANGE `processing_time_seconds` `processing_time_seconds` INTEGER NOT NULL;","timestamp":"2025-07-12 16:51:15:5115"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `usage_records` CHANGE `input_file_count` `input_file_count` INTEGER NOT NULL;","timestamp":"2025-07-12 16:51:16:5116"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `usage_records` CHANGE `output_file_count` `output_file_count` INTEGER NOT NULL DEFAULT 0;","timestamp":"2025-07-12 16:51:16:5116"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `usage_records` CHANGE `success` `success` TINYINT(1) NOT NULL DEFAULT true;","timestamp":"2025-07-12 16:51:16:5116"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `usage_records` CHANGE `error_message` `error_message` TEXT;","timestamp":"2025-07-12 16:51:16:5116"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `usage_records` CHANGE `parameters` `parameters` JSON;","timestamp":"2025-07-12 16:51:16:5116"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `usage_records` CHANGE `created_at` `created_at` DATETIME NOT NULL;","timestamp":"2025-07-12 16:51:16:5116"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `usage_records` CHANGE `updated_at` `updated_at` DATETIME NOT NULL;","timestamp":"2025-07-12 16:51:17:5117"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SHOW INDEX FROM `usage_records`","timestamp":"2025-07-12 16:51:17:5117"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mDatabase initialized successfully\u001b[39m","timestamp":"2025-07-12 16:51:17:5117"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mServer running on port 3001\u001b[39m","timestamp":"2025-07-12 16:51:17:5117"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mEnvironment: development\u001b[39m","timestamp":"2025-07-12 16:51:17:5117"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mDatabase connected and synchronized\u001b[39m","timestamp":"2025-07-12 16:51:17:5117"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT 1+1 AS result","timestamp":"2025-07-12 16:51:35:5135"}
{"database":"pdf","host":"127.0.0.1","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mDatabase connection established successfully\u001b[39m","port":3306,"timestamp":"2025-07-12 16:51:35:5135"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT TABLE_NAME FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_TYPE = 'BASE TABLE' AND TABLE_NAME = 'users' AND TABLE_SCHEMA = 'pdf'","timestamp":"2025-07-12 16:51:35:5135"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SHOW FULL COLUMNS FROM `users`;","timestamp":"2025-07-12 16:51:35:5135"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT CONSTRAINT_NAME as constraint_name,CONSTRAINT_NAME as constraintName,CONSTRAINT_SCHEMA as constraintSchema,CONSTRAINT_SCHEMA as constraintCatalog,TABLE_NAME as tableName,TABLE_SCHEMA as tableSchema,TABLE_SCHEMA as tableCatalog,COLUMN_NAME as columnName,REFERENCED_TABLE_SCHEMA as referencedTableSchema,REFERENCED_TABLE_SCHEMA as referencedTableCatalog,REFERENCED_TABLE_NAME as referencedTableName,REFERENCED_COLUMN_NAME as referencedColumnName FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE where TABLE_NAME = 'users' AND CONSTRAINT_NAME!='PRIMARY' AND CONSTRAINT_SCHEMA='pdf' AND REFERENCED_TABLE_NAME IS NOT NULL;","timestamp":"2025-07-12 16:51:35:5135"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `email` `email` VARCHAR(255) NOT NULL UNIQUE;","timestamp":"2025-07-12 16:51:35:5135"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `password_hash` `password_hash` VARCHAR(255) NOT NULL;","timestamp":"2025-07-12 16:51:36:5136"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `first_name` `first_name` VARCHAR(100) NOT NULL;","timestamp":"2025-07-12 16:51:36:5136"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `last_name` `last_name` VARCHAR(100) NOT NULL;","timestamp":"2025-07-12 16:51:36:5136"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `subscription_plan` `subscription_plan` ENUM('free', 'premium', 'enterprise') NOT NULL DEFAULT 'free';","timestamp":"2025-07-12 16:51:37:5137"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `is_verified` `is_verified` TINYINT(1) NOT NULL DEFAULT false;","timestamp":"2025-07-12 16:51:38:5138"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `verification_token` `verification_token` VARCHAR(255);","timestamp":"2025-07-12 16:51:38:5138"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `reset_password_token` `reset_password_token` VARCHAR(255);","timestamp":"2025-07-12 16:51:38:5138"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `reset_password_expires` `reset_password_expires` DATETIME;","timestamp":"2025-07-12 16:51:39:5139"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `last_login_at` `last_login_at` DATETIME;","timestamp":"2025-07-12 16:51:39:5139"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `created_at` `created_at` DATETIME NOT NULL;","timestamp":"2025-07-12 16:51:40:5140"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `updated_at` `updated_at` DATETIME NOT NULL;","timestamp":"2025-07-12 16:51:40:5140"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SHOW INDEX FROM `users`","timestamp":"2025-07-12 16:51:40:5140"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT TABLE_NAME FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_TYPE = 'BASE TABLE' AND TABLE_NAME = 'processing_sessions' AND TABLE_SCHEMA = 'pdf'","timestamp":"2025-07-12 16:51:40:5140"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SHOW FULL COLUMNS FROM `processing_sessions`;","timestamp":"2025-07-12 16:51:40:5140"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT CONSTRAINT_NAME as constraint_name,CONSTRAINT_NAME as constraintName,CONSTRAINT_SCHEMA as constraintSchema,CONSTRAINT_SCHEMA as constraintCatalog,TABLE_NAME as tableName,TABLE_SCHEMA as tableSchema,TABLE_SCHEMA as tableCatalog,COLUMN_NAME as columnName,REFERENCED_TABLE_SCHEMA as referencedTableSchema,REFERENCED_TABLE_SCHEMA as referencedTableCatalog,REFERENCED_TABLE_NAME as referencedTableName,REFERENCED_COLUMN_NAME as referencedColumnName FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE where TABLE_NAME = 'processing_sessions' AND CONSTRAINT_NAME!='PRIMARY' AND CONSTRAINT_SCHEMA='pdf' AND REFERENCED_TABLE_NAME IS NOT NULL;","timestamp":"2025-07-12 16:51:40:5140"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT CONSTRAINT_CATALOG AS constraintCatalog, CONSTRAINT_NAME AS constraintName, CONSTRAINT_SCHEMA AS constraintSchema, CONSTRAINT_TYPE AS constraintType, TABLE_NAME AS tableName, TABLE_SCHEMA AS tableSchema from INFORMATION_SCHEMA.TABLE_CONSTRAINTS WHERE table_name='processing_sessions' AND constraint_name = 'processing_sessions_ibfk_1' AND TABLE_SCHEMA = 'pdf';","timestamp":"2025-07-12 16:51:40:5140"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `processing_sessions` DROP FOREIGN KEY `processing_sessions_ibfk_1`;","timestamp":"2025-07-12 16:51:40:5140"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `processing_sessions` ADD FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE ON UPDATE CASCADE;","timestamp":"2025-07-12 16:51:40:5140"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `processing_sessions` CHANGE `session_token` `session_token` VARCHAR(255) NOT NULL UNIQUE;","timestamp":"2025-07-12 16:51:45:5145"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `processing_sessions` CHANGE `status` `status` ENUM('active', 'completed', 'expired') NOT NULL DEFAULT 'active';","timestamp":"2025-07-12 16:51:45:5145"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `processing_sessions` CHANGE `files_processed` `files_processed` INTEGER NOT NULL DEFAULT 0;","timestamp":"2025-07-12 16:51:45:5145"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `processing_sessions` CHANGE `quota_consumed` `quota_consumed` INTEGER NOT NULL DEFAULT 0;","timestamp":"2025-07-12 16:51:45:5145"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `processing_sessions` CHANGE `can_download` `can_download` TINYINT(1) NOT NULL DEFAULT true;","timestamp":"2025-07-12 16:51:46:5146"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `processing_sessions` CHANGE `expires_at` `expires_at` DATETIME NOT NULL;","timestamp":"2025-07-12 16:51:46:5146"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `processing_sessions` CHANGE `created_at` `created_at` DATETIME NOT NULL;","timestamp":"2025-07-12 16:51:46:5146"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `processing_sessions` CHANGE `updated_at` `updated_at` DATETIME NOT NULL;","timestamp":"2025-07-12 16:51:46:5146"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SHOW INDEX FROM `processing_sessions`","timestamp":"2025-07-12 16:51:46:5146"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT TABLE_NAME FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_TYPE = 'BASE TABLE' AND TABLE_NAME = 'usage_records' AND TABLE_SCHEMA = 'pdf'","timestamp":"2025-07-12 16:51:46:5146"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SHOW FULL COLUMNS FROM `usage_records`;","timestamp":"2025-07-12 16:51:46:5146"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT CONSTRAINT_NAME as constraint_name,CONSTRAINT_NAME as constraintName,CONSTRAINT_SCHEMA as constraintSchema,CONSTRAINT_SCHEMA as constraintCatalog,TABLE_NAME as tableName,TABLE_SCHEMA as tableSchema,TABLE_SCHEMA as tableCatalog,COLUMN_NAME as columnName,REFERENCED_TABLE_SCHEMA as referencedTableSchema,REFERENCED_TABLE_SCHEMA as referencedTableCatalog,REFERENCED_TABLE_NAME as referencedTableName,REFERENCED_COLUMN_NAME as referencedColumnName FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE where TABLE_NAME = 'usage_records' AND CONSTRAINT_NAME!='PRIMARY' AND CONSTRAINT_SCHEMA='pdf' AND REFERENCED_TABLE_NAME IS NOT NULL;","timestamp":"2025-07-12 16:51:46:5146"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT CONSTRAINT_CATALOG AS constraintCatalog, CONSTRAINT_NAME AS constraintName, CONSTRAINT_SCHEMA AS constraintSchema, CONSTRAINT_TYPE AS constraintType, TABLE_NAME AS tableName, TABLE_SCHEMA AS tableSchema from INFORMATION_SCHEMA.TABLE_CONSTRAINTS WHERE table_name='usage_records' AND constraint_name = 'usage_records_ibfk_1' AND TABLE_SCHEMA = 'pdf';","timestamp":"2025-07-12 16:51:46:5146"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `usage_records` DROP FOREIGN KEY `usage_records_ibfk_1`;","timestamp":"2025-07-12 16:51:46:5146"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `usage_records` ADD FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE ON UPDATE CASCADE;","timestamp":"2025-07-12 16:51:47:5147"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `usage_records` CHANGE `tool_name` `tool_name` VARCHAR(100) NOT NULL;","timestamp":"2025-07-12 16:51:49:5149"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `usage_records` CHANGE `file_size_mb` `file_size_mb` DECIMAL(10,2) NOT NULL;","timestamp":"2025-07-12 16:51:49:5149"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `usage_records` CHANGE `processing_time_seconds` `processing_time_seconds` INTEGER NOT NULL;","timestamp":"2025-07-12 16:51:49:5149"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `usage_records` CHANGE `input_file_count` `input_file_count` INTEGER NOT NULL;","timestamp":"2025-07-12 16:51:50:5150"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `usage_records` CHANGE `output_file_count` `output_file_count` INTEGER NOT NULL DEFAULT 0;","timestamp":"2025-07-12 16:51:50:5150"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `usage_records` CHANGE `success` `success` TINYINT(1) NOT NULL DEFAULT true;","timestamp":"2025-07-12 16:51:50:5150"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `usage_records` CHANGE `error_message` `error_message` TEXT;","timestamp":"2025-07-12 16:51:50:5150"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `usage_records` CHANGE `parameters` `parameters` JSON;","timestamp":"2025-07-12 16:51:50:5150"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `usage_records` CHANGE `created_at` `created_at` DATETIME NOT NULL;","timestamp":"2025-07-12 16:51:50:5150"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `usage_records` CHANGE `updated_at` `updated_at` DATETIME NOT NULL;","timestamp":"2025-07-12 16:51:51:5151"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SHOW INDEX FROM `usage_records`","timestamp":"2025-07-12 16:51:51:5151"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mDatabase initialized successfully\u001b[39m","timestamp":"2025-07-12 16:51:51:5151"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT `id`, `email`, `password_hash`, `first_name`, `last_name`, `subscription_plan`, `is_verified`, `verification_token`, `reset_password_token`, `reset_password_expires`, `last_login_at`, `created_at` AS `createdAt`, `updated_at` AS `updatedAt` FROM `users` AS `User` WHERE `User`.`email` = '<EMAIL>';","timestamp":"2025-07-12 16:52:09:529"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): INSERT INTO `users` (`id`,`email`,`password_hash`,`first_name`,`last_name`,`subscription_plan`,`is_verified`,`created_at`,`updated_at`) VALUES (?,?,?,?,?,?,?,?,?);","timestamp":"2025-07-12 16:52:09:529"}
{"email":"<EMAIL>","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mUser registered successfully\u001b[39m","timestamp":"2025-07-12 16:52:09:529","userId":"3b971185-b4f9-4106-9c1c-5fbe87724649"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT `id`, `user_id`, `tool_name`, `file_size_mb`, `processing_time_seconds`, `input_file_count`, `output_file_count`, `success`, `error_message`, `parameters`, `created_at` AS `createdAt`, `updated_at` AS `updatedAt` FROM `usage_records` AS `UsageRecord` WHERE `UsageRecord`.`user_id` = '3b971185-b4f9-4106-9c1c-5fbe87724649' AND `UsageRecord`.`created_at` >= '2025-07-11 23:00:00';","timestamp":"2025-07-12 16:52:09:529"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT `id`, `user_id`, `tool_name`, `file_size_mb`, `processing_time_seconds`, `input_file_count`, `output_file_count`, `success`, `error_message`, `parameters`, `created_at` AS `createdAt`, `updated_at` AS `updatedAt` FROM `usage_records` AS `UsageRecord` WHERE `UsageRecord`.`user_id` = '3b971185-b4f9-4106-9c1c-5fbe87724649' AND `UsageRecord`.`created_at` >= '2025-07-11 23:00:00';","timestamp":"2025-07-12 16:52:09:529"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): INSERT INTO `processing_sessions` (`id`,`user_id`,`session_token`,`status`,`files_processed`,`quota_consumed`,`can_download`,`expires_at`,`created_at`,`updated_at`) VALUES (?,?,?,?,?,?,?,?,?,?);","timestamp":"2025-07-12 16:52:09:529"}
{"fileCount":1,"jobId":"80e34ba9-24c2-4a1c-9347-f767aaf83e32","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mStarting PDF tool processing\u001b[39m","parameters":{},"sessionToken":"b81f1ddd-924d-412a-804f-1065a7d8d5e4","timestamp":"2025-07-12 16:52:10:5210","toolName":"repair_pdf"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mFile saved\u001b[39m","originalName":"test.pdf","savedPath":"G:\\PDF Porject\\backend\\uploads\\2729e59c-baa2-47de-b9d8-61c7382a13c5.pdf","size":328,"timestamp":"2025-07-12 16:52:10:5210"}
{"inputFiles":1,"jobId":"7716d455-06c9-42cd-ad93-8d49d4baf0ec","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mStarting Python tool execution\u001b[39m","parameters":{},"timestamp":"2025-07-12 16:52:10:5210","toolName":"repair_pdf"}
{"error":"Python script failed with code 1: Traceback (most recent call last):\r\n  File \"G:\\PDF Porject\\tools\\repair_pdf_wrapper.py\", line 15, in <module>\r\n    from repair_pdf import repair_pdf_tool\r\n  File \"G:\\PDF Porject\\tools\\repair_pdf.py\", line 11, in <module>\r\n    from .base_tool import BasePDFTool, ProcessingError, ValidationError\r\nImportError: attempted relative import with no known parent package\r\n","jobId":"7716d455-06c9-42cd-ad93-8d49d4baf0ec","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mPython tool execution failed\u001b[39m","processingTime":1203,"timestamp":"2025-07-12 16:52:11:5211","toolName":"repair_pdf"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mProcessing recorded in session\u001b[39m","sessionToken":"b81f1ddd-924d-412a-804f-1065a7d8d5e4","success":false,"timestamp":"2025-07-12 16:52:11:5211","toolName":"repair_pdf","userId":"guest"}
{"filePath":"G:\\PDF Porject\\backend\\uploads\\2729e59c-baa2-47de-b9d8-61c7382a13c5.pdf","level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mFile cleaned up\u001b[39m","timestamp":"2025-07-12 16:52:11:5211"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT `id`, `email`, `password_hash`, `first_name`, `last_name`, `subscription_plan`, `is_verified`, `verification_token`, `reset_password_token`, `reset_password_expires`, `last_login_at`, `created_at` AS `createdAt`, `updated_at` AS `updatedAt` FROM `users` AS `User` WHERE `User`.`email` = '<EMAIL>';","timestamp":"2025-07-12 16:52:11:5211"}
{"error":"Invalid email or password","ip":"::1","isOperational":true,"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRequest error\u001b[39m","method":"POST","stack":"AuthenticationError: Invalid email or password\n    at G:\\PDF Porject\\backend\\src\\routes\\auth.ts:87:13\n    at processTicksAndRejections (node:internal/process/task_queues:95:5)","statusCode":401,"timestamp":"2025-07-12 16:52:11:5211","url":"/api/auth/login","userAgent":"axios/1.10.0"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT `id`, `email`, `password_hash`, `first_name`, `last_name`, `subscription_plan`, `is_verified`, `verification_token`, `reset_password_token`, `reset_password_expires`, `last_login_at`, `created_at` AS `createdAt`, `updated_at` AS `updatedAt` FROM `users` AS `User` WHERE `User`.`email` = '<EMAIL>';","timestamp":"2025-07-12 16:52:52:5252"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): INSERT INTO `users` (`id`,`email`,`password_hash`,`first_name`,`last_name`,`subscription_plan`,`is_verified`,`created_at`,`updated_at`) VALUES (?,?,?,?,?,?,?,?,?);","timestamp":"2025-07-12 16:52:52:5252"}
{"email":"<EMAIL>","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mUser registered successfully\u001b[39m","timestamp":"2025-07-12 16:52:52:5252","userId":"cc85a08f-5c3f-4e65-bc45-13d10aafee30"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT `id`, `user_id`, `tool_name`, `file_size_mb`, `processing_time_seconds`, `input_file_count`, `output_file_count`, `success`, `error_message`, `parameters`, `created_at` AS `createdAt`, `updated_at` AS `updatedAt` FROM `usage_records` AS `UsageRecord` WHERE `UsageRecord`.`user_id` = 'cc85a08f-5c3f-4e65-bc45-13d10aafee30' AND `UsageRecord`.`created_at` >= '2025-07-11 23:00:00';","timestamp":"2025-07-12 16:52:52:5252"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT `id`, `user_id`, `tool_name`, `file_size_mb`, `processing_time_seconds`, `input_file_count`, `output_file_count`, `success`, `error_message`, `parameters`, `created_at` AS `createdAt`, `updated_at` AS `updatedAt` FROM `usage_records` AS `UsageRecord` WHERE `UsageRecord`.`user_id` = 'cc85a08f-5c3f-4e65-bc45-13d10aafee30' AND `UsageRecord`.`created_at` >= '2025-07-11 23:00:00';","timestamp":"2025-07-12 16:52:52:5252"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): INSERT INTO `processing_sessions` (`id`,`user_id`,`session_token`,`status`,`files_processed`,`quota_consumed`,`can_download`,`expires_at`,`created_at`,`updated_at`) VALUES (?,?,?,?,?,?,?,?,?,?);","timestamp":"2025-07-12 16:52:52:5252"}
{"error":"Authorization header is required","ip":"::1","isOperational":true,"level":"\u001b[31merror\u001b[39m","message":"\u001b[31mRequest error\u001b[39m","method":"POST","stack":"AuthenticationError: Authorization header is required\n    at Function.extractTokenFromHeader (G:\\PDF Porject\\backend\\src\\middleware\\auth.ts:67:13)\n    at authenticate (G:\\PDF Porject\\backend\\src\\middleware\\auth.ts:82:31)\n    at Layer.handle [as handle_request] (G:\\PDF Porject\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at next (G:\\PDF Porject\\backend\\node_modules\\express\\lib\\router\\route.js:149:13)\n    at Route.dispatch (G:\\PDF Porject\\backend\\node_modules\\express\\lib\\router\\route.js:119:3)\n    at Layer.handle [as handle_request] (G:\\PDF Porject\\backend\\node_modules\\express\\lib\\router\\layer.js:95:5)\n    at G:\\PDF Porject\\backend\\node_modules\\express\\lib\\router\\index.js:284:15\n    at Function.process_params (G:\\PDF Porject\\backend\\node_modules\\express\\lib\\router\\index.js:346:12)\n    at next (G:\\PDF Porject\\backend\\node_modules\\express\\lib\\router\\index.js:280:10)\n    at Function.handle (G:\\PDF Porject\\backend\\node_modules\\express\\lib\\router\\index.js:175:3)","statusCode":401,"timestamp":"2025-07-12 16:52:53:5253","url":"/api/pdf-tools/complete-session","userAgent":"axios/1.10.0"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT 1+1 AS result","timestamp":"2025-07-12 16:53:12:5312"}
{"database":"pdf","host":"127.0.0.1","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mDatabase connection established successfully\u001b[39m","port":3306,"timestamp":"2025-07-12 16:53:12:5312"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT TABLE_NAME FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_TYPE = 'BASE TABLE' AND TABLE_NAME = 'users' AND TABLE_SCHEMA = 'pdf'","timestamp":"2025-07-12 16:53:12:5312"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SHOW FULL COLUMNS FROM `users`;","timestamp":"2025-07-12 16:53:12:5312"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT CONSTRAINT_NAME as constraint_name,CONSTRAINT_NAME as constraintName,CONSTRAINT_SCHEMA as constraintSchema,CONSTRAINT_SCHEMA as constraintCatalog,TABLE_NAME as tableName,TABLE_SCHEMA as tableSchema,TABLE_SCHEMA as tableCatalog,COLUMN_NAME as columnName,REFERENCED_TABLE_SCHEMA as referencedTableSchema,REFERENCED_TABLE_SCHEMA as referencedTableCatalog,REFERENCED_TABLE_NAME as referencedTableName,REFERENCED_COLUMN_NAME as referencedColumnName FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE where TABLE_NAME = 'users' AND CONSTRAINT_NAME!='PRIMARY' AND CONSTRAINT_SCHEMA='pdf' AND REFERENCED_TABLE_NAME IS NOT NULL;","timestamp":"2025-07-12 16:53:12:5312"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `email` `email` VARCHAR(255) NOT NULL UNIQUE;","timestamp":"2025-07-12 16:53:12:5312"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT 1+1 AS result","timestamp":"2025-07-12 16:53:12:5312"}
{"database":"pdf","host":"127.0.0.1","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mDatabase connection established successfully\u001b[39m","port":3306,"timestamp":"2025-07-12 16:53:12:5312"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT TABLE_NAME FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_TYPE = 'BASE TABLE' AND TABLE_NAME = 'users' AND TABLE_SCHEMA = 'pdf'","timestamp":"2025-07-12 16:53:12:5312"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SHOW FULL COLUMNS FROM `users`;","timestamp":"2025-07-12 16:53:12:5312"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT CONSTRAINT_NAME as constraint_name,CONSTRAINT_NAME as constraintName,CONSTRAINT_SCHEMA as constraintSchema,CONSTRAINT_SCHEMA as constraintCatalog,TABLE_NAME as tableName,TABLE_SCHEMA as tableSchema,TABLE_SCHEMA as tableCatalog,COLUMN_NAME as columnName,REFERENCED_TABLE_SCHEMA as referencedTableSchema,REFERENCED_TABLE_SCHEMA as referencedTableCatalog,REFERENCED_TABLE_NAME as referencedTableName,REFERENCED_COLUMN_NAME as referencedColumnName FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE where TABLE_NAME = 'users' AND CONSTRAINT_NAME!='PRIMARY' AND CONSTRAINT_SCHEMA='pdf' AND REFERENCED_TABLE_NAME IS NOT NULL;","timestamp":"2025-07-12 16:53:12:5312"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `email` `email` VARCHAR(255) NOT NULL UNIQUE;","timestamp":"2025-07-12 16:53:12:5312"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `password_hash` `password_hash` VARCHAR(255) NOT NULL;","timestamp":"2025-07-12 16:53:13:5313"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `password_hash` `password_hash` VARCHAR(255) NOT NULL;","timestamp":"2025-07-12 16:53:14:5314"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `first_name` `first_name` VARCHAR(100) NOT NULL;","timestamp":"2025-07-12 16:53:14:5314"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `first_name` `first_name` VARCHAR(100) NOT NULL;","timestamp":"2025-07-12 16:53:15:5315"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `last_name` `last_name` VARCHAR(100) NOT NULL;","timestamp":"2025-07-12 16:53:15:5315"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `last_name` `last_name` VARCHAR(100) NOT NULL;","timestamp":"2025-07-12 16:53:15:5315"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `subscription_plan` `subscription_plan` ENUM('free', 'premium', 'enterprise') NOT NULL DEFAULT 'free';","timestamp":"2025-07-12 16:53:16:5316"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `subscription_plan` `subscription_plan` ENUM('free', 'premium', 'enterprise') NOT NULL DEFAULT 'free';","timestamp":"2025-07-12 16:53:16:5316"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `is_verified` `is_verified` TINYINT(1) NOT NULL DEFAULT false;","timestamp":"2025-07-12 16:53:16:5316"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `is_verified` `is_verified` TINYINT(1) NOT NULL DEFAULT false;","timestamp":"2025-07-12 16:53:16:5316"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `verification_token` `verification_token` VARCHAR(255);","timestamp":"2025-07-12 16:53:16:5316"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `verification_token` `verification_token` VARCHAR(255);","timestamp":"2025-07-12 16:53:16:5316"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `reset_password_token` `reset_password_token` VARCHAR(255);","timestamp":"2025-07-12 16:53:17:5317"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `reset_password_token` `reset_password_token` VARCHAR(255);","timestamp":"2025-07-12 16:53:17:5317"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `reset_password_expires` `reset_password_expires` DATETIME;","timestamp":"2025-07-12 16:53:17:5317"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `reset_password_expires` `reset_password_expires` DATETIME;","timestamp":"2025-07-12 16:53:17:5317"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `last_login_at` `last_login_at` DATETIME;","timestamp":"2025-07-12 16:53:17:5317"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `last_login_at` `last_login_at` DATETIME;","timestamp":"2025-07-12 16:53:17:5317"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `created_at` `created_at` DATETIME NOT NULL;","timestamp":"2025-07-12 16:53:18:5318"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `created_at` `created_at` DATETIME NOT NULL;","timestamp":"2025-07-12 16:53:18:5318"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `updated_at` `updated_at` DATETIME NOT NULL;","timestamp":"2025-07-12 16:53:18:5318"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `updated_at` `updated_at` DATETIME NOT NULL;","timestamp":"2025-07-12 16:53:18:5318"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SHOW INDEX FROM `users`","timestamp":"2025-07-12 16:53:19:5319"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT TABLE_NAME FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_TYPE = 'BASE TABLE' AND TABLE_NAME = 'processing_sessions' AND TABLE_SCHEMA = 'pdf'","timestamp":"2025-07-12 16:53:19:5319"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SHOW FULL COLUMNS FROM `processing_sessions`;","timestamp":"2025-07-12 16:53:19:5319"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT CONSTRAINT_NAME as constraint_name,CONSTRAINT_NAME as constraintName,CONSTRAINT_SCHEMA as constraintSchema,CONSTRAINT_SCHEMA as constraintCatalog,TABLE_NAME as tableName,TABLE_SCHEMA as tableSchema,TABLE_SCHEMA as tableCatalog,COLUMN_NAME as columnName,REFERENCED_TABLE_SCHEMA as referencedTableSchema,REFERENCED_TABLE_SCHEMA as referencedTableCatalog,REFERENCED_TABLE_NAME as referencedTableName,REFERENCED_COLUMN_NAME as referencedColumnName FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE where TABLE_NAME = 'processing_sessions' AND CONSTRAINT_NAME!='PRIMARY' AND CONSTRAINT_SCHEMA='pdf' AND REFERENCED_TABLE_NAME IS NOT NULL;","timestamp":"2025-07-12 16:53:19:5319"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SHOW INDEX FROM `users`","timestamp":"2025-07-12 16:53:19:5319"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT CONSTRAINT_CATALOG AS constraintCatalog, CONSTRAINT_NAME AS constraintName, CONSTRAINT_SCHEMA AS constraintSchema, CONSTRAINT_TYPE AS constraintType, TABLE_NAME AS tableName, TABLE_SCHEMA AS tableSchema from INFORMATION_SCHEMA.TABLE_CONSTRAINTS WHERE table_name='processing_sessions' AND constraint_name = 'processing_sessions_ibfk_1' AND TABLE_SCHEMA = 'pdf';","timestamp":"2025-07-12 16:53:19:5319"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT TABLE_NAME FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_TYPE = 'BASE TABLE' AND TABLE_NAME = 'processing_sessions' AND TABLE_SCHEMA = 'pdf'","timestamp":"2025-07-12 16:53:19:5319"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `processing_sessions` DROP FOREIGN KEY `processing_sessions_ibfk_1`;","timestamp":"2025-07-12 16:53:19:5319"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SHOW FULL COLUMNS FROM `processing_sessions`;","timestamp":"2025-07-12 16:53:19:5319"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT CONSTRAINT_NAME as constraint_name,CONSTRAINT_NAME as constraintName,CONSTRAINT_SCHEMA as constraintSchema,CONSTRAINT_SCHEMA as constraintCatalog,TABLE_NAME as tableName,TABLE_SCHEMA as tableSchema,TABLE_SCHEMA as tableCatalog,COLUMN_NAME as columnName,REFERENCED_TABLE_SCHEMA as referencedTableSchema,REFERENCED_TABLE_SCHEMA as referencedTableCatalog,REFERENCED_TABLE_NAME as referencedTableName,REFERENCED_COLUMN_NAME as referencedColumnName FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE where TABLE_NAME = 'processing_sessions' AND CONSTRAINT_NAME!='PRIMARY' AND CONSTRAINT_SCHEMA='pdf' AND REFERENCED_TABLE_NAME IS NOT NULL;","timestamp":"2025-07-12 16:53:19:5319"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT CONSTRAINT_CATALOG AS constraintCatalog, CONSTRAINT_NAME AS constraintName, CONSTRAINT_SCHEMA AS constraintSchema, CONSTRAINT_TYPE AS constraintType, TABLE_NAME AS tableName, TABLE_SCHEMA AS tableSchema from INFORMATION_SCHEMA.TABLE_CONSTRAINTS WHERE table_name='processing_sessions' AND constraint_name = 'processing_sessions_ibfk_1' AND TABLE_SCHEMA = 'pdf';","timestamp":"2025-07-12 16:53:19:5319"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `processing_sessions` DROP FOREIGN KEY `processing_sessions_ibfk_1`;","timestamp":"2025-07-12 16:53:19:5319"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `processing_sessions` ADD FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE ON UPDATE CASCADE;","timestamp":"2025-07-12 16:53:19:5319"}
{"error":"Can't DROP 'processing_sessions_ibfk_1'; check that column/key exists","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mFailed to initialize database\u001b[39m","timestamp":"2025-07-12 16:53:19:5319"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `processing_sessions` CHANGE `session_token` `session_token` VARCHAR(255) NOT NULL UNIQUE;","timestamp":"2025-07-12 16:53:23:5323"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `processing_sessions` CHANGE `status` `status` ENUM('active', 'completed', 'expired') NOT NULL DEFAULT 'active';","timestamp":"2025-07-12 16:53:24:5324"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `processing_sessions` CHANGE `files_processed` `files_processed` INTEGER NOT NULL DEFAULT 0;","timestamp":"2025-07-12 16:53:24:5324"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `processing_sessions` CHANGE `quota_consumed` `quota_consumed` INTEGER NOT NULL DEFAULT 0;","timestamp":"2025-07-12 16:53:24:5324"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `processing_sessions` CHANGE `can_download` `can_download` TINYINT(1) NOT NULL DEFAULT true;","timestamp":"2025-07-12 16:53:24:5324"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `processing_sessions` CHANGE `expires_at` `expires_at` DATETIME NOT NULL;","timestamp":"2025-07-12 16:53:24:5324"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `processing_sessions` CHANGE `created_at` `created_at` DATETIME NOT NULL;","timestamp":"2025-07-12 16:53:24:5324"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `processing_sessions` CHANGE `updated_at` `updated_at` DATETIME NOT NULL;","timestamp":"2025-07-12 16:53:25:5325"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SHOW INDEX FROM `processing_sessions`","timestamp":"2025-07-12 16:53:25:5325"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT TABLE_NAME FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_TYPE = 'BASE TABLE' AND TABLE_NAME = 'usage_records' AND TABLE_SCHEMA = 'pdf'","timestamp":"2025-07-12 16:53:25:5325"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SHOW FULL COLUMNS FROM `usage_records`;","timestamp":"2025-07-12 16:53:25:5325"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT CONSTRAINT_NAME as constraint_name,CONSTRAINT_NAME as constraintName,CONSTRAINT_SCHEMA as constraintSchema,CONSTRAINT_SCHEMA as constraintCatalog,TABLE_NAME as tableName,TABLE_SCHEMA as tableSchema,TABLE_SCHEMA as tableCatalog,COLUMN_NAME as columnName,REFERENCED_TABLE_SCHEMA as referencedTableSchema,REFERENCED_TABLE_SCHEMA as referencedTableCatalog,REFERENCED_TABLE_NAME as referencedTableName,REFERENCED_COLUMN_NAME as referencedColumnName FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE where TABLE_NAME = 'usage_records' AND CONSTRAINT_NAME!='PRIMARY' AND CONSTRAINT_SCHEMA='pdf' AND REFERENCED_TABLE_NAME IS NOT NULL;","timestamp":"2025-07-12 16:53:25:5325"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT CONSTRAINT_CATALOG AS constraintCatalog, CONSTRAINT_NAME AS constraintName, CONSTRAINT_SCHEMA AS constraintSchema, CONSTRAINT_TYPE AS constraintType, TABLE_NAME AS tableName, TABLE_SCHEMA AS tableSchema from INFORMATION_SCHEMA.TABLE_CONSTRAINTS WHERE table_name='usage_records' AND constraint_name = 'usage_records_ibfk_1' AND TABLE_SCHEMA = 'pdf';","timestamp":"2025-07-12 16:53:25:5325"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `usage_records` DROP FOREIGN KEY `usage_records_ibfk_1`;","timestamp":"2025-07-12 16:53:25:5325"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `usage_records` ADD FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE ON UPDATE CASCADE;","timestamp":"2025-07-12 16:53:25:5325"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `usage_records` CHANGE `tool_name` `tool_name` VARCHAR(100) NOT NULL;","timestamp":"2025-07-12 16:53:28:5328"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `usage_records` CHANGE `file_size_mb` `file_size_mb` DECIMAL(10,2) NOT NULL;","timestamp":"2025-07-12 16:53:28:5328"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `usage_records` CHANGE `processing_time_seconds` `processing_time_seconds` INTEGER NOT NULL;","timestamp":"2025-07-12 16:53:28:5328"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `usage_records` CHANGE `input_file_count` `input_file_count` INTEGER NOT NULL;","timestamp":"2025-07-12 16:53:28:5328"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `usage_records` CHANGE `output_file_count` `output_file_count` INTEGER NOT NULL DEFAULT 0;","timestamp":"2025-07-12 16:53:28:5328"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `usage_records` CHANGE `success` `success` TINYINT(1) NOT NULL DEFAULT true;","timestamp":"2025-07-12 16:53:29:5329"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `usage_records` CHANGE `error_message` `error_message` TEXT;","timestamp":"2025-07-12 16:53:29:5329"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `usage_records` CHANGE `parameters` `parameters` JSON;","timestamp":"2025-07-12 16:53:29:5329"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `usage_records` CHANGE `created_at` `created_at` DATETIME NOT NULL;","timestamp":"2025-07-12 16:53:29:5329"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `usage_records` CHANGE `updated_at` `updated_at` DATETIME NOT NULL;","timestamp":"2025-07-12 16:53:29:5329"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SHOW INDEX FROM `usage_records`","timestamp":"2025-07-12 16:53:29:5329"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mDatabase initialized successfully\u001b[39m","timestamp":"2025-07-12 16:53:29:5329"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mServer running on port 3001\u001b[39m","timestamp":"2025-07-12 16:53:29:5329"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mEnvironment: development\u001b[39m","timestamp":"2025-07-12 16:53:29:5329"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mDatabase connected and synchronized\u001b[39m","timestamp":"2025-07-12 16:53:29:5329"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT 1+1 AS result","timestamp":"2025-07-12 16:53:53:5353"}
{"database":"pdf","host":"127.0.0.1","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mDatabase connection established successfully\u001b[39m","port":3306,"timestamp":"2025-07-12 16:53:53:5353"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT TABLE_NAME FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_TYPE = 'BASE TABLE' AND TABLE_NAME = 'users' AND TABLE_SCHEMA = 'pdf'","timestamp":"2025-07-12 16:53:53:5353"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SHOW FULL COLUMNS FROM `users`;","timestamp":"2025-07-12 16:53:53:5353"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT CONSTRAINT_NAME as constraint_name,CONSTRAINT_NAME as constraintName,CONSTRAINT_SCHEMA as constraintSchema,CONSTRAINT_SCHEMA as constraintCatalog,TABLE_NAME as tableName,TABLE_SCHEMA as tableSchema,TABLE_SCHEMA as tableCatalog,COLUMN_NAME as columnName,REFERENCED_TABLE_SCHEMA as referencedTableSchema,REFERENCED_TABLE_SCHEMA as referencedTableCatalog,REFERENCED_TABLE_NAME as referencedTableName,REFERENCED_COLUMN_NAME as referencedColumnName FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE where TABLE_NAME = 'users' AND CONSTRAINT_NAME!='PRIMARY' AND CONSTRAINT_SCHEMA='pdf' AND REFERENCED_TABLE_NAME IS NOT NULL;","timestamp":"2025-07-12 16:53:53:5353"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `email` `email` VARCHAR(255) NOT NULL UNIQUE;","timestamp":"2025-07-12 16:53:53:5353"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT 1+1 AS result","timestamp":"2025-07-12 16:53:53:5353"}
{"database":"pdf","host":"127.0.0.1","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mDatabase connection established successfully\u001b[39m","port":3306,"timestamp":"2025-07-12 16:53:53:5353"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT TABLE_NAME FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_TYPE = 'BASE TABLE' AND TABLE_NAME = 'users' AND TABLE_SCHEMA = 'pdf'","timestamp":"2025-07-12 16:53:53:5353"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SHOW FULL COLUMNS FROM `users`;","timestamp":"2025-07-12 16:53:53:5353"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT CONSTRAINT_NAME as constraint_name,CONSTRAINT_NAME as constraintName,CONSTRAINT_SCHEMA as constraintSchema,CONSTRAINT_SCHEMA as constraintCatalog,TABLE_NAME as tableName,TABLE_SCHEMA as tableSchema,TABLE_SCHEMA as tableCatalog,COLUMN_NAME as columnName,REFERENCED_TABLE_SCHEMA as referencedTableSchema,REFERENCED_TABLE_SCHEMA as referencedTableCatalog,REFERENCED_TABLE_NAME as referencedTableName,REFERENCED_COLUMN_NAME as referencedColumnName FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE where TABLE_NAME = 'users' AND CONSTRAINT_NAME!='PRIMARY' AND CONSTRAINT_SCHEMA='pdf' AND REFERENCED_TABLE_NAME IS NOT NULL;","timestamp":"2025-07-12 16:53:53:5353"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `email` `email` VARCHAR(255) NOT NULL UNIQUE;","timestamp":"2025-07-12 16:53:53:5353"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `password_hash` `password_hash` VARCHAR(255) NOT NULL;","timestamp":"2025-07-12 16:53:53:5353"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `password_hash` `password_hash` VARCHAR(255) NOT NULL;","timestamp":"2025-07-12 16:53:54:5354"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `first_name` `first_name` VARCHAR(100) NOT NULL;","timestamp":"2025-07-12 16:53:54:5354"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `first_name` `first_name` VARCHAR(100) NOT NULL;","timestamp":"2025-07-12 16:53:55:5355"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `last_name` `last_name` VARCHAR(100) NOT NULL;","timestamp":"2025-07-12 16:53:55:5355"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `last_name` `last_name` VARCHAR(100) NOT NULL;","timestamp":"2025-07-12 16:53:56:5356"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `subscription_plan` `subscription_plan` ENUM('free', 'premium', 'enterprise') NOT NULL DEFAULT 'free';","timestamp":"2025-07-12 16:53:56:5356"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `subscription_plan` `subscription_plan` ENUM('free', 'premium', 'enterprise') NOT NULL DEFAULT 'free';","timestamp":"2025-07-12 16:53:56:5356"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `is_verified` `is_verified` TINYINT(1) NOT NULL DEFAULT false;","timestamp":"2025-07-12 16:53:56:5356"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `is_verified` `is_verified` TINYINT(1) NOT NULL DEFAULT false;","timestamp":"2025-07-12 16:53:56:5356"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `verification_token` `verification_token` VARCHAR(255);","timestamp":"2025-07-12 16:53:57:5357"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `verification_token` `verification_token` VARCHAR(255);","timestamp":"2025-07-12 16:53:57:5357"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `reset_password_token` `reset_password_token` VARCHAR(255);","timestamp":"2025-07-12 16:53:57:5357"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `reset_password_token` `reset_password_token` VARCHAR(255);","timestamp":"2025-07-12 16:53:57:5357"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `reset_password_expires` `reset_password_expires` DATETIME;","timestamp":"2025-07-12 16:53:57:5357"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `reset_password_expires` `reset_password_expires` DATETIME;","timestamp":"2025-07-12 16:53:58:5358"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `last_login_at` `last_login_at` DATETIME;","timestamp":"2025-07-12 16:53:58:5358"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `last_login_at` `last_login_at` DATETIME;","timestamp":"2025-07-12 16:53:58:5358"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `created_at` `created_at` DATETIME NOT NULL;","timestamp":"2025-07-12 16:53:58:5358"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `created_at` `created_at` DATETIME NOT NULL;","timestamp":"2025-07-12 16:53:59:5359"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `updated_at` `updated_at` DATETIME NOT NULL;","timestamp":"2025-07-12 16:53:59:5359"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `updated_at` `updated_at` DATETIME NOT NULL;","timestamp":"2025-07-12 16:53:59:5359"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SHOW INDEX FROM `users`","timestamp":"2025-07-12 16:53:59:5359"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT TABLE_NAME FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_TYPE = 'BASE TABLE' AND TABLE_NAME = 'processing_sessions' AND TABLE_SCHEMA = 'pdf'","timestamp":"2025-07-12 16:53:59:5359"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SHOW FULL COLUMNS FROM `processing_sessions`;","timestamp":"2025-07-12 16:53:59:5359"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT CONSTRAINT_NAME as constraint_name,CONSTRAINT_NAME as constraintName,CONSTRAINT_SCHEMA as constraintSchema,CONSTRAINT_SCHEMA as constraintCatalog,TABLE_NAME as tableName,TABLE_SCHEMA as tableSchema,TABLE_SCHEMA as tableCatalog,COLUMN_NAME as columnName,REFERENCED_TABLE_SCHEMA as referencedTableSchema,REFERENCED_TABLE_SCHEMA as referencedTableCatalog,REFERENCED_TABLE_NAME as referencedTableName,REFERENCED_COLUMN_NAME as referencedColumnName FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE where TABLE_NAME = 'processing_sessions' AND CONSTRAINT_NAME!='PRIMARY' AND CONSTRAINT_SCHEMA='pdf' AND REFERENCED_TABLE_NAME IS NOT NULL;","timestamp":"2025-07-12 16:53:59:5359"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SHOW INDEX FROM `users`","timestamp":"2025-07-12 16:54:00:540"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT CONSTRAINT_CATALOG AS constraintCatalog, CONSTRAINT_NAME AS constraintName, CONSTRAINT_SCHEMA AS constraintSchema, CONSTRAINT_TYPE AS constraintType, TABLE_NAME AS tableName, TABLE_SCHEMA AS tableSchema from INFORMATION_SCHEMA.TABLE_CONSTRAINTS WHERE table_name='processing_sessions' AND constraint_name = 'processing_sessions_ibfk_1' AND TABLE_SCHEMA = 'pdf';","timestamp":"2025-07-12 16:54:00:540"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `processing_sessions` DROP FOREIGN KEY `processing_sessions_ibfk_1`;","timestamp":"2025-07-12 16:54:00:540"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT TABLE_NAME FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_TYPE = 'BASE TABLE' AND TABLE_NAME = 'processing_sessions' AND TABLE_SCHEMA = 'pdf'","timestamp":"2025-07-12 16:54:00:540"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SHOW FULL COLUMNS FROM `processing_sessions`;","timestamp":"2025-07-12 16:54:00:540"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT CONSTRAINT_NAME as constraint_name,CONSTRAINT_NAME as constraintName,CONSTRAINT_SCHEMA as constraintSchema,CONSTRAINT_SCHEMA as constraintCatalog,TABLE_NAME as tableName,TABLE_SCHEMA as tableSchema,TABLE_SCHEMA as tableCatalog,COLUMN_NAME as columnName,REFERENCED_TABLE_SCHEMA as referencedTableSchema,REFERENCED_TABLE_SCHEMA as referencedTableCatalog,REFERENCED_TABLE_NAME as referencedTableName,REFERENCED_COLUMN_NAME as referencedColumnName FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE where TABLE_NAME = 'processing_sessions' AND CONSTRAINT_NAME!='PRIMARY' AND CONSTRAINT_SCHEMA='pdf' AND REFERENCED_TABLE_NAME IS NOT NULL;","timestamp":"2025-07-12 16:54:00:540"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `processing_sessions` ADD FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE ON UPDATE CASCADE;","timestamp":"2025-07-12 16:54:00:540"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT CONSTRAINT_CATALOG AS constraintCatalog, CONSTRAINT_NAME AS constraintName, CONSTRAINT_SCHEMA AS constraintSchema, CONSTRAINT_TYPE AS constraintType, TABLE_NAME AS tableName, TABLE_SCHEMA AS tableSchema from INFORMATION_SCHEMA.TABLE_CONSTRAINTS WHERE table_name='processing_sessions' AND constraint_name = 'processing_sessions_ibfk_1' AND TABLE_SCHEMA = 'pdf';","timestamp":"2025-07-12 16:54:00:540"}
{"error":"Constraint processing_sessions_ibfk_1 on table processing_sessions does not exist","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mFailed to initialize database\u001b[39m","timestamp":"2025-07-12 16:54:00:540"}
