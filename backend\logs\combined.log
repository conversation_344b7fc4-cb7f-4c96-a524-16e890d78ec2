{"database":"pdf","error":"Access denied for user 'root'@'localhost' (using password: NO)","host":"localhost","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mUnable to connect to database\u001b[39m","port":3306,"timestamp":"2025-07-12 14:06:49:649"}
{"database":"pdf","error":"Access denied for user 'root'@'localhost' (using password: NO)","host":"localhost","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mUnable to connect to database\u001b[39m","port":3306,"timestamp":"2025-07-12 14:07:29:729"}
{"database":"pdf","error":"Access denied for user 'root'@'localhost' (using password: NO)","host":"localhost","level":"\u001b[31merror\u001b[39m","message":"\u001b[31mUnable to connect to database\u001b[39m","port":3306,"timestamp":"2025-07-12 14:08:00:80"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT 1+1 AS result","timestamp":"2025-07-12 14:08:19:819"}
{"database":"pdf","host":"127.0.0.1","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mDatabase connection established successfully\u001b[39m","port":3306,"timestamp":"2025-07-12 14:08:19:819"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT TABLE_NAME FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_TYPE = 'BASE TABLE' AND TABLE_NAME = 'users' AND TABLE_SCHEMA = 'pdf'","timestamp":"2025-07-12 14:08:19:819"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): CREATE TABLE IF NOT EXISTS `users` (`id` CHAR(36) BINARY , `email` VARCHAR(255) NOT NULL UNIQUE, `password_hash` VARCHAR(255) NOT NULL, `first_name` VARCHAR(100) NOT NULL, `last_name` VARCHAR(100) NOT NULL, `subscription_plan` ENUM('free', 'premium', 'enterprise') NOT NULL DEFAULT 'free', `is_verified` TINYINT(1) NOT NULL DEFAULT false, `verification_token` VARCHAR(255), `reset_password_token` VARCHAR(255), `reset_password_expires` DATETIME, `last_login_at` DATETIME, `created_at` DATETIME NOT NULL, `updated_at` DATETIME NOT NULL, PRIMARY KEY (`id`)) ENGINE=InnoDB;","timestamp":"2025-07-12 14:08:19:819"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SHOW INDEX FROM `users`","timestamp":"2025-07-12 14:08:20:820"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` ADD UNIQUE INDEX `users_email` (`email`)","timestamp":"2025-07-12 14:08:20:820"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` ADD INDEX `users_subscription_plan` (`subscription_plan`)","timestamp":"2025-07-12 14:08:21:821"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` ADD INDEX `users_is_verified` (`is_verified`)","timestamp":"2025-07-12 14:08:21:821"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` ADD INDEX `users_verification_token` (`verification_token`)","timestamp":"2025-07-12 14:08:21:821"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` ADD INDEX `users_reset_password_token` (`reset_password_token`)","timestamp":"2025-07-12 14:08:22:822"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT TABLE_NAME FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_TYPE = 'BASE TABLE' AND TABLE_NAME = 'usage_records' AND TABLE_SCHEMA = 'pdf'","timestamp":"2025-07-12 14:08:22:822"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): CREATE TABLE IF NOT EXISTS `usage_records` (`id` CHAR(36) BINARY , `user_id` CHAR(36) BINARY NOT NULL, `tool_name` VARCHAR(100) NOT NULL, `file_size_mb` DECIMAL(10,2) NOT NULL, `processing_time_seconds` INTEGER NOT NULL, `input_file_count` INTEGER NOT NULL, `output_file_count` INTEGER NOT NULL DEFAULT 0, `success` TINYINT(1) NOT NULL DEFAULT true, `error_message` TEXT, `parameters` JSON, `created_at` DATETIME NOT NULL, `updated_at` DATETIME NOT NULL, PRIMARY KEY (`id`), FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE ON UPDATE CASCADE) ENGINE=InnoDB;","timestamp":"2025-07-12 14:08:22:822"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SHOW INDEX FROM `usage_records`","timestamp":"2025-07-12 14:08:22:822"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `usage_records` ADD INDEX `usage_records_user_id` (`user_id`)","timestamp":"2025-07-12 14:08:22:822"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `usage_records` ADD INDEX `usage_records_tool_name` (`tool_name`)","timestamp":"2025-07-12 14:08:23:823"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `usage_records` ADD INDEX `usage_records_success` (`success`)","timestamp":"2025-07-12 14:08:23:823"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `usage_records` ADD INDEX `usage_records_created_at` (`created_at`)","timestamp":"2025-07-12 14:08:23:823"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `usage_records` ADD INDEX `usage_records_user_id_created_at` (`user_id`, `created_at`)","timestamp":"2025-07-12 14:08:24:824"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `usage_records` ADD INDEX `usage_records_user_id_tool_name` (`user_id`, `tool_name`)","timestamp":"2025-07-12 14:08:24:824"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mDatabase initialized successfully\u001b[39m","timestamp":"2025-07-12 14:08:24:824"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mServer running on port 3001\u001b[39m","timestamp":"2025-07-12 14:08:24:824"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mEnvironment: development\u001b[39m","timestamp":"2025-07-12 14:08:24:824"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mDatabase connected and synchronized\u001b[39m","timestamp":"2025-07-12 14:08:24:824"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT 1+1 AS result","timestamp":"2025-07-12 14:11:36:1136"}
{"database":"pdf","host":"127.0.0.1","level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mDatabase connection established successfully\u001b[39m","port":3306,"timestamp":"2025-07-12 14:11:36:1136"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT TABLE_NAME FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_TYPE = 'BASE TABLE' AND TABLE_NAME = 'users' AND TABLE_SCHEMA = 'pdf'","timestamp":"2025-07-12 14:11:36:1136"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SHOW FULL COLUMNS FROM `users`;","timestamp":"2025-07-12 14:11:36:1136"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT CONSTRAINT_NAME as constraint_name,CONSTRAINT_NAME as constraintName,CONSTRAINT_SCHEMA as constraintSchema,CONSTRAINT_SCHEMA as constraintCatalog,TABLE_NAME as tableName,TABLE_SCHEMA as tableSchema,TABLE_SCHEMA as tableCatalog,COLUMN_NAME as columnName,REFERENCED_TABLE_SCHEMA as referencedTableSchema,REFERENCED_TABLE_SCHEMA as referencedTableCatalog,REFERENCED_TABLE_NAME as referencedTableName,REFERENCED_COLUMN_NAME as referencedColumnName FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE where TABLE_NAME = 'users' AND CONSTRAINT_NAME!='PRIMARY' AND CONSTRAINT_SCHEMA='pdf' AND REFERENCED_TABLE_NAME IS NOT NULL;","timestamp":"2025-07-12 14:11:36:1136"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `email` `email` VARCHAR(255) NOT NULL UNIQUE;","timestamp":"2025-07-12 14:11:36:1136"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `password_hash` `password_hash` VARCHAR(255) NOT NULL;","timestamp":"2025-07-12 14:11:36:1136"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `first_name` `first_name` VARCHAR(100) NOT NULL;","timestamp":"2025-07-12 14:11:36:1136"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `last_name` `last_name` VARCHAR(100) NOT NULL;","timestamp":"2025-07-12 14:11:36:1136"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `subscription_plan` `subscription_plan` ENUM('free', 'premium', 'enterprise') NOT NULL DEFAULT 'free';","timestamp":"2025-07-12 14:11:37:1137"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `is_verified` `is_verified` TINYINT(1) NOT NULL DEFAULT false;","timestamp":"2025-07-12 14:11:37:1137"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `verification_token` `verification_token` VARCHAR(255);","timestamp":"2025-07-12 14:11:38:1138"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `reset_password_token` `reset_password_token` VARCHAR(255);","timestamp":"2025-07-12 14:11:38:1138"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `reset_password_expires` `reset_password_expires` DATETIME;","timestamp":"2025-07-12 14:11:39:1139"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `last_login_at` `last_login_at` DATETIME;","timestamp":"2025-07-12 14:11:39:1139"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `created_at` `created_at` DATETIME NOT NULL;","timestamp":"2025-07-12 14:11:39:1139"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `users` CHANGE `updated_at` `updated_at` DATETIME NOT NULL;","timestamp":"2025-07-12 14:11:39:1139"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SHOW INDEX FROM `users`","timestamp":"2025-07-12 14:11:40:1140"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT TABLE_NAME FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_TYPE = 'BASE TABLE' AND TABLE_NAME = 'usage_records' AND TABLE_SCHEMA = 'pdf'","timestamp":"2025-07-12 14:11:40:1140"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SHOW FULL COLUMNS FROM `usage_records`;","timestamp":"2025-07-12 14:11:40:1140"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT CONSTRAINT_NAME as constraint_name,CONSTRAINT_NAME as constraintName,CONSTRAINT_SCHEMA as constraintSchema,CONSTRAINT_SCHEMA as constraintCatalog,TABLE_NAME as tableName,TABLE_SCHEMA as tableSchema,TABLE_SCHEMA as tableCatalog,COLUMN_NAME as columnName,REFERENCED_TABLE_SCHEMA as referencedTableSchema,REFERENCED_TABLE_SCHEMA as referencedTableCatalog,REFERENCED_TABLE_NAME as referencedTableName,REFERENCED_COLUMN_NAME as referencedColumnName FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE where TABLE_NAME = 'usage_records' AND CONSTRAINT_NAME!='PRIMARY' AND CONSTRAINT_SCHEMA='pdf' AND REFERENCED_TABLE_NAME IS NOT NULL;","timestamp":"2025-07-12 14:11:40:1140"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SELECT CONSTRAINT_CATALOG AS constraintCatalog, CONSTRAINT_NAME AS constraintName, CONSTRAINT_SCHEMA AS constraintSchema, CONSTRAINT_TYPE AS constraintType, TABLE_NAME AS tableName, TABLE_SCHEMA AS tableSchema from INFORMATION_SCHEMA.TABLE_CONSTRAINTS WHERE table_name='usage_records' AND constraint_name = 'usage_records_ibfk_1' AND TABLE_SCHEMA = 'pdf';","timestamp":"2025-07-12 14:11:40:1140"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `usage_records` DROP FOREIGN KEY `usage_records_ibfk_1`;","timestamp":"2025-07-12 14:11:40:1140"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `usage_records` ADD FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE ON UPDATE CASCADE;","timestamp":"2025-07-12 14:11:40:1140"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `usage_records` CHANGE `tool_name` `tool_name` VARCHAR(100) NOT NULL;","timestamp":"2025-07-12 14:11:42:1142"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `usage_records` CHANGE `file_size_mb` `file_size_mb` DECIMAL(10,2) NOT NULL;","timestamp":"2025-07-12 14:11:42:1142"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `usage_records` CHANGE `processing_time_seconds` `processing_time_seconds` INTEGER NOT NULL;","timestamp":"2025-07-12 14:11:42:1142"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `usage_records` CHANGE `input_file_count` `input_file_count` INTEGER NOT NULL;","timestamp":"2025-07-12 14:11:42:1142"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `usage_records` CHANGE `output_file_count` `output_file_count` INTEGER NOT NULL DEFAULT 0;","timestamp":"2025-07-12 14:11:43:1143"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `usage_records` CHANGE `success` `success` TINYINT(1) NOT NULL DEFAULT true;","timestamp":"2025-07-12 14:11:43:1143"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `usage_records` CHANGE `error_message` `error_message` TEXT;","timestamp":"2025-07-12 14:11:43:1143"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `usage_records` CHANGE `parameters` `parameters` JSON;","timestamp":"2025-07-12 14:11:43:1143"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `usage_records` CHANGE `created_at` `created_at` DATETIME NOT NULL;","timestamp":"2025-07-12 14:11:43:1143"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): ALTER TABLE `usage_records` CHANGE `updated_at` `updated_at` DATETIME NOT NULL;","timestamp":"2025-07-12 14:11:43:1143"}
{"level":"\u001b[37mdebug\u001b[39m","message":"\u001b[37mSQL Query\u001b[39m","sql":"Executing (default): SHOW INDEX FROM `usage_records`","timestamp":"2025-07-12 14:11:43:1143"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mDatabase initialized successfully\u001b[39m","timestamp":"2025-07-12 14:11:44:1144"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mServer running on port 3001\u001b[39m","timestamp":"2025-07-12 14:11:44:1144"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mEnvironment: development\u001b[39m","timestamp":"2025-07-12 14:11:44:1144"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mDatabase connected and synchronized\u001b[39m","timestamp":"2025-07-12 14:11:44:1144"}
