import { spawn, ChildProcess } from 'child_process';
import path from 'path';
import fs from 'fs/promises';
import { v4 as uuidv4 } from 'uuid';
import { logger } from '../utils/logger';

export interface PythonToolParams {
  toolName: string;
  inputFiles: string[];
  outputPath: string;
  parameters?: Record<string, any>;
  timeout?: number;
}

export interface PythonToolResult {
  success: boolean;
  outputFiles: string[];
  error?: string;
  processingTime: number;
  logs?: string[];
}

export class PythonExecutor {
  private readonly toolsPath: string;
  private readonly pythonPath: string;
  private readonly maxTimeout: number;

  constructor() {
    this.toolsPath = path.resolve(__dirname, '../../../tools');
    this.pythonPath = process.env.PYTHON_PATH || 'python';
    this.maxTimeout = parseInt(process.env.MAX_PROCESSING_TIMEOUT || '300000'); // 5 minutes
  }

  async executeTool(params: PythonToolParams): Promise<PythonToolResult> {
    const startTime = Date.now();
    const jobId = uuidv4();
    
    logger.info('Starting Python tool execution', {
      jobId,
      toolName: params.toolName,
      inputFiles: params.inputFiles.length,
      parameters: params.parameters
    });

    try {
      // Validate inputs
      await this.validateInputs(params);

      // Prepare execution environment
      const scriptPath = path.join(this.toolsPath, `${params.toolName}.py`);
      const tempConfigPath = await this.createTempConfig(jobId, params);

      // Execute Python script
      const result = await this.runPythonScript(scriptPath, tempConfigPath, params.timeout);

      // Clean up temporary files
      await this.cleanup(tempConfigPath);

      const processingTime = Date.now() - startTime;

      logger.info('Python tool execution completed', {
        jobId,
        toolName: params.toolName,
        success: result.success,
        processingTime
      });

      return {
        ...result,
        processingTime
      };

    } catch (error) {
      const processingTime = Date.now() - startTime;
      
      logger.error('Python tool execution failed', {
        jobId,
        toolName: params.toolName,
        error: error instanceof Error ? error.message : String(error),
        processingTime
      });

      return {
        success: false,
        outputFiles: [],
        error: error instanceof Error ? error.message : String(error),
        processingTime
      };
    }
  }

  private async validateInputs(params: PythonToolParams): Promise<void> {
    // Check if tool exists
    const toolPath = path.join(this.toolsPath, `${params.toolName}.py`);
    try {
      await fs.access(toolPath);
    } catch {
      throw new Error(`Tool '${params.toolName}' not found`);
    }

    // Validate input files exist
    for (const inputFile of params.inputFiles) {
      try {
        await fs.access(inputFile);
      } catch {
        throw new Error(`Input file not found: ${inputFile}`);
      }
    }

    // Validate output directory
    try {
      await fs.mkdir(params.outputPath, { recursive: true });
    } catch (error) {
      throw new Error(`Cannot create output directory: ${params.outputPath}`);
    }
  }

  private async createTempConfig(jobId: string, params: PythonToolParams): Promise<string> {
    const config = {
      job_id: jobId,
      tool_name: params.toolName,
      input_files: params.inputFiles,
      output_path: params.outputPath,
      parameters: params.parameters || {}
    };

    const tempDir = path.join(__dirname, '../../../temp');
    await fs.mkdir(tempDir, { recursive: true });
    
    const configPath = path.join(tempDir, `${jobId}_config.json`);
    await fs.writeFile(configPath, JSON.stringify(config, null, 2));
    
    return configPath;
  }

  private async runPythonScript(
    scriptPath: string,
    configPath: string,
    timeout?: number
  ): Promise<Omit<PythonToolResult, 'processingTime'>> {
    return new Promise((resolve, reject) => {
      const actualTimeout = Math.min(timeout || this.maxTimeout, this.maxTimeout);

      // Use the wrapper script instead of individual tool scripts
      const wrapperScript = path.join(this.toolsPath, `${path.basename(scriptPath, '.py')}_wrapper.py`);

      const pythonProcess: ChildProcess = spawn(this.pythonPath, [
        wrapperScript,
        '--config', configPath
      ], {
        stdio: ['pipe', 'pipe', 'pipe'],
        timeout: actualTimeout
      });

      let stdout = '';
      let stderr = '';

      pythonProcess.stdout?.on('data', (data) => {
        stdout += data.toString();
      });

      pythonProcess.stderr?.on('data', (data) => {
        stderr += data.toString();
      });

      pythonProcess.on('close', (code) => {
        if (code === 0) {
          try {
            const result = JSON.parse(stdout);
            resolve({
              success: true,
              outputFiles: result.output_files || [],
              logs: result.logs || []
            });
          } catch (error) {
            reject(new Error(`Failed to parse Python script output: ${error}`));
          }
        } else {
          reject(new Error(`Python script failed with code ${code}: ${stderr}`));
        }
      });

      pythonProcess.on('error', (error) => {
        reject(new Error(`Failed to start Python process: ${error.message}`));
      });

      // Handle timeout
      setTimeout(() => {
        if (!pythonProcess.killed) {
          pythonProcess.kill('SIGTERM');
          reject(new Error(`Python script timed out after ${actualTimeout}ms`));
        }
      }, actualTimeout);
    });
  }

  private async cleanup(configPath: string): Promise<void> {
    try {
      await fs.unlink(configPath);
    } catch (error) {
      logger.warn('Failed to cleanup temp config file', { configPath, error });
    }
  }

  async getAvailableTools(): Promise<string[]> {
    try {
      const files = await fs.readdir(this.toolsPath);
      return files
        .filter(file => file.endsWith('.py') && file !== '__init__.py' && file !== 'base_tool.py')
        .map(file => path.basename(file, '.py'));
    } catch (error) {
      logger.error('Failed to get available tools', { error });
      return [];
    }
  }
}

export const pythonExecutor = new PythonExecutor();
