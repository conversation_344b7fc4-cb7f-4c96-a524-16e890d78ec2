import { DataTypes, Model, Optional } from 'sequelize';
import { sequelize } from '../config/database';

// User attributes interface
export interface UserAttributes {
  id: string;
  email: string;
  password_hash: string;
  first_name: string;
  last_name: string;
  subscription_plan: 'free' | 'premium' | 'enterprise';
  is_verified: boolean;
  verification_token?: string;
  reset_password_token?: string;
  reset_password_expires?: Date;
  last_login_at?: Date;
  created_at?: Date;
  updated_at?: Date;
}

// User creation attributes (optional fields for creation)
export interface UserCreationAttributes extends Optional<UserAttributes, 
  'id' | 'is_verified' | 'verification_token' | 'reset_password_token' | 
  'reset_password_expires' | 'last_login_at' | 'created_at' | 'updated_at'> {}

// User model class
export class User extends Model<UserAttributes, UserCreationAttributes> implements UserAttributes {
  public id!: string;
  public email!: string;
  public password_hash!: string;
  public first_name!: string;
  public last_name!: string;
  public subscription_plan!: 'free' | 'premium' | 'enterprise';
  public is_verified!: boolean;
  public verification_token?: string;
  public reset_password_token?: string;
  public reset_password_expires?: Date;
  public last_login_at?: Date;

  // Timestamps
  public readonly created_at!: Date;
  public readonly updated_at!: Date;

  // Instance methods
  public getFullName(): string {
    return `${this.first_name} ${this.last_name}`;
  }

  public toJSON(): Omit<UserAttributes, 'password_hash' | 'verification_token' | 'reset_password_token'> {
    const values = { ...this.get() };
    delete values.password_hash;
    delete values.verification_token;
    delete values.reset_password_token;
    return values;
  }
}

// Initialize User model
User.init(
  {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true,
    },
    email: {
      type: DataTypes.STRING(255),
      allowNull: false,
      unique: true,
      validate: {
        isEmail: true,
      },
    },
    password_hash: {
      type: DataTypes.STRING(255),
      allowNull: false,
    },
    first_name: {
      type: DataTypes.STRING(100),
      allowNull: false,
      validate: {
        len: [2, 100],
      },
    },
    last_name: {
      type: DataTypes.STRING(100),
      allowNull: false,
      validate: {
        len: [2, 100],
      },
    },
    subscription_plan: {
      type: DataTypes.ENUM('free', 'premium', 'enterprise'),
      allowNull: false,
      defaultValue: 'free',
    },
    is_verified: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: false,
    },
    verification_token: {
      type: DataTypes.STRING(255),
      allowNull: true,
    },
    reset_password_token: {
      type: DataTypes.STRING(255),
      allowNull: true,
    },
    reset_password_expires: {
      type: DataTypes.DATE,
      allowNull: true,
    },
    last_login_at: {
      type: DataTypes.DATE,
      allowNull: true,
    },
  },
  {
    sequelize,
    modelName: 'User',
    tableName: 'users',
    timestamps: true,
    underscored: true,
    indexes: [
      {
        unique: true,
        fields: ['email'],
      },
      {
        fields: ['subscription_plan'],
      },
      {
        fields: ['is_verified'],
      },
      {
        fields: ['verification_token'],
      },
      {
        fields: ['reset_password_token'],
      },
    ],
  }
);

export default User;
