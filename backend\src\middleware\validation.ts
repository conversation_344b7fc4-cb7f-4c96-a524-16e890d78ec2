import Joi from 'joi';
import { Request, Response, NextFunction } from 'express';
import { ValidationError } from './errorHandler';

// Common validation schemas
export const schemas = {
  // File upload validation
  fileUpload: Joi.object({
    files: Joi.array().items(
      Joi.object({
        fieldname: Joi.string().required(),
        originalname: Joi.string().required(),
        encoding: Joi.string().required(),
        mimetype: Joi.string().valid(
          'application/pdf',
          'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
          'application/vnd.ms-excel',
          'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
          'application/msword',
          'application/vnd.openxmlformats-officedocument.presentationml.presentation',
          'application/vnd.ms-powerpoint',
          'text/html',
          'image/jpeg',
          'image/png',
          'image/bmp',
          'image/tiff'
        ).required(),
        size: Joi.number().max(50 * 1024 * 1024).required(), // 50MB max
        buffer: Joi.binary().required()
      })
    ).min(1).max(10).required() // 1-10 files
  }),

  // PDF tool parameters
  pdfToolParams: Joi.object({
    toolName: Joi.string().valid(
      'repair_pdf',
      'merge_pdf',
      'split_pdf',
      'compress_pdf',
      'excel_to_pdf',
      'word_to_pdf',
      'powerpoint_to_pdf',
      'html_to_pdf',
      'pdf_to_excel',
      'pdf_to_word',
      'pdf_to_powerpoint',
      'pdf_to_image',
      'pdf_to_pdfa',
      'ocr_pdf',
      'sign_pdf',
      'protect_pdf',
      'unlock_pdf',
      'watermark_pdf',
      'rotate_pdf',
      'crop_pdf',
      'organize_pdf',
      'page_numbers_pdf',
      'redact_pdf',
      'compare_pdf',
      'edit_pdf',
      'scan_to_pdf',
      'image_to_pdf'
    ).required(),
    parameters: Joi.object().optional()
  }),

  // User registration
  userRegistration: Joi.object({
    email: Joi.string().email().required(),
    password: Joi.string().min(8).pattern(
      /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/
    ).required().messages({
      'string.pattern.base': 'Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character'
    }),
    confirmPassword: Joi.string().valid(Joi.ref('password')).required().messages({
      'any.only': 'Passwords do not match'
    }),
    firstName: Joi.string().min(2).max(50).required(),
    lastName: Joi.string().min(2).max(50).required()
  }),

  // User login
  userLogin: Joi.object({
    email: Joi.string().email().required(),
    password: Joi.string().required()
  }),

  // Password reset request
  passwordResetRequest: Joi.object({
    email: Joi.string().email().required()
  }),

  // Password reset
  passwordReset: Joi.object({
    token: Joi.string().required(),
    password: Joi.string().min(8).pattern(
      /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/
    ).required(),
    confirmPassword: Joi.string().valid(Joi.ref('password')).required()
  })
};

// Validation middleware factory
export const validate = (schema: Joi.ObjectSchema, property: 'body' | 'query' | 'params' = 'body') => {
  return (req: Request, res: Response, next: NextFunction) => {
    const { error, value } = schema.validate(req[property], {
      abortEarly: false,
      stripUnknown: true
    });

    if (error) {
      const errorMessage = error.details
        .map(detail => detail.message)
        .join(', ');
      
      throw new ValidationError(errorMessage);
    }

    // Replace the request property with the validated and sanitized value
    req[property] = value;
    next();
  };
};

// File validation middleware
export const validateFiles = (req: Request, res: Response, next: NextFunction) => {
  if (!req.files || !Array.isArray(req.files) || req.files.length === 0) {
    throw new ValidationError('At least one file is required');
  }

  // Check file count
  if (req.files.length > 10) {
    throw new ValidationError('Maximum 10 files allowed');
  }

  // Validate each file
  for (const file of req.files) {
    // Check file size (50MB max)
    if (file.size > 50 * 1024 * 1024) {
      throw new ValidationError(`File ${file.originalname} exceeds maximum size of 50MB`);
    }

    // Check file type based on tool
    const toolName = req.body.toolName;
    if (!isValidFileType(file.mimetype, toolName)) {
      throw new ValidationError(`File ${file.originalname} has unsupported type for tool ${toolName}`);
    }
  }

  next();
};

// Helper function to check if file type is valid for a specific tool
function isValidFileType(mimetype: string, toolName: string): boolean {
  const pdfTools = [
    'repair_pdf', 'merge_pdf', 'split_pdf', 'compress_pdf', 'pdf_to_excel',
    'pdf_to_word', 'pdf_to_powerpoint', 'pdf_to_image', 'pdf_to_pdfa',
    'ocr_pdf', 'sign_pdf', 'protect_pdf', 'unlock_pdf', 'watermark_pdf',
    'rotate_pdf', 'crop_pdf', 'organize_pdf', 'page_numbers_pdf',
    'redact_pdf', 'compare_pdf', 'edit_pdf'
  ];

  const officeToTools = ['excel_to_pdf', 'word_to_pdf', 'powerpoint_to_pdf'];
  const imageTools = ['image_to_pdf', 'scan_to_pdf'];
  const htmlTools = ['html_to_pdf'];

  if (pdfTools.includes(toolName)) {
    return mimetype === 'application/pdf';
  }

  if (officeToTools.includes(toolName)) {
    return [
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      'application/vnd.ms-excel',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'application/msword',
      'application/vnd.openxmlformats-officedocument.presentationml.presentation',
      'application/vnd.ms-powerpoint'
    ].includes(mimetype);
  }

  if (imageTools.includes(toolName)) {
    return [
      'image/jpeg',
      'image/png',
      'image/bmp',
      'image/tiff'
    ].includes(mimetype);
  }

  if (htmlTools.includes(toolName)) {
    return mimetype === 'text/html';
  }

  return false;
}
