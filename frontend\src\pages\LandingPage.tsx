import React from 'react';
import { Link } from 'react-router-dom';
import { useLanguage } from '../contexts/LanguageContext';
import SEOHead from '../components/SEOHead';
import { 
  FileText, 
  Split, 
  Minimize2, 
  FileImage, 
  Edit, 
  PenTool,
  RotateCw,
  Globe,
  Lock,
  Unlock,
  FolderOpen,
  Wrench,
  Hash,
  Smartphone,
  Search,
  Eye,
  Scissors,
  Crop,
  ArrowRight,
  Star,
  Shield,
  Zap,
  Users,
  CheckCircle,
  FileText as FileTextIcon
} from 'lucide-react';

const LandingPage = () => {
  const { t, language } = useLanguage();

  // Show only the most popular PDF tools on landing page
  const popularTools = [
    {
      title: t('tool.merge.title'),
      description: t('tool.merge.description'),
      icon: <FileText className="w-6 h-6" />,
      link: "/fusionner-pdf",
      color: "from-blue-500 to-blue-600"
    },
    {
      title: t('tool.split.title'),
      description: t('tool.split.description'),
      icon: <Split className="w-6 h-6" />,
      link: "/diviser-pdf",
      color: "from-purple-500 to-purple-600"
    },
    {
      title: t('tool.compress.title'),
      description: t('tool.compress.description'),
      icon: <Minimize2 className="w-6 h-6" />,
      link: "/compresser-pdf",
      color: "from-green-500 to-green-600"
    },
    {
      title: "Modifier PDF",
      description: "Ajouter du texte, des images, des formes ou des annotations manuscrites à un document PDF.",
      icon: <Edit className="w-6 h-6" />,
      link: "/modifier-pdf",
      color: "from-violet-500 to-violet-600"
    },
    {
      title: t('tool.pdfToWord.title'),
      description: t('tool.pdfToWord.description'),
      icon: <FileText className="w-6 h-6" />,
      link: "/pdf-vers-word",
      color: "from-indigo-500 to-indigo-600"
    },
    {
      title: "JPG en PDF",
      description: "Convertissez vos images en PDF. Ajustez l'orientation et les marges.",
      icon: <FileImage className="w-6 h-6" />,
      link: "/jpg-vers-pdf",
      color: "from-amber-500 to-amber-600"
    },
    {
      title: "Signer PDF",
      description: "Signez vous-même ou demandez des signatures électroniques à des tiers.",
      icon: <PenTool className="w-6 h-6" />,
      link: "/signer-pdf",
      color: "from-cyan-500 to-cyan-600"
    },
    {
      title: "Protéger PDF",
      description: "Protégez les fichiers PDF avec un mot de passe. Chiffrez les documents PDF.",
      icon: <Lock className="w-6 h-6" />,
      link: "/proteger-pdf",
      color: "from-gray-500 to-gray-600"
    },
    {
      title: "Faire pivoter PDF",
      description: "Faites pivoter votre PDF comme vous le souhaitez. Tournez plusieurs fichiers PDF à la fois!",
      icon: <RotateCw className="w-6 h-6" />,
      link: "/pivoter-pdf",
      color: "from-red-500 to-red-600"
    }
  ];

  // All tools for the "View All Tools" section
  const allToolsPreview = [
    {
      title: "PDF en PowerPoint",
      description: "Transformez vos fichiers PDF en présentations PPT et PPTX faciles à éditer.",
      icon: <FileText className="w-6 h-6" />,
      link: "/pdf-vers-powerpoint",
      color: "from-orange-500 to-orange-600"
    },
    {
      title: t('tool.wordToPdf.title'),
      description: t('tool.wordToPdf.description'),
      icon: <FileText className="w-6 h-6" />,
      link: "/word-vers-pdf",
      color: "from-sky-500 to-sky-600"
    },
    {
      title: "PDF en Excel",
      description: "Transférez les données de fichiers PDF vers des feuilles de calcul Excel en quelques secondes.",
      icon: <FileText className="w-6 h-6" />,
      link: "/pdf-vers-excel",
      color: "from-emerald-500 to-emerald-600"
    },
    {
      title: "PDF en JPG",
      description: "Extraire toutes les images contenues dans un fichier PDF ou convertir chaque page dans un fichier JPG.",
      icon: <FileImage className="w-6 h-6" />,
      link: "/pdf-vers-jpg",
      color: "from-pink-500 to-pink-600"
    },
    {
      title: "OCR PDF",
      description: "Convertissez en toute simplicité vos PDF numérisés en documents indexables et modifiables.",
      icon: <Search className="w-6 h-6" />,
      link: "/ocr-pdf",
      color: "from-blue-500 to-blue-600"
    },
    {
      title: "Organiser PDF",
      description: "Triez les pages de votre fichier PDF comme bon vous semble. Supprimez ou ajoutez des pages PDF.",
      icon: <FolderOpen className="w-6 h-6" />,
      link: "/organiser-pdf",
      color: "from-stone-500 to-stone-600"
    },
    {
      title: "Déverrouiller PDF",
      description: "Retirez le mot de passe de sécurité du PDF, de sorte à ce que vous puissiez l'utiliser comme vous le souhaitez.",
      icon: <Unlock className="w-6 h-6" />,
      link: "/deverrouiller-pdf",
      color: "from-yellow-500 to-yellow-600"
    },
    {
      title: "HTML en PDF",
      description: "Convertissez des pages web HTML en PDF. Copiez-collez l'URL de la page qui vous intéresse.",
      icon: <Globe className="w-6 h-6" />,
      link: "/html-vers-pdf",
      color: "from-slate-500 to-slate-600"
    },
    {
      title: "Filigrane",
      description: "Choisissez une image ou un texte à appliquer sur votre PDF. Sélectionnez l'emplacement, la transparence et la typographie.",
      icon: <FileText className="w-6 h-6" />,
      link: "/filigrane-pdf",
      color: "from-lime-500 to-lime-600"
    },
    {
      title: "Réparer PDF",
      description: "Réparez un PDF endommagé et restaurez les données d'un PDF corrompu.",
      icon: <Wrench className="w-6 h-6" />,
      link: "/reparer-pdf",
      color: "from-neutral-500 to-neutral-600"
    },
    {
      title: "Comparer PDF",
      description: "Permet de comparer des documents côte à côte, en mettant facilement en évidence les changements.",
      icon: <Eye className="w-6 h-6" />,
      link: "/comparer-pdf",
      color: "from-green-500 to-green-600"
    },
    {
      title: "Censurer PDF",
      description: "Censurez le texte et les graphiques pour supprimer définitivement les informations sensibles d'un PDF.",
      icon: <Scissors className="w-6 h-6" />,
      link: "/censurer-pdf",
      color: "from-red-500 to-red-600"
    }
  ];

  const stats = [
    { number: "10M+", label: "Documents traités", icon: <FileText className="w-5 h-5" /> },
    { number: "500K+", label: "Utilisateurs actifs", icon: <Users className="w-5 h-5" /> },
    { number: "99.9%", label: "Temps de disponibilité", icon: <Zap className="w-5 h-5" /> },
    { number: "4.9/5", label: "Note utilisateurs", icon: <Star className="w-5 h-5" /> }
  ];

  const scrollToTools = () => {
    const toolsSection = document.getElementById('popular-tools');
    if (toolsSection) {
      toolsSection.scrollIntoView({ behavior: 'smooth' });
    }
  };

  const benefits = [
    {
      icon: <Shield className="w-8 h-8" />,
      title: "100% Sécurisé",
      description: "Vos fichiers sont chiffrés et supprimés automatiquement après traitement"
    },
    {
      icon: <Zap className="w-8 h-8" />,
      title: "Ultra Rapide",
      description: "Traitement en quelques secondes grâce à notre infrastructure cloud"
    },
    {
      icon: <Globe className="w-8 h-8" />,
      title: "Accessible Partout",
      description: "Fonctionne sur tous les appareils et navigateurs, aucune installation requise"
    }
  ];

  return (
    <div className="min-h-screen">
      <SEOHead
        title={t('landing.seo.title')}
        description={t('landing.seo.description')}
        keywords={t('landing.seo.keywords')}
        canonicalUrl="/"
        structuredData={{
          "@context": "https://schema.org",
          "@type": "WebApplication",
          "name": "PDFTools Pro",
          "description": t('landing.seo.description'),
          "applicationCategory": "BusinessApplication",
          "operatingSystem": "Web Browser",
          "offers": {
            "@type": "Offer",
            "price": "0",
            "priceCurrency": "USD"
          },
          "featureList": [
            "Merge PDF",
            "Split PDF", 
            "Compress PDF",
            "Convert PDF to Word",
            "Convert Word to PDF",
            "Edit PDF",
            "Sign PDF"
          ]
        }}
      />
      {/* Animated Background */}
      <div className="absolute inset-0 -z-10 overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-br from-blue-50 via-white to-purple-50"></div>
        <div className="absolute top-0 left-0 w-full h-full">
          <div className="absolute top-20 left-10 w-72 h-72 bg-blue-200 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-blob"></div>
          <div className="absolute top-40 right-10 w-72 h-72 bg-purple-200 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-blob animation-delay-2000"></div>
          <div className="absolute -bottom-8 left-20 w-72 h-72 bg-pink-200 rounded-full mix-blend-multiply filter blur-xl opacity-20 animate-blob animation-delay-4000"></div>
        </div>
      </div>

      {/* Hero Section */}
      <section className="relative pt-20 pb-32 px-4 sm:px-6 lg:px-8">
        <div className="max-w-7xl mx-auto">
          <div className="text-center">
            {/* Badge */}
            <div className="inline-flex items-center px-4 py-2 bg-blue-100 text-blue-800 rounded-full text-sm font-medium mb-8 animate-fade-in-up">
              <Star className="w-4 h-4 mr-2 text-yellow-500" />
              {t('landing.hero.badge')}
            </div>

            {/* Main Heading */}
            <h1 className="text-5xl sm:text-6xl lg:text-7xl font-bold mb-8 animate-fade-in-up animation-delay-200">
              <span className="bg-gradient-to-r from-blue-600 via-purple-600 to-blue-800 bg-clip-text text-transparent">
                {t('landing.hero.titlePart1')}
              </span>
              <br />
              <span className="text-slate-800">{t('landing.hero.titlePart2')}</span>
            </h1>

            {/* Subtitle */}
            <p className="text-xl sm:text-2xl text-slate-600 max-w-4xl mx-auto mb-12 leading-relaxed animate-fade-in-up animation-delay-400">
              {t('landing.hero.subtitle')}
            </p>

            {/* CTA Buttons */}
            <div className="flex flex-col sm:flex-row gap-6 justify-center mb-16 animate-fade-in-up animation-delay-600">
              <Link 
                to="/tools"
                className="group bg-gradient-to-r from-blue-600 to-purple-600 text-white px-10 py-5 rounded-2xl text-lg font-semibold hover:shadow-2xl hover:shadow-blue-500/25 transition-all duration-300 transform hover:scale-105 flex items-center justify-center"
              >
                <span>{t('landing.hero.startFree')}</span>
                <ArrowRight className="w-5 h-5 ml-2 group-hover:translate-x-1 transition-transform" />
              </Link>
              <button 
                onClick={scrollToTools}
                className="group border-2 border-slate-300 text-slate-700 px-10 py-5 rounded-2xl text-lg font-semibold hover:border-blue-600 hover:text-blue-600 hover:bg-blue-50 transition-all duration-300 flex items-center justify-center"
              >
                <FileText className="w-5 h-5 mr-2" />
                {t('landing.hero.discoverTools')}
              </button>
            </div>

            {/* Stats */}
            <div className="grid grid-cols-2 lg:grid-cols-4 gap-8 max-w-4xl mx-auto animate-fade-in-up animation-delay-800">
              {stats.map((stat, index) => (
                <div key={index} className="text-center">
                  <div className="flex items-center justify-center mb-2">
                    <div className="p-2 bg-blue-100 rounded-lg mr-2">
                      {stat.icon}
                    </div>
                  </div>
                  <div className="text-3xl font-bold text-slate-800 mb-1">{t(`landing.stats.${index}.number`)}</div>
                  <div className="text-sm text-slate-600">{t(`landing.stats.${index}.label`)}</div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Benefits Section */}
      <section className="py-20 px-4 sm:px-6 lg:px-8 bg-white/50 backdrop-blur-sm">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-4xl sm:text-5xl font-bold text-slate-800 mb-6">
              {language === 'fr' ? 'Pourquoi choisir PDFTools Pro ?' : 
               language === 'es' ? '¿Por qué elegir PDFTools Pro?' :
               language === 'de' ? 'Warum PDFTools Pro wählen?' :
               language === 'pt' ? 'Por que escolher PDFTools Pro?' :
               'Why choose PDFTools Pro?'}
            </h2>
            <p className="text-xl text-slate-600 max-w-3xl mx-auto">
              {language === 'fr' ? 'Une solution complète, sécurisée et performante pour tous vos besoins PDF' :
               language === 'es' ? 'Una solución completa, segura y eficiente para todas sus necesidades de PDF' :
               language === 'de' ? 'Eine vollständige, sichere und leistungsstarke Lösung für alle Ihre PDF-Bedürfnisse' :
               language === 'pt' ? 'Uma solução completa, segura e eficiente para todas as suas necessidades de PDF' :
               'A complete, secure and efficient solution for all your PDF needs'}
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {benefits.map((benefit, index) => (
              <div key={index} className="group text-center p-8 rounded-2xl bg-white shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2">
                <div className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-r from-blue-500 to-purple-600 rounded-2xl text-white mb-6 group-hover:scale-110 transition-transform">
                  {benefit.icon}
                </div>
                <h3 className="text-2xl font-bold text-slate-800 mb-4">
                  {language === 'fr' ? benefit.title :
                   language === 'es' ? (index === 0 ? '100% Seguro' : index === 1 ? 'Ultra Rápido' : 'Accesible en Todas Partes') :
                   language === 'de' ? (index === 0 ? '100% Sicher' : index === 1 ? 'Ultra Schnell' : 'Überall Zugänglich') :
                   language === 'pt' ? (index === 0 ? '100% Seguro' : index === 1 ? 'Ultra Rápido' : 'Acessível em Qualquer Lugar') :
                   (index === 0 ? '100% Secure' : index === 1 ? 'Ultra Fast' : 'Accessible Everywhere')}
                </h3>
                <p className="text-slate-600 leading-relaxed">
                  {language === 'fr' ? benefit.description :
                   language === 'es' ? (index === 0 ? 'Sus archivos están cifrados y se eliminan automáticamente después del procesamiento' : 
                                       index === 1 ? 'Procesamiento en segundos gracias a nuestra infraestructura en la nube' : 
                                       'Funciona en todos los dispositivos y navegadores, no requiere instalación') :
                   language === 'de' ? (index === 0 ? 'Ihre Dateien sind verschlüsselt und werden nach der Verarbeitung automatisch gelöscht' : 
                                       index === 1 ? 'Verarbeitung in Sekunden dank unserer Cloud-Infrastruktur' : 
                                       'Funktioniert auf allen Geräten und Browsern, keine Installation erforderlich') :
                   language === 'pt' ? (index === 0 ? 'Seus arquivos são criptografados e excluídos automaticamente após o processamento' : 
                                       index === 1 ? 'Processamento em segundos graças à nossa infraestrutura em nuvem' : 
                                       'Funciona em todos os dispositivos e navegadores, nenhuma instalação necessária') :
                   (index === 0 ? 'Your files are encrypted and automatically deleted after processing' : 
                    index === 1 ? 'Processing in seconds thanks to our cloud infrastructure' : 
                    'Works on all devices and browsers, no installation required')}
                </p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Tools Grid */}
      <section id="popular-tools" className="py-20 px-4 sm:px-6 lg:px-8">
        <div className="max-w-7xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-4xl sm:text-5xl font-bold text-slate-800 mb-4">
              {language === 'fr' ? 'Outils PDF les plus populaires' :
               language === 'es' ? 'Herramientas PDF más populares' :
               language === 'de' ? 'Beliebteste PDF-Tools' :
               language === 'pt' ? 'Ferramentas PDF mais populares' :
               'Most popular PDF tools'}
            </h2>
            <p className="text-xl text-slate-600 max-w-3xl mx-auto mb-8">
              {language === 'fr' ? 'Découvrez nos outils PDF les plus utilisés pour vos tâches quotidiennes' :
               language === 'es' ? 'Descubre nuestras herramientas PDF más utilizadas para tus tareas diarias' :
               language === 'de' ? 'Entdecken Sie unsere meistgenutzten PDF-Tools für Ihre täglichen Aufgaben' :
               language === 'pt' ? 'Descubra nossas ferramentas PDF mais usadas para suas tarefas diárias' :
               'Discover our most used PDF tools for your daily tasks'}
            </p>
            <Link 
              to="/tools"
              className="inline-flex items-center text-blue-600 hover:text-blue-700 font-medium"
            >
              {language === 'fr' ? 'Voir tous les outils' :
               language === 'es' ? 'Ver todas las herramientas' :
               language === 'de' ? 'Alle Tools anzeigen' :
               language === 'pt' ? 'Ver todas as ferramentas' :
               'View all tools'}
              <ArrowRight className="w-4 h-4 ml-1" />
            </Link>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6 mb-16">
            {popularTools.map((tool, index) => (
              <Link
                key={index}
                to={tool.link}
                className="group bg-white rounded-2xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-2 border border-slate-100 hover:border-blue-200"
              >
                <div className={`w-12 h-12 rounded-xl bg-gradient-to-r ${tool.color} flex items-center justify-center text-white mb-4 group-hover:scale-110 transition-transform`}>
                  {tool.icon}
                </div>
                <h3 className="text-lg font-semibold text-slate-800 mb-2 group-hover:text-blue-600 transition-colors">
                  {tool.title}
                </h3>
                <p className="text-slate-600 text-sm leading-relaxed line-clamp-3">
                  {tool.description}
                </p>
                <div className="flex items-center mt-4 text-blue-600 text-sm font-medium opacity-0 group-hover:opacity-100 transition-opacity">
                  {language === 'fr' ? 'Essayer maintenant' :
                   language === 'es' ? 'Probar ahora' :
                   language === 'de' ? 'Jetzt testen' :
                   language === 'pt' ? 'Experimentar agora' :
                   'Try now'}
                  <ArrowRight className="w-4 h-4 ml-1 group-hover:translate-x-1 transition-transform" />
                </div>
              </Link>
            ))}
          </div>

          {/* All Tools Preview */}
          <div className="bg-slate-50 rounded-3xl p-8">
            <div className="text-center mb-8">
              <h3 className="text-2xl font-bold text-slate-800 mb-2">
                {language === 'fr' ? 'Et bien plus encore...' :
                 language === 'es' ? 'Y mucho más...' :
                 language === 'de' ? 'Und vieles mehr...' :
                 language === 'pt' ? 'E muito mais...' :
                 'And much more...'}
              </h3>
              <p className="text-slate-600">
                {language === 'fr' ? 'Découvrez tous nos outils PDF pour répondre à tous vos besoins' :
                 language === 'es' ? 'Descubre todas nuestras herramientas PDF para satisfacer todas tus necesidades' :
                 language === 'de' ? 'Entdecken Sie alle unsere PDF-Tools für alle Ihre Bedürfnisse' :
                 language === 'pt' ? 'Descubra todas as nossas ferramentas PDF para atender a todas as suas necessidades' :
                 'Discover all our PDF tools to meet all your needs'}
              </p>
            </div>
            
            <div className="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4 mb-8">
              {allToolsPreview.map((tool, index) => (
                <Link
                  key={index}
                  to={tool.link}
                  className="group flex flex-col items-center p-4 bg-white rounded-xl hover:shadow-md transition-all duration-200 hover:-translate-y-1"
                >
                  <div className={`w-10 h-10 rounded-lg bg-gradient-to-r ${tool.color} flex items-center justify-center text-white mb-2 group-hover:scale-110 transition-transform`}>
                    {tool.icon}
                  </div>
                  <span className="text-xs font-medium text-slate-700 text-center leading-tight">
                    {tool.title}
                  </span>
                </Link>
              ))}
            </div>
            
            <div className="text-center">
              <Link 
                to="/tools"
                className="inline-flex items-center bg-gradient-to-r from-blue-600 to-purple-600 text-white px-8 py-4 rounded-xl text-lg font-medium hover:shadow-xl transition-all duration-200 transform hover:scale-105"
              >
                {language === 'fr' ? 'Voir tous les 27 outils' :
                 language === 'es' ? 'Ver las 27 herramientas' :
                 language === 'de' ? 'Alle 27 Tools anzeigen' :
                 language === 'pt' ? 'Ver todas as 27 ferramentas' :
                 'View all 27 tools'}
                <ArrowRight className="w-5 h-5 ml-2" />
              </Link>
            </div>
          </div>
        </div>
      </section>


      {/* FAQ Section */}
      <section className="py-20 px-4 sm:px-6 lg:px-8 bg-slate-50">
        <div className="max-w-4xl mx-auto">
          <div className="text-center mb-16">
            <h2 className="text-4xl sm:text-5xl font-bold text-slate-800 mb-6">
              {t('landing.faq.title')}
            </h2>
            <p className="text-xl text-slate-600 max-w-3xl mx-auto">
              {t('landing.faq.subtitle')}
            </p>
          </div>

          <div className="space-y-6">
            {[0, 1, 2, 3, 4, 5].map((index) => (
              <div key={index} className="bg-white rounded-2xl p-6 shadow-sm border border-slate-200">
                <h3 className="text-lg font-semibold text-slate-800 mb-3">
                  {t(`landing.faq.questions.${index}.question`)}
                </h3>
                <p className="text-slate-600 leading-relaxed">
                  {t(`landing.faq.questions.${index}.answer`)}
                </p>
              </div>
            ))}
          </div>
          
          <div className="text-center mt-12">
            <p className="text-slate-600 mb-4">
              {t('landing.faq.moreQuestions')}
            </p>
            <button className="bg-gradient-to-r from-blue-600 to-purple-600 text-white px-8 py-3 rounded-xl font-medium hover:shadow-lg transition-all duration-200">
              {t('landing.faq.contactUs')}
            </button>
          </div>
        </div>
      </section>

      {/* Final CTA */}
      <section className="py-20 px-4 sm:px-6 lg:px-8 bg-white">
        <div className="max-w-4xl mx-auto text-center">
          <h2 className="text-4xl sm:text-5xl font-bold text-slate-800 mb-6">
            {t('landing.cta.title')}
          </h2>
          <p className="text-xl text-slate-600 mb-12">
            {t('landing.cta.subtitle')}
          </p>
          
          <div className="flex flex-col sm:flex-row gap-6 justify-center">
            <Link 
              to="/tools"
              className="group bg-gradient-to-r from-blue-600 to-purple-600 text-white px-10 py-5 rounded-2xl text-lg font-semibold hover:shadow-2xl hover:shadow-blue-500/25 transition-all duration-300 transform hover:scale-105 flex items-center justify-center"
            >
              <span>{t('landing.cta.tryFree')}</span>
              <ArrowRight className="w-5 h-5 ml-2 group-hover:translate-x-1 transition-transform" />
            </Link>
            <button className="border-2 border-slate-300 text-slate-700 px-10 py-5 rounded-2xl text-lg font-semibold hover:border-blue-600 hover:text-blue-600 hover:bg-blue-50 transition-all duration-300">
              {t('landing.cta.learnMore')}
            </button>
          </div>

          <div className="mt-12 flex items-center justify-center space-x-8 text-sm text-slate-500">
            <div className="flex items-center">
              <CheckCircle className="w-4 h-4 text-green-500 mr-2" />
              {language === 'fr' ? 'Gratuit à vie' :
               language === 'es' ? 'Preguntas frecuentes' :
               language === 'de' ? 'Häufig gestellte Fragen' :
               language === 'pt' ? 'Perguntas frequentes' :
               'Free forever'}
            </div>
            <div className="flex items-center">
              <CheckCircle className="w-4 h-4 text-green-500 mr-2" />
              {language === 'fr' ? 'Aucune inscription' :
               language === 'es' ? 'Sin registro' :
               language === 'de' ? 'Keine Registrierung' :
               language === 'pt' ? 'Sem cadastro' :
               'No registration'}
            </div>
            <div className="flex items-center">
              <CheckCircle className="w-4 h-4 text-green-500 mr-2" />
              {language === 'fr' ? '100% sécurisé' :
               language === 'es' ? '100% seguro' :
               language === 'de' ? '100% sicher' :
               language === 'pt' ? '100% seguro' :
               '100% secure'}
            </div>
          </div>
        </div>
      </section>
    </div>
  );
};

export default LandingPage;