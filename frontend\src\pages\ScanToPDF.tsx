import React, { useState } from 'react';
import { Smartphone, Download, ArrowRight } from 'lucide-react';
import ToolLayout from '../components/ToolLayout';

const ScanToPDF = () => {
  const [isProcessing, setIsProcessing] = useState(false);
  const [scanQuality, setScanQuality] = useState<'standard' | 'high' | 'ultra'>('high');

  const handleScan = () => {
    setIsProcessing(true);
    // Simulate processing
    setTimeout(() => {
      setIsProcessing(false);
      alert('Scanner mobile activé avec succès!');
    }, 2000);
  };

  return (
    <ToolLayout
      title="Numériser au format PDF"
      description="Numérisez des documents avec votre mobile et envoyez-les vers votre navigateur en un clin d'oeil"
      icon={<Smartphone className="w-8 h-8" />}
      color="from-purple-500 to-purple-600"
    >
      <div className="space-y-6">
        <div className="bg-slate-50 p-6 rounded-xl">
          <h3 className="text-lg font-semibold text-slate-700 mb-4">
            Scanner mobile
          </h3>
          
          <div className="space-y-4">
            <div className="text-center">
              <div className="w-32 h-32 bg-purple-100 rounded-xl flex items-center justify-center mx-auto mb-4">
                <Smartphone className="w-16 h-16 text-purple-600" />
              </div>
              <p className="text-slate-600 mb-4">
                Scannez vos documents directement depuis votre appareil mobile
              </p>
              
              <div className="bg-white p-4 rounded-lg border-2 border-dashed border-purple-300">
                <div className="text-center">
                  <div className="text-2xl mb-2">📱</div>
                  <p className="text-sm text-slate-600">
                    Ouvrez la caméra de votre téléphone pour scanner
                  </p>
                </div>
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-slate-700 mb-2">
                Qualité de numérisation
              </label>
              <div className="space-y-2">
                <label className="flex items-center space-x-3">
                  <input
                    type="radio"
                    name="quality"
                    value="standard"
                    checked={scanQuality === 'standard'}
                    onChange={(e) => setScanQuality(e.target.value as 'standard')}
                    className="text-purple-600"
                  />
                  <span className="text-slate-700">Standard (plus rapide)</span>
                </label>
                <label className="flex items-center space-x-3">
                  <input
                    type="radio"
                    name="quality"
                    value="high"
                    checked={scanQuality === 'high'}
                    onChange={(e) => setScanQuality(e.target.value as 'high')}
                    className="text-purple-600"
                  />
                  <span className="text-slate-700">Haute qualité (recommandé)</span>
                </label>
                <label className="flex items-center space-x-3">
                  <input
                    type="radio"
                    name="quality"
                    value="ultra"
                    checked={scanQuality === 'ultra'}
                    onChange={(e) => setScanQuality(e.target.value as 'ultra')}
                    className="text-purple-600"
                  />
                  <span className="text-slate-700">Ultra haute qualité</span>
                </label>
              </div>
            </div>

            <div className="space-y-3">
              <div className="flex items-center space-x-3">
                <input
                  type="checkbox"
                  id="auto-crop"
                  defaultChecked
                  className="text-purple-600 rounded"
                />
                <label htmlFor="auto-crop" className="text-slate-700">
                  Recadrage automatique
                </label>
              </div>

              <div className="flex items-center space-x-3">
                <input
                  type="checkbox"
                  id="auto-enhance"
                  defaultChecked
                  className="text-purple-600 rounded"
                />
                <label htmlFor="auto-enhance" className="text-slate-700">
                  Amélioration automatique
                </label>
              </div>

              <div className="flex items-center space-x-3">
                <input
                  type="checkbox"
                  id="multi-page"
                  className="text-purple-600 rounded"
                />
                <label htmlFor="multi-page" className="text-slate-700">
                  Scan multi-pages
                </label>
              </div>
            </div>

            <div className="bg-blue-50 p-4 rounded-lg">
              <div className="flex items-center space-x-2">
                <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                <span className="text-sm text-blue-800 font-medium">Instructions</span>
              </div>
              <ol className="text-sm text-blue-700 mt-1 space-y-1">
                <li>1. Cliquez sur "Activer le scanner"</li>
                <li>2. Autorisez l'accès à la caméra</li>
                <li>3. Placez le document dans le cadre</li>
                <li>4. Appuyez sur capturer</li>
                <li>5. Téléchargez votre PDF</li>
              </ol>
            </div>
          </div>
        </div>

        <div className="flex justify-center">
          <button
            onClick={handleScan}
            disabled={isProcessing}
            className="bg-gradient-to-r from-purple-600 to-pink-600 text-white px-8 py-4 rounded-xl text-lg font-medium hover:shadow-xl transition-all duration-200 transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2"
          >
            {isProcessing ? (
              <>
                <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white"></div>
                <span>Activation du scanner...</span>
              </>
            ) : (
              <>
                <span>Activer le scanner</span>
                <ArrowRight className="w-5 h-5" />
              </>
            )}
          </button>
        </div>
      </div>
    </ToolLayout>
  );
};

export default ScanToPDF;