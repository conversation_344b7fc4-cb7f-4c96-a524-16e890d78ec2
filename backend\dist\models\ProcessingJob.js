"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ProcessingJob = void 0;
const sequelize_1 = require("sequelize");
const database_1 = require("../config/database");
const User_1 = require("./User");
class ProcessingJob extends sequelize_1.Model {
    markAsStarted() {
        this.status = 'processing';
        this.started_at = new Date();
    }
    markAsCompleted(outputFiles, processingTimeSeconds) {
        this.status = 'completed';
        this.output_files = outputFiles;
        this.processing_time_seconds = processingTimeSeconds;
        this.completed_at = new Date();
    }
    markAsFailed(errorMessage) {
        this.status = 'failed';
        this.error_message = errorMessage;
        this.completed_at = new Date();
    }
    getDuration() {
        if (this.started_at && this.completed_at) {
            return Math.round((this.completed_at.getTime() - this.started_at.getTime()) / 1000);
        }
        return null;
    }
}
exports.ProcessingJob = ProcessingJob;
ProcessingJob.init({
    id: {
        type: sequelize_1.DataTypes.UUID,
        defaultValue: sequelize_1.DataTypes.UUIDV4,
        primaryKey: true,
    },
    user_id: {
        type: sequelize_1.DataTypes.UUID,
        allowNull: false,
        references: {
            model: User_1.User,
            key: 'id',
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE',
    },
    tool_name: {
        type: sequelize_1.DataTypes.STRING(100),
        allowNull: false,
        validate: {
            isIn: [[
                    'repair_pdf', 'merge_pdf', 'split_pdf', 'compress_pdf',
                    'excel_to_pdf', 'word_to_pdf', 'powerpoint_to_pdf', 'html_to_pdf',
                    'pdf_to_excel', 'pdf_to_word', 'pdf_to_powerpoint', 'pdf_to_image',
                    'pdf_to_pdfa', 'ocr_pdf', 'sign_pdf', 'protect_pdf', 'unlock_pdf',
                    'watermark_pdf', 'rotate_pdf', 'crop_pdf', 'organize_pdf',
                    'page_numbers_pdf', 'redact_pdf', 'compare_pdf', 'edit_pdf',
                    'scan_to_pdf', 'image_to_pdf'
                ]],
        },
    },
    status: {
        type: sequelize_1.DataTypes.ENUM('pending', 'processing', 'completed', 'failed', 'cancelled'),
        allowNull: false,
        defaultValue: 'pending',
    },
    input_files: {
        type: sequelize_1.DataTypes.JSON,
        allowNull: false,
        validate: {
            isArray(value) {
                if (!Array.isArray(value)) {
                    throw new Error('input_files must be an array');
                }
            },
        },
    },
    output_files: {
        type: sequelize_1.DataTypes.JSON,
        allowNull: false,
        defaultValue: [],
        validate: {
            isArray(value) {
                if (!Array.isArray(value)) {
                    throw new Error('output_files must be an array');
                }
            },
        },
    },
    parameters: {
        type: sequelize_1.DataTypes.JSON,
        allowNull: true,
    },
    error_message: {
        type: sequelize_1.DataTypes.TEXT,
        allowNull: true,
    },
    processing_time_seconds: {
        type: sequelize_1.DataTypes.INTEGER,
        allowNull: true,
        validate: {
            min: 0,
        },
    },
    file_size_mb: {
        type: sequelize_1.DataTypes.DECIMAL(10, 2),
        allowNull: true,
        validate: {
            min: 0,
        },
    },
    started_at: {
        type: sequelize_1.DataTypes.DATE,
        allowNull: true,
    },
    completed_at: {
        type: sequelize_1.DataTypes.DATE,
        allowNull: true,
    },
}, {
    sequelize: database_1.sequelize,
    modelName: 'ProcessingJob',
    tableName: 'processing_jobs',
    timestamps: true,
    underscored: true,
    indexes: [
        {
            fields: ['user_id'],
        },
        {
            fields: ['tool_name'],
        },
        {
            fields: ['status'],
        },
        {
            fields: ['created_at'],
        },
        {
            fields: ['user_id', 'status'],
        },
        {
            fields: ['user_id', 'created_at'],
        },
    ],
});
exports.default = ProcessingJob;
//# sourceMappingURL=ProcessingJob.js.map