"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.sequelize = void 0;
exports.testConnection = testConnection;
exports.initializeDatabase = initializeDatabase;
exports.closeConnection = closeConnection;
const sequelize_1 = require("sequelize");
const logger_1 = require("../utils/logger");
console.log('Environment variables:');
console.log('DB_HOST:', process.env['DB_HOST']);
console.log('DB_USER:', process.env['DB_USER']);
console.log('DB_PASSWORD:', process.env['DB_PASSWORD'] ? '***SET***' : 'NOT SET');
console.log('DB_NAME:', process.env['DB_NAME']);
const dbConfig = {
    host: process.env['DB_HOST'] || 'localhost',
    port: parseInt(process.env['DB_PORT'] || '3306'),
    database: process.env['DB_NAME'] || 'pdf',
    username: process.env['DB_USER'] || 'root',
    password: process.env['DB_PASSWORD'] || '',
    dialect: 'mysql',
    logging: (sql) => {
        if (process.env['NODE_ENV'] === 'development') {
            logger_1.logger.debug('SQL Query', { sql });
        }
    },
    pool: {
        max: 10,
        min: 0,
        acquire: 30000,
        idle: 10000
    },
    define: {
        timestamps: true,
        underscored: true,
        freezeTableName: true
    }
};
exports.sequelize = new sequelize_1.Sequelize(dbConfig.database, dbConfig.username, dbConfig.password, {
    host: dbConfig.host,
    port: dbConfig.port,
    dialect: dbConfig.dialect,
    logging: dbConfig.logging,
    pool: dbConfig.pool,
    define: dbConfig.define,
    dialectOptions: {
        charset: 'utf8mb4',
        connectTimeout: 60000
    }
});
async function testConnection() {
    try {
        await exports.sequelize.authenticate();
        logger_1.logger.info('Database connection established successfully', {
            host: dbConfig.host,
            port: dbConfig.port,
            database: dbConfig.database
        });
        return true;
    }
    catch (error) {
        logger_1.logger.error('Unable to connect to database', {
            error: error instanceof Error ? error.message : String(error),
            host: dbConfig.host,
            port: dbConfig.port,
            database: dbConfig.database
        });
        return false;
    }
}
async function initializeDatabase() {
    try {
        const isConnected = await testConnection();
        if (!isConnected) {
            throw new Error('Failed to connect to database');
        }
        await exports.sequelize.sync({ alter: process.env['NODE_ENV'] === 'development' });
        logger_1.logger.info('Database initialized successfully');
    }
    catch (error) {
        logger_1.logger.error('Failed to initialize database', {
            error: error instanceof Error ? error.message : String(error)
        });
        throw error;
    }
}
async function closeConnection() {
    try {
        await exports.sequelize.close();
        logger_1.logger.info('Database connection closed');
    }
    catch (error) {
        logger_1.logger.error('Error closing database connection', {
            error: error instanceof Error ? error.message : String(error)
        });
    }
}
exports.default = exports.sequelize;
//# sourceMappingURL=database.js.map