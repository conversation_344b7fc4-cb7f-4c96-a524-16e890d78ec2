import { DataTypes, Model, Optional } from 'sequelize';
import { sequelize } from '../config/database';
import { User } from './User';

// Processing job attributes interface
export interface ProcessingJobAttributes {
  id: string;
  user_id: string;
  tool_name: string;
  status: 'pending' | 'processing' | 'completed' | 'failed' | 'cancelled';
  input_files: string[];
  output_files: string[];
  parameters?: object;
  error_message?: string;
  processing_time_seconds?: number;
  file_size_mb?: number;
  started_at?: Date;
  completed_at?: Date;
  created_at?: Date;
  updated_at?: Date;
}

// Processing job creation attributes
export interface ProcessingJobCreationAttributes extends Optional<ProcessingJobAttributes, 
  'id' | 'status' | 'output_files' | 'error_message' | 'processing_time_seconds' | 
  'file_size_mb' | 'started_at' | 'completed_at' | 'created_at' | 'updated_at'> {}

// Processing job model class
export class ProcessingJob extends Model<ProcessingJobAttributes, ProcessingJobCreationAttributes> 
  implements ProcessingJobAttributes {
  public id!: string;
  public user_id!: string;
  public tool_name!: string;
  public status!: 'pending' | 'processing' | 'completed' | 'failed' | 'cancelled';
  public input_files!: string[];
  public output_files!: string[];
  public parameters?: object;
  public error_message?: string;
  public processing_time_seconds?: number;
  public file_size_mb?: number;
  public started_at?: Date;
  public completed_at?: Date;

  // Timestamps
  public readonly created_at!: Date;
  public readonly updated_at!: Date;

  // Associations
  public readonly user?: User;

  // Instance methods
  public markAsStarted(): void {
    this.status = 'processing';
    this.started_at = new Date();
  }

  public markAsCompleted(outputFiles: string[], processingTimeSeconds: number): void {
    this.status = 'completed';
    this.output_files = outputFiles;
    this.processing_time_seconds = processingTimeSeconds;
    this.completed_at = new Date();
  }

  public markAsFailed(errorMessage: string): void {
    this.status = 'failed';
    this.error_message = errorMessage;
    this.completed_at = new Date();
  }

  public getDuration(): number | null {
    if (this.started_at && this.completed_at) {
      return Math.round((this.completed_at.getTime() - this.started_at.getTime()) / 1000);
    }
    return null;
  }
}

// Initialize ProcessingJob model
ProcessingJob.init(
  {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true,
    },
    user_id: {
      type: DataTypes.UUID,
      allowNull: false,
      references: {
        model: User,
        key: 'id',
      },
      onUpdate: 'CASCADE',
      onDelete: 'CASCADE',
    },
    tool_name: {
      type: DataTypes.STRING(100),
      allowNull: false,
      validate: {
        isIn: [[
          'repair_pdf', 'merge_pdf', 'split_pdf', 'compress_pdf',
          'excel_to_pdf', 'word_to_pdf', 'powerpoint_to_pdf', 'html_to_pdf',
          'pdf_to_excel', 'pdf_to_word', 'pdf_to_powerpoint', 'pdf_to_image',
          'pdf_to_pdfa', 'ocr_pdf', 'sign_pdf', 'protect_pdf', 'unlock_pdf',
          'watermark_pdf', 'rotate_pdf', 'crop_pdf', 'organize_pdf',
          'page_numbers_pdf', 'redact_pdf', 'compare_pdf', 'edit_pdf',
          'scan_to_pdf', 'image_to_pdf'
        ]],
      },
    },
    status: {
      type: DataTypes.ENUM('pending', 'processing', 'completed', 'failed', 'cancelled'),
      allowNull: false,
      defaultValue: 'pending',
    },
    input_files: {
      type: DataTypes.JSON,
      allowNull: false,
      validate: {
        isArray(value: any) {
          if (!Array.isArray(value)) {
            throw new Error('input_files must be an array');
          }
        },
      },
    },
    output_files: {
      type: DataTypes.JSON,
      allowNull: false,
      defaultValue: [],
      validate: {
        isArray(value: any) {
          if (!Array.isArray(value)) {
            throw new Error('output_files must be an array');
          }
        },
      },
    },
    parameters: {
      type: DataTypes.JSON,
      allowNull: true,
    },
    error_message: {
      type: DataTypes.TEXT,
      allowNull: true,
    },
    processing_time_seconds: {
      type: DataTypes.INTEGER,
      allowNull: true,
      validate: {
        min: 0,
      },
    },
    file_size_mb: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: true,
      validate: {
        min: 0,
      },
    },
    started_at: {
      type: DataTypes.DATE,
      allowNull: true,
    },
    completed_at: {
      type: DataTypes.DATE,
      allowNull: true,
    },
  },
  {
    sequelize,
    modelName: 'ProcessingJob',
    tableName: 'processing_jobs',
    timestamps: true,
    underscored: true,
    indexes: [
      {
        fields: ['user_id'],
      },
      {
        fields: ['tool_name'],
      },
      {
        fields: ['status'],
      },
      {
        fields: ['created_at'],
      },
      {
        fields: ['user_id', 'status'],
      },
      {
        fields: ['user_id', 'created_at'],
      },
    ],
  }
);

export default ProcessingJob;
