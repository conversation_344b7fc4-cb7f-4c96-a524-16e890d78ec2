"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ProcessingSession = void 0;
const sequelize_1 = require("sequelize");
const database_1 = require("../config/database");
const User_1 = require("./User");
class ProcessingSession extends sequelize_1.Model {
    isActive() {
        return this.status === 'active' && new Date() < this.expires_at;
    }
    markCompleted() {
        this.status = 'completed';
        this.can_download = true;
    }
    incrementUsage() {
        this.files_processed += 1;
        this.quota_consumed += 1;
    }
    canProcessMore(userQuota) {
        return this.isActive() && this.quota_consumed < userQuota;
    }
}
exports.ProcessingSession = ProcessingSession;
ProcessingSession.init({
    id: {
        type: sequelize_1.DataTypes.UUID,
        defaultValue: sequelize_1.DataTypes.UUIDV4,
        primaryKey: true,
    },
    user_id: {
        type: sequelize_1.DataTypes.UUID,
        allowNull: false,
        references: {
            model: User_1.User,
            key: 'id',
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE',
    },
    session_token: {
        type: sequelize_1.DataTypes.STRING(255),
        allowNull: false,
        unique: true,
    },
    status: {
        type: sequelize_1.DataTypes.ENUM('active', 'completed', 'expired'),
        allowNull: false,
        defaultValue: 'active',
    },
    files_processed: {
        type: sequelize_1.DataTypes.INTEGER,
        allowNull: false,
        defaultValue: 0,
        validate: {
            min: 0,
        },
    },
    quota_consumed: {
        type: sequelize_1.DataTypes.INTEGER,
        allowNull: false,
        defaultValue: 0,
        validate: {
            min: 0,
        },
    },
    can_download: {
        type: sequelize_1.DataTypes.BOOLEAN,
        allowNull: false,
        defaultValue: true,
    },
    expires_at: {
        type: sequelize_1.DataTypes.DATE,
        allowNull: false,
    },
}, {
    sequelize: database_1.sequelize,
    modelName: 'ProcessingSession',
    tableName: 'processing_sessions',
    timestamps: true,
    underscored: true,
    indexes: [
        {
            fields: ['user_id'],
        },
        {
            fields: ['session_token'],
            unique: true,
        },
        {
            fields: ['status'],
        },
        {
            fields: ['expires_at'],
        },
        {
            fields: ['user_id', 'status'],
        },
    ],
});
exports.default = ProcessingSession;
//# sourceMappingURL=ProcessingSession.js.map