"""
PDF crop tool for cropping pages to specific dimensions or removing margins.
"""
import os
from typing import List, Dict, Any, Optional, Tuple
from PyPDF2 import PdfWriter, PdfReader
import fitz  # PyMuPDF for advanced cropping
import structlog

from .base_tool import BasePDFTool, ProcessingError, ValidationError

logger = structlog.get_logger()


class PDFCropTool(BasePDFTool):
    """Tool for cropping PDF pages to specific dimensions."""
    
    def __init__(self):
        super().__init__("crop")
        
        # Crop modes
        self.crop_modes = {
            "margins": "Remove margins automatically",
            "custom": "Crop to custom dimensions",
            "center": "Crop to center area with specified size",
            "bbox": "Crop to bounding box of content",
            "preset": "Use preset crop dimensions"
        }
        
        # Preset crop sizes (in points, 72 points = 1 inch)
        self.presets = {
            "letter": (612, 792),      # 8.5 x 11 inches
            "a4": (595, 842),          # A4 size
            "legal": (612, 1008),      # 8.5 x 14 inches
            "tabloid": (792, 1224),    # 11 x 17 inches
            "square": (612, 612),      # Square format
            "business_card": (252, 144) # 3.5 x 2 inches
        }
    
    async def process(
        self,
        input_files: List[str],
        output_path: str,
        parameters: Optional[Dict[str, Any]] = None
    ) -> List[str]:
        """
        Crop PDF pages according to specified parameters.
        
        Args:
            input_files: List of PDF file paths to crop
            output_path: Output directory for cropped PDFs
            parameters: Crop parameters (mode, dimensions, margins, etc.)
            
        Returns:
            List containing paths to cropped PDF files
        """
        # Validate inputs
        if len(input_files) == 0:
            raise ValidationError("At least 1 PDF file is required for cropping")
        
        self.validate_input_files(input_files)
        self.validate_pdf_files(input_files)
        
        # Prepare parameters
        params = parameters or {}
        crop_mode = params.get("crop_mode", "margins")
        pages = params.get("pages", "all")  # "all", "odd", "even", or specific pages
        
        # Mode-specific parameters
        if crop_mode == "custom":
            crop_box = params.get("crop_box", [0, 0, 612, 792])  # [x, y, width, height]
        elif crop_mode == "margins":
            margin_threshold = params.get("margin_threshold", 10)  # points
        elif crop_mode == "center":
            center_width = params.get("center_width", 400)
            center_height = params.get("center_height", 600)
        elif crop_mode == "preset":
            preset_name = params.get("preset", "a4")
            if preset_name not in self.presets:
                raise ValidationError(f"Invalid preset: {preset_name}. Available: {list(self.presets.keys())}")
        
        # Validate parameters
        if crop_mode not in self.crop_modes:
            raise ValidationError(f"Invalid crop mode: {crop_mode}. Available: {list(self.crop_modes.keys())}")
        
        # Ensure output directory exists
        self.ensure_output_directory(output_path)
        
        output_files = []
        
        try:
            self.logger.info(
                "Starting PDF cropping",
                input_count=len(input_files),
                crop_mode=crop_mode,
                pages=pages
            )
            
            for i, input_file in enumerate(input_files):
                try:
                    self.logger.debug(f"Cropping file {i+1}/{len(input_files)}", file_path=input_file)
                    
                    # Generate output filename
                    base_name = os.path.splitext(os.path.basename(input_file))[0]
                    output_filename = f"{base_name}_cropped.pdf"
                    output_file = os.path.join(output_path, output_filename)
                    
                    # Crop the PDF
                    crop_result = await self._crop_pdf(
                        input_file,
                        output_file,
                        crop_mode,
                        pages,
                        params
                    )
                    
                    # Verify output file was created
                    if not os.path.exists(output_file):
                        raise ProcessingError("Failed to create cropped PDF file")
                    
                    output_size = self.get_file_size_mb(output_file)
                    input_size = self.get_file_size_mb(input_file)
                    
                    self.logger.info(
                        f"File {i+1} cropped successfully",
                        input_file=input_file,
                        output_file=output_file,
                        crop_mode=crop_mode,
                        pages_processed=crop_result["pages_processed"],
                        input_size_mb=round(input_size, 2),
                        output_size_mb=round(output_size, 2)
                    )
                    
                    output_files.append(output_file)
                    
                except Exception as e:
                    self.logger.error(f"Failed to crop file {i+1}", file_path=input_file, error=str(e))
                    # Clean up any partial output files
                    self.cleanup_files(output_files)
                    raise ProcessingError(f"Failed to crop {os.path.basename(input_file)}: {str(e)}")
            
            total_input_size = sum(self.get_file_size_mb(f) for f in input_files)
            total_output_size = sum(self.get_file_size_mb(f) for f in output_files)
            
            self.logger.info(
                "PDF cropping completed successfully",
                output_files=len(output_files),
                total_input_size_mb=round(total_input_size, 2),
                total_output_size_mb=round(total_output_size, 2)
            )
            
            return output_files
            
        except Exception as e:
            # Clean up any output files on error
            self.cleanup_files(output_files)
            
            if isinstance(e, (ValidationError, ProcessingError)):
                raise e
            else:
                raise ProcessingError(f"Unexpected error during PDF cropping: {str(e)}")
    
    async def _crop_pdf(
        self,
        input_file: str,
        output_file: str,
        crop_mode: str,
        pages: str,
        params: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Crop a PDF file according to the specified mode."""
        try:
            # Read the input PDF
            with open(input_file, 'rb') as pdf_file:
                pdf_reader = PdfReader(pdf_file)
                
                # Check if PDF is encrypted
                if pdf_reader.is_encrypted:
                    raise ProcessingError("Cannot crop encrypted PDF")
                
                total_pages = len(pdf_reader.pages)
                
                # Determine which pages to crop
                pages_to_crop = self._parse_pages_parameter(pages, total_pages)
                
                # Create PDF writer
                pdf_writer = PdfWriter()
                
                pages_processed = 0
                
                # Process each page
                for page_num in range(total_pages):
                    page = pdf_reader.pages[page_num]
                    
                    # Crop page if it's in the list
                    if (page_num + 1) in pages_to_crop:
                        if crop_mode == "margins":
                            cropped_page = await self._crop_margins(page, params.get("margin_threshold", 10))
                        elif crop_mode == "custom":
                            cropped_page = await self._crop_custom(page, params.get("crop_box", [0, 0, 612, 792]))
                        elif crop_mode == "center":
                            cropped_page = await self._crop_center(
                                page, 
                                params.get("center_width", 400),
                                params.get("center_height", 600)
                            )
                        elif crop_mode == "bbox":
                            cropped_page = await self._crop_bbox(page, input_file, page_num)
                        elif crop_mode == "preset":
                            preset_name = params.get("preset", "a4")
                            cropped_page = await self._crop_preset(page, preset_name)
                        else:
                            cropped_page = page
                        
                        pdf_writer.add_page(cropped_page)
                        pages_processed += 1
                        
                        self.logger.debug(f"Cropped page {page_num + 1}")
                    else:
                        # Add page without cropping
                        pdf_writer.add_page(page)
                
                # Copy metadata if available
                if pdf_reader.metadata:
                    pdf_writer.add_metadata(pdf_reader.metadata)
                
                # Write the cropped PDF
                with open(output_file, 'wb') as output_pdf:
                    pdf_writer.write(output_pdf)
                
                return {
                    "pages_processed": pages_processed,
                    "total_pages": total_pages,
                    "crop_mode": crop_mode
                }
                
        except Exception as e:
            raise ProcessingError(f"Failed to crop PDF: {str(e)}")
    
    async def _crop_margins(self, page, margin_threshold: float):
        """Crop page by removing margins."""
        try:
            # Get current page dimensions
            mediabox = page.mediabox
            
            # Calculate new crop box by removing margins
            left = float(mediabox.left) + margin_threshold
            bottom = float(mediabox.bottom) + margin_threshold
            right = float(mediabox.right) - margin_threshold
            top = float(mediabox.top) - margin_threshold
            
            # Ensure valid dimensions
            if right <= left or top <= bottom:
                self.logger.warning("Margin removal would result in invalid dimensions, skipping")
                return page
            
            # Set crop box
            page.cropbox.lower_left = (left, bottom)
            page.cropbox.upper_right = (right, top)
            
            return page
            
        except Exception as e:
            self.logger.warning(f"Failed to crop margins: {str(e)}")
            return page
    
    async def _crop_custom(self, page, crop_box: List[float]):
        """Crop page to custom dimensions."""
        try:
            x, y, width, height = crop_box
            
            # Set crop box
            page.cropbox.lower_left = (x, y)
            page.cropbox.upper_right = (x + width, y + height)
            
            return page
            
        except Exception as e:
            self.logger.warning(f"Failed to apply custom crop: {str(e)}")
            return page
    
    async def _crop_center(self, page, center_width: float, center_height: float):
        """Crop page to center area with specified dimensions."""
        try:
            # Get current page dimensions
            mediabox = page.mediabox
            page_width = float(mediabox.right) - float(mediabox.left)
            page_height = float(mediabox.top) - float(mediabox.bottom)
            
            # Calculate center position
            center_x = float(mediabox.left) + page_width / 2
            center_y = float(mediabox.bottom) + page_height / 2
            
            # Calculate crop box
            left = center_x - center_width / 2
            bottom = center_y - center_height / 2
            right = center_x + center_width / 2
            top = center_y + center_height / 2
            
            # Ensure crop box is within page bounds
            left = max(left, float(mediabox.left))
            bottom = max(bottom, float(mediabox.bottom))
            right = min(right, float(mediabox.right))
            top = min(top, float(mediabox.top))
            
            # Set crop box
            page.cropbox.lower_left = (left, bottom)
            page.cropbox.upper_right = (right, top)
            
            return page
            
        except Exception as e:
            self.logger.warning(f"Failed to crop center: {str(e)}")
            return page
    
    async def _crop_bbox(self, page, input_file: str, page_num: int):
        """Crop page to bounding box of content using PyMuPDF."""
        try:
            # Use PyMuPDF to get content bounding box
            doc = fitz.open(input_file)
            fitz_page = doc[page_num]
            
            # Get page content bounding box
            bbox = fitz_page.get_text("dict")
            
            if bbox and "blocks" in bbox:
                # Calculate bounding box of all content
                min_x = min_y = float('inf')
                max_x = max_y = float('-inf')
                
                for block in bbox["blocks"]:
                    if "bbox" in block:
                        block_bbox = block["bbox"]
                        min_x = min(min_x, block_bbox[0])
                        min_y = min(min_y, block_bbox[1])
                        max_x = max(max_x, block_bbox[2])
                        max_y = max(max_y, block_bbox[3])
                
                if min_x != float('inf'):
                    # Add small padding
                    padding = 10
                    min_x = max(0, min_x - padding)
                    min_y = max(0, min_y - padding)
                    max_x += padding
                    max_y += padding
                    
                    # Set crop box
                    page.cropbox.lower_left = (min_x, min_y)
                    page.cropbox.upper_right = (max_x, max_y)
            
            doc.close()
            return page
            
        except Exception as e:
            self.logger.warning(f"Failed to crop to bounding box: {str(e)}")
            return page
    
    async def _crop_preset(self, page, preset_name: str):
        """Crop page to preset dimensions."""
        try:
            if preset_name not in self.presets:
                return page
            
            preset_width, preset_height = self.presets[preset_name]
            
            # Get current page dimensions
            mediabox = page.mediabox
            page_width = float(mediabox.right) - float(mediabox.left)
            page_height = float(mediabox.top) - float(mediabox.bottom)
            
            # Center the preset size on the page
            center_x = float(mediabox.left) + page_width / 2
            center_y = float(mediabox.bottom) + page_height / 2
            
            left = center_x - preset_width / 2
            bottom = center_y - preset_height / 2
            right = center_x + preset_width / 2
            top = center_y + preset_height / 2
            
            # Set crop box
            page.cropbox.lower_left = (left, bottom)
            page.cropbox.upper_right = (right, top)
            
            return page
            
        except Exception as e:
            self.logger.warning(f"Failed to apply preset crop: {str(e)}")
            return page

    def _parse_pages_parameter(self, pages: str, total_pages: int) -> set:
        """Parse the pages parameter to get set of page numbers to crop."""
        if pages == "all":
            return set(range(1, total_pages + 1))
        elif pages == "odd":
            return set(range(1, total_pages + 1, 2))
        elif pages == "even":
            return set(range(2, total_pages + 1, 2))
        else:
            # Parse specific pages (e.g., "1,3,5-7,10")
            page_numbers = set()

            for part in pages.split(','):
                part = part.strip()

                if '-' in part:
                    # Range like "5-7"
                    start_str, end_str = part.split('-', 1)
                    start = int(start_str.strip())
                    end = int(end_str.strip())

                    if start < 1 or end > total_pages or start > end:
                        raise ValidationError(f"Invalid page range: {part}")

                    page_numbers.update(range(start, end + 1))
                else:
                    # Single page like "1"
                    page = int(part)

                    if page < 1 or page > total_pages:
                        raise ValidationError(f"Invalid page number: {page}")

                    page_numbers.add(page)

            return page_numbers

    def get_crop_options(self) -> Dict[str, Dict[str, Any]]:
        """Get available crop options."""
        return {
            "crop_mode": {
                "description": "Cropping mode",
                "type": "string",
                "options": list(self.crop_modes.keys()),
                "default": "margins",
                "mode_descriptions": self.crop_modes
            },
            "pages": {
                "description": "Pages to crop",
                "type": "string",
                "options": ["all", "odd", "even", "specific (e.g., 1,3,5-7)"],
                "default": "all"
            },
            "margin_threshold": {
                "description": "Margin size to remove (in points, for margins mode)",
                "type": "number",
                "min": 0,
                "max": 100,
                "default": 10
            },
            "crop_box": {
                "description": "Custom crop box [x, y, width, height] (for custom mode)",
                "type": "array",
                "default": [0, 0, 612, 792]
            },
            "center_width": {
                "description": "Width of center crop area (for center mode)",
                "type": "number",
                "min": 50,
                "max": 1000,
                "default": 400
            },
            "center_height": {
                "description": "Height of center crop area (for center mode)",
                "type": "number",
                "min": 50,
                "max": 1000,
                "default": 600
            },
            "preset": {
                "description": "Preset crop size (for preset mode)",
                "type": "string",
                "options": list(self.presets.keys()),
                "default": "a4",
                "preset_dimensions": self.presets
            }
        }

    def get_crop_modes(self) -> Dict[str, str]:
        """Get available crop modes."""
        return self.crop_modes.copy()

    def get_presets(self) -> Dict[str, Tuple[int, int]]:
        """Get available crop presets."""
        return self.presets.copy()

    async def analyze_page_dimensions(self, file_path: str) -> Dict[str, Any]:
        """Analyze page dimensions for crop planning."""
        try:
            with open(file_path, 'rb') as pdf_file:
                pdf_reader = PdfReader(pdf_file)

                if pdf_reader.is_encrypted:
                    return {
                        "filename": os.path.basename(file_path),
                        "error": "PDF is encrypted and cannot be analyzed"
                    }

                total_pages = len(pdf_reader.pages)
                page_dimensions = []

                # Analyze first 10 pages
                for page_num in range(min(total_pages, 10)):
                    page = pdf_reader.pages[page_num]

                    # Get page dimensions
                    mediabox = page.mediabox
                    width = float(mediabox.right) - float(mediabox.left)
                    height = float(mediabox.top) - float(mediabox.bottom)

                    # Convert to inches
                    width_inches = width / 72
                    height_inches = height / 72

                    # Determine orientation
                    orientation = "landscape" if width > height else "portrait"

                    page_dimensions.append({
                        "page": page_num + 1,
                        "width_points": round(width, 2),
                        "height_points": round(height, 2),
                        "width_inches": round(width_inches, 2),
                        "height_inches": round(height_inches, 2),
                        "orientation": orientation
                    })

                # Find most common dimensions
                dimension_counts = {}
                for dim in page_dimensions:
                    key = (dim["width_points"], dim["height_points"])
                    dimension_counts[key] = dimension_counts.get(key, 0) + 1

                most_common = max(dimension_counts.items(), key=lambda x: x[1]) if dimension_counts else None

                return {
                    "filename": os.path.basename(file_path),
                    "total_pages": total_pages,
                    "size_mb": round(self.get_file_size_mb(file_path), 2),
                    "page_dimensions": page_dimensions,
                    "most_common_size": {
                        "width_points": most_common[0][0],
                        "height_points": most_common[0][1],
                        "width_inches": round(most_common[0][0] / 72, 2),
                        "height_inches": round(most_common[0][1] / 72, 2),
                        "page_count": most_common[1]
                    } if most_common else None,
                    "has_mixed_sizes": len(dimension_counts) > 1,
                    "sample_note": f"Showing first {len(page_dimensions)} pages" if total_pages > 10 else None
                }

        except Exception as e:
            return {
                "filename": os.path.basename(file_path),
                "error": str(e)
            }

    async def preview_crop(
        self,
        page_width: float,
        page_height: float,
        crop_mode: str,
        **kwargs
    ) -> Dict[str, Any]:
        """Preview crop operation without actually cropping."""
        try:
            original_area = page_width * page_height

            if crop_mode == "margins":
                margin = kwargs.get("margin_threshold", 10)
                new_width = page_width - (2 * margin)
                new_height = page_height - (2 * margin)
                crop_x = margin
                crop_y = margin

            elif crop_mode == "custom":
                crop_box = kwargs.get("crop_box", [0, 0, 400, 600])
                crop_x, crop_y, new_width, new_height = crop_box

            elif crop_mode == "center":
                new_width = kwargs.get("center_width", 400)
                new_height = kwargs.get("center_height", 600)
                crop_x = (page_width - new_width) / 2
                crop_y = (page_height - new_height) / 2

            elif crop_mode == "preset":
                preset_name = kwargs.get("preset", "a4")
                if preset_name in self.presets:
                    new_width, new_height = self.presets[preset_name]
                    crop_x = (page_width - new_width) / 2
                    crop_y = (page_height - new_height) / 2
                else:
                    raise ValidationError(f"Invalid preset: {preset_name}")

            else:
                raise ValidationError(f"Unsupported crop mode: {crop_mode}")

            # Ensure valid dimensions
            if new_width <= 0 or new_height <= 0:
                raise ValidationError("Crop would result in invalid dimensions")

            new_area = new_width * new_height
            area_reduction = ((original_area - new_area) / original_area) * 100

            return {
                "crop_mode": crop_mode,
                "original_dimensions": {
                    "width": page_width,
                    "height": page_height,
                    "area": original_area
                },
                "cropped_dimensions": {
                    "width": new_width,
                    "height": new_height,
                    "area": new_area,
                    "x_offset": crop_x,
                    "y_offset": crop_y
                },
                "area_reduction_percent": round(area_reduction, 2),
                "is_valid": True
            }

        except Exception as e:
            return {
                "crop_mode": crop_mode,
                "error": str(e),
                "is_valid": False
            }


# Create tool instance
crop_pdf_tool = PDFCropTool()
