// Debug script for compression issues
const axios = require('axios');
const FormData = require('form-data');
const fs = require('fs');

const BASE_URL = 'http://localhost:3001/api';

async function debugCompressionIssue() {
  console.log('🔍 Debugging Compression Issue...\n');

  try {
    // Test 1: Check quota first
    console.log('1. Testing quota check...');
    const quotaResponse = await axios.post(`${BASE_URL}/pdf-tools/check-quota`, {
      toolName: 'compress_pdf'
    });

    if (quotaResponse.data.success) {
      console.log('✅ Quota check successful');
      console.log(`   Remaining quota: ${quotaResponse.data.data.remainingQuota.files} files`);
      console.log(`   Session token: ${quotaResponse.data.data.sessionToken.substring(0, 8)}...`);

      // Test 2: Create a test PDF file
      console.log('\n2. Creating test PDF file...');
      const testPdfContent = Buffer.from('%PDF-1.4\n1 0 obj\n<<\n/Type /Catalog\n/Pages 2 0 R\n>>\nendobj\n2 0 obj\n<<\n/Type /Pages\n/Kids [3 0 R]\n/Count 1\n>>\nendobj\n3 0 obj\n<<\n/Type /Page\n/Parent 2 0 R\n/MediaBox [0 0 612 792]\n>>\nendobj\nxref\n0 4\n0000000000 65535 f \n0000000009 00000 n \n0000000074 00000 n \n0000000120 00000 n \ntrailer\n<<\n/Size 4\n/Root 1 0 R\n>>\nstartxref\n179\n%%EOF');
      
      console.log(`✅ Test PDF created (${testPdfContent.length} bytes)`);

      // Test 3: Try compression with detailed logging
      console.log('\n3. Testing compression...');
      
      const formData = new FormData();
      formData.append('files', testPdfContent, {
        filename: 'test.pdf',
        contentType: 'application/pdf'
      });
      formData.append('toolName', 'compress_pdf');
      formData.append('sessionToken', quotaResponse.data.data.sessionToken);
      formData.append('parameters', JSON.stringify({
        quality: 'medium',
        optimize_images: true
      }));

      console.log('📤 Sending request with:');
      console.log(`   Tool: compress_pdf`);
      console.log(`   Session: ${quotaResponse.data.data.sessionToken.substring(0, 8)}...`);
      console.log(`   File: test.pdf (application/pdf)`);
      console.log(`   Parameters: {"quality":"medium","optimize_images":true}`);

      try {
        const processResponse = await axios.post(`${BASE_URL}/pdf-tools/process`, formData, {
          headers: formData.getHeaders(),
          timeout: 30000 // 30 second timeout
        });

        if (processResponse.data.success) {
          console.log('✅ Compression successful!');
          console.log(`   Output files: ${processResponse.data.data.outputFiles.length}`);
          console.log(`   Processing time: ${processResponse.data.data.processingTime}ms`);
        } else {
          console.log('❌ Compression failed (success=false)');
          console.log(`   Error: ${processResponse.data.error.message}`);
        }

      } catch (processError) {
        console.log('❌ Compression request failed');
        console.log(`   Status: ${processError.response?.status}`);
        console.log(`   Status Text: ${processError.response?.statusText}`);
        
        if (processError.response?.data) {
          console.log(`   Error Response:`, processError.response.data);
        } else {
          console.log(`   Error Message: ${processError.message}`);
        }

        // Additional debugging for 422 errors
        if (processError.response?.status === 422) {
          console.log('\n🔍 422 Error Analysis:');
          console.log('   This usually means validation failed');
          console.log('   Possible causes:');
          console.log('   - File type validation failed');
          console.log('   - Missing required fields');
          console.log('   - Invalid parameters format');
          console.log('   - Session token validation failed');
        }
      }

    } else {
      console.log('❌ Quota check failed');
      console.log(`   Error: ${quotaResponse.data.error.message}`);
    }

    // Test 4: Check backend logs
    console.log('\n4. Backend Log Analysis:');
    console.log('   Check the backend terminal for detailed error logs');
    console.log('   Look for validation errors or processing failures');

  } catch (error) {
    console.error('\n❌ Debug test failed:', error.response?.data || error.message);
  }
}

// Run the debug test
debugCompressionIssue();
