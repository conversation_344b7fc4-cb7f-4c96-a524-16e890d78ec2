"""
PDF to PDF/A conversion tool for creating archival-quality PDFs.
"""
import os
import tempfile
from typing import List, Dict, Any, Optional
import structlog

from .base_tool import BasePDFTool, ProcessingError, ValidationError

logger = structlog.get_logger()


class PDFToPDFATool(BasePDFTool):
    """Tool for converting PDF files to PDF/A archival format."""
    
    def __init__(self):
        super().__init__("pdf_to_pdfa")
        
        # PDF/A conformance levels
        self.conformance_levels = {
            "pdfa-1b": "PDF/A-1b (Basic conformance)",
            "pdfa-1a": "PDF/A-1a (Full conformance)",
            "pdfa-2b": "PDF/A-2b (Basic conformance)",
            "pdfa-2a": "PDF/A-2a (Full conformance)",
            "pdfa-2u": "PDF/A-2u (Unicode conformance)",
            "pdfa-3b": "PDF/A-3b (Basic conformance)",
            "pdfa-3a": "PDF/A-3a (Full conformance)",
            "pdfa-3u": "PDF/A-3u (Unicode conformance)"
        }
        
        # Color profiles
        self.color_profiles = {
            "srgb": "sRGB color profile",
            "cmyk": "CMYK color profile",
            "grayscale": "Grayscale color profile"
        }
    
    async def process(
        self,
        input_files: List[str],
        output_path: str,
        parameters: Optional[Dict[str, Any]] = None
    ) -> List[str]:
        """
        Convert PDF files to PDF/A archival format.
        
        Args:
            input_files: List of PDF file paths to convert
            output_path: Output directory for PDF/A files
            parameters: Conversion parameters (conformance level, color profile, etc.)
            
        Returns:
            List containing paths to converted PDF/A files
        """
        # Validate inputs
        if len(input_files) == 0:
            raise ValidationError("At least 1 PDF file is required for PDF/A conversion")
        
        self.validate_input_files(input_files)
        self.validate_pdf_files(input_files)
        
        # Prepare parameters
        params = parameters or {}
        conformance_level = params.get("conformance_level", "pdfa-2b")
        color_profile = params.get("color_profile", "srgb")
        embed_fonts = params.get("embed_fonts", True)
        optimize_images = params.get("optimize_images", True)
        
        # Validate parameters
        if conformance_level not in self.conformance_levels:
            raise ValidationError(f"Invalid conformance level: {conformance_level}. Available: {list(self.conformance_levels.keys())}")
        
        if color_profile not in self.color_profiles:
            raise ValidationError(f"Invalid color profile: {color_profile}. Available: {list(self.color_profiles.keys())}")
        
        # Ensure output directory exists
        self.ensure_output_directory(output_path)
        
        output_files = []
        
        try:
            self.logger.info(
                "Starting PDF to PDF/A conversion",
                input_count=len(input_files),
                conformance_level=conformance_level,
                color_profile=color_profile,
                embed_fonts=embed_fonts
            )
            
            for i, input_file in enumerate(input_files):
                try:
                    self.logger.debug(f"Converting file {i+1}/{len(input_files)}", file_path=input_file)
                    
                    # Generate output filename
                    base_name = os.path.splitext(os.path.basename(input_file))[0]
                    output_filename = f"{base_name}_pdfa.pdf"
                    output_file = os.path.join(output_path, output_filename)
                    
                    # Convert the PDF
                    conversion_result = await self._convert_pdf_to_pdfa(
                        input_file,
                        output_file,
                        conformance_level,
                        color_profile,
                        embed_fonts,
                        optimize_images
                    )
                    
                    # Verify output file was created
                    if not os.path.exists(output_file):
                        raise ProcessingError("Failed to create PDF/A file")
                    
                    output_size = self.get_file_size_mb(output_file)
                    input_size = self.get_file_size_mb(input_file)
                    
                    self.logger.info(
                        f"File {i+1} converted successfully",
                        input_file=input_file,
                        output_file=output_file,
                        conformance_level=conformance_level,
                        input_size_mb=round(input_size, 2),
                        output_size_mb=round(output_size, 2),
                        validation_status=conversion_result.get("validation_status", "unknown")
                    )
                    
                    output_files.append(output_file)
                    
                except Exception as e:
                    self.logger.error(f"Failed to convert file {i+1}", file_path=input_file, error=str(e))
                    # Clean up any partial output files
                    self.cleanup_files(output_files)
                    raise ProcessingError(f"Failed to convert {os.path.basename(input_file)}: {str(e)}")
            
            total_input_size = sum(self.get_file_size_mb(f) for f in input_files)
            total_output_size = sum(self.get_file_size_mb(f) for f in output_files)
            
            self.logger.info(
                "PDF to PDF/A conversion completed successfully",
                input_files=len(input_files),
                output_files=len(output_files),
                total_input_size_mb=round(total_input_size, 2),
                total_output_size_mb=round(total_output_size, 2)
            )
            
            return output_files
            
        except Exception as e:
            # Clean up any output files on error
            self.cleanup_files(output_files)
            
            if isinstance(e, (ValidationError, ProcessingError)):
                raise e
            else:
                raise ProcessingError(f"Unexpected error during PDF/A conversion: {str(e)}")
    
    async def _convert_pdf_to_pdfa(
        self,
        input_file: str,
        output_file: str,
        conformance_level: str,
        color_profile: str,
        embed_fonts: bool,
        optimize_images: bool
    ) -> Dict[str, Any]:
        """Convert a single PDF file to PDF/A."""
        try:
            # Try different conversion methods based on availability
            
            # Method 1: Try Ghostscript (most reliable for PDF/A)
            try:
                return await self._convert_with_ghostscript(
                    input_file, output_file, conformance_level, color_profile, embed_fonts, optimize_images
                )
            except Exception as e:
                self.logger.warning(f"Ghostscript conversion failed: {str(e)}")
            
            # Method 2: Try PyMuPDF with PDF/A settings
            try:
                return await self._convert_with_pymupdf(
                    input_file, output_file, conformance_level, embed_fonts
                )
            except Exception as e:
                self.logger.warning(f"PyMuPDF conversion failed: {str(e)}")
            
            # Method 3: Basic conversion with metadata updates
            return await self._convert_basic_pdfa(
                input_file, output_file, conformance_level
            )
            
        except Exception as e:
            raise ProcessingError(f"Failed to convert PDF to PDF/A: {str(e)}")
    
    async def _convert_with_ghostscript(
        self,
        input_file: str,
        output_file: str,
        conformance_level: str,
        color_profile: str,
        embed_fonts: bool,
        optimize_images: bool
    ) -> Dict[str, Any]:
        """Convert using Ghostscript for proper PDF/A compliance."""
        import subprocess
        import shutil
        
        # Check if Ghostscript is available
        gs_cmd = None
        for cmd in ['gs', 'gswin64c', 'gswin32c']:
            if shutil.which(cmd):
                gs_cmd = cmd
                break
        
        if not gs_cmd:
            raise ProcessingError("Ghostscript not found in system PATH")
        
        # Build Ghostscript command
        cmd = [
            gs_cmd,
            '-dPDFA=2',  # PDF/A-2 by default
            '-dBATCH',
            '-dNOPAUSE',
            '-dUseCIEColor=true',
            '-dPDFACompatibilityPolicy=1',
            '-sDEVICE=pdfwrite',
            f'-sOutputFile={output_file}'
        ]
        
        # Add conformance level specific settings
        if conformance_level.startswith('pdfa-1'):
            cmd[1] = '-dPDFA=1'
        elif conformance_level.startswith('pdfa-3'):
            cmd[1] = '-dPDFA=3'
        
        # Add color profile settings
        if color_profile == "srgb":
            cmd.extend(['-sColorConversionStrategy=sRGB'])
        elif color_profile == "cmyk":
            cmd.extend(['-sColorConversionStrategy=CMYK'])
        elif color_profile == "grayscale":
            cmd.extend(['-sColorConversionStrategy=Gray'])
        
        # Add font embedding
        if embed_fonts:
            cmd.extend(['-dEmbedAllFonts=true', '-dSubsetFonts=true'])
        
        # Add image optimization
        if optimize_images:
            cmd.extend(['-dAutoRotatePages=/None', '-dCompressFonts=true'])
        
        cmd.append(input_file)
        
        # Run Ghostscript
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=120)
        
        if result.returncode != 0:
            raise ProcessingError(f"Ghostscript conversion failed: {result.stderr}")
        
        return {
            "conversion_method": "ghostscript",
            "conformance_level": conformance_level,
            "validation_status": "compliant"
        }
    
    async def _convert_with_pymupdf(
        self,
        input_file: str,
        output_file: str,
        conformance_level: str,
        embed_fonts: bool
    ) -> Dict[str, Any]:
        """Convert using PyMuPDF with PDF/A settings."""
        try:
            import fitz
            
            # Open PDF
            doc = fitz.open(input_file)
            
            if doc.needs_pass:
                doc.close()
                raise ProcessingError("Cannot convert password-protected PDF")
            
            # Set PDF/A metadata
            metadata = doc.metadata or {}
            metadata.update({
                "/Producer": "PDF/A Converter",
                "/Creator": "PDF/A Conversion Tool",
                "/ModDate": fitz.get_pdf_now(),
                "/Trapped": "/False"
            })
            
            # Save with PDF/A settings
            save_options = {
                "garbage": 4,
                "clean": True,
                "deflate": True,
                "linear": True
            }
            
            if embed_fonts:
                save_options["expand"] = 255  # Embed all fonts
            
            doc.save(output_file, **save_options)
            doc.close()
            
            return {
                "conversion_method": "pymupdf",
                "conformance_level": conformance_level,
                "validation_status": "basic_compliance"
            }
            
        except Exception as e:
            raise ProcessingError(f"PyMuPDF conversion failed: {str(e)}")
    
    async def _convert_basic_pdfa(
        self,
        input_file: str,
        output_file: str,
        conformance_level: str
    ) -> Dict[str, Any]:
        """Basic PDF/A conversion with metadata updates."""
        try:
            from PyPDF2 import PdfWriter, PdfReader
            
            with open(input_file, 'rb') as infile:
                reader = PdfReader(infile)
                writer = PdfWriter()
                
                # Copy all pages
                for page in reader.pages:
                    writer.add_page(page)
                
                # Add PDF/A metadata
                metadata = reader.metadata or {}
                metadata.update({
                    "/Producer": "PDF/A Converter",
                    "/Creator": "PDF/A Conversion Tool",
                    "/Subject": f"PDF/A document - {conformance_level}",
                    "/Keywords": "PDF/A, archival, long-term preservation"
                })
                
                writer.add_metadata(metadata)
                
                # Write PDF/A file
                with open(output_file, 'wb') as outfile:
                    writer.write(outfile)
            
            return {
                "conversion_method": "basic_metadata",
                "conformance_level": conformance_level,
                "validation_status": "metadata_only"
            }
            
        except Exception as e:
            raise ProcessingError(f"Basic PDF/A conversion failed: {str(e)}")
    
    def get_conversion_options(self) -> Dict[str, Dict[str, Any]]:
        """Get available conversion options."""
        return {
            "conformance_level": {
                "description": "PDF/A conformance level",
                "type": "string",
                "options": list(self.conformance_levels.keys()),
                "default": "pdfa-2b",
                "level_descriptions": self.conformance_levels
            },
            "color_profile": {
                "description": "Color profile for conversion",
                "type": "string",
                "options": list(self.color_profiles.keys()),
                "default": "srgb",
                "profile_descriptions": self.color_profiles
            },
            "embed_fonts": {
                "description": "Embed all fonts in the PDF/A file",
                "type": "boolean",
                "default": True
            },
            "optimize_images": {
                "description": "Optimize images for archival storage",
                "type": "boolean",
                "default": True
            }
        }
    
    def get_conformance_levels(self) -> Dict[str, str]:
        """Get available PDF/A conformance levels."""
        return self.conformance_levels.copy()
    
    def get_color_profiles(self) -> Dict[str, str]:
        """Get available color profiles."""
        return self.color_profiles.copy()
    
    async def validate_pdfa_compliance(self, file_path: str) -> Dict[str, Any]:
        """Validate PDF/A compliance of a file."""
        try:
            import fitz
            
            doc = fitz.open(file_path)
            
            if doc.needs_pass:
                doc.close()
                return {"error": "Cannot validate password-protected PDF"}
            
            metadata = doc.metadata or {}
            
            # Basic compliance checks
            compliance_checks = {
                "has_metadata": bool(metadata),
                "has_producer": "/Producer" in metadata,
                "has_creator": "/Creator" in metadata,
                "page_count": len(doc),
                "file_size_mb": round(os.path.getsize(file_path) / (1024 * 1024), 2)
            }
            
            # Check for embedded fonts (simplified)
            font_info = []
            for page_num in range(min(len(doc), 5)):  # Check first 5 pages
                page = doc[page_num]
                fonts = page.get_fonts()
                font_info.extend(fonts)
            
            compliance_checks["fonts_found"] = len(font_info)
            compliance_checks["embedded_fonts"] = len([f for f in font_info if len(f) > 3 and f[3]])
            
            doc.close()
            
            # Determine likely compliance level
            if compliance_checks["has_metadata"] and compliance_checks["embedded_fonts"] > 0:
                likely_compliance = "Likely PDF/A compliant"
            else:
                likely_compliance = "May not be PDF/A compliant"
            
            return {
                "filename": os.path.basename(file_path),
                "compliance_checks": compliance_checks,
                "likely_compliance": likely_compliance,
                "metadata": metadata
            }
            
        except Exception as e:
            return {
                "filename": os.path.basename(file_path),
                "error": str(e)
            }


# Create tool instance
pdf_to_pdfa_tool = PDFToPDFATool()
