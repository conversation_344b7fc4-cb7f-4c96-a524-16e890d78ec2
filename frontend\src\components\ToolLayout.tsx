import React from 'react';
import { Link } from 'react-router-dom';
import { useLocation } from 'react-router-dom';
import { ArrowLeft, Upload, Download, AlertCircle, Crown } from 'lucide-react';
import { useLanguage } from '../contexts/LanguageContext';
import { useAuth } from '../contexts/AuthContext';
import { useUsage } from '../contexts/UsageContext';
import SEOHead from './SEOHead';
import { getToolSEO } from '../utils/seoData';

interface ToolLayoutProps {
  title: string;
  description: string;
  icon: React.ReactNode;
  children: React.ReactNode;
  color?: string;
  isPremiumOnly?: boolean;
}

const ToolLayout: React.FC<ToolLayoutProps> = ({ 
  title, 
  description, 
  icon, 
  children, 
  color = "from-blue-500 to-purple-600",
  isPremiumOnly = false
}) => {
  const location = useLocation();
  const { t, language } = useLanguage();
  const { isAuthenticated, user } = useAuth();
  const { canUseTools, getRemainingUsage, incrementUsage } = useUsage();

  // Premium-only tools require authentication
  if (isPremiumOnly && !isAuthenticated) {
    return (
      <>
        <SEOHead
          title={title}
          description={description}
          canonicalUrl={location.pathname}
          structuredData={{
            "@context": "https://schema.org",
            "@type": "WebApplication",
            "name": `${title} - PDFTools Pro`,
            "description": description,
            "applicationCategory": "BusinessApplication",
            "operatingSystem": "Web Browser"
          }}
        />
      <div className="min-h-screen py-8 px-4 sm:px-6 lg:px-8">
        <div className="max-w-4xl mx-auto">
          <div className="mb-8">
            <Link 
              to="/" 
              className="inline-flex items-center text-slate-600 hover:text-blue-600 transition-colors mb-6"
            >
              <ArrowLeft className="w-5 h-5 mr-2" />
              {t('action.backToTools')}
            </Link>
          </div>

          <div className="bg-white rounded-xl shadow-lg border border-slate-200 p-8 text-center">
            <div className="w-16 h-16 bg-yellow-100 rounded-full flex items-center justify-center mx-auto mb-6">
              <Crown className="w-8 h-8 text-yellow-600" />
            </div>
            <h2 className="text-2xl font-bold text-slate-800 mb-4">Outil Premium</h2>
            <p className="text-slate-600 mb-8">
              Cet outil nécessite un compte Premium. 
              Créez un compte et passez Premium pour y accéder !
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link 
                to="/signup"
                className="bg-gradient-to-r from-yellow-500 to-orange-500 text-white px-8 py-3 rounded-lg font-medium hover:shadow-lg transition-all duration-200"
              >
                Découvrir Premium
              </Link>
              <Link 
                to="/signin"
                className="border border-slate-300 text-slate-700 px-8 py-3 rounded-lg font-medium hover:bg-slate-50 transition-all duration-200"
              >
                Se connecter
              </Link>
            </div>
          </div>
        </div>
      </div>
      </>
    );
  }

  // For non-premium tools, check usage quota for all users except premium
  if (!isPremiumOnly && !(isAuthenticated && user?.subscriptionPlan === 'premium') && !canUseTools()) {
    return (
      <>
        <SEOHead
          title={title}
          description={description}
          canonicalUrl={location.pathname}
          structuredData={{
            "@context": "https://schema.org",
            "@type": "WebApplication",
            "name": `${title} - PDFTools Pro`,
            "description": description,
            "applicationCategory": "BusinessApplication",
            "operatingSystem": "Web Browser"
          }}
        />
      <div className="min-h-screen py-8 px-4 sm:px-6 lg:px-8">
        <div className="max-w-4xl mx-auto">
          <div className="mb-8">
            <Link 
              to="/" 
              className="inline-flex items-center text-slate-600 hover:text-blue-600 transition-colors mb-6"
            >
              <ArrowLeft className="w-5 h-5 mr-2" />
              {t('action.backToTools')}
            </Link>
          </div>

          <div className="bg-white rounded-xl shadow-lg border border-slate-200 p-8 text-center">
            <div className="w-16 h-16 bg-orange-100 rounded-full flex items-center justify-center mx-auto mb-6">
              <AlertCircle className="w-8 h-8 text-orange-600" />
            </div>
            <h2 className="text-2xl font-bold text-slate-800 mb-4">{t('usage.dailyQuotaReached')}</h2>
            <p className="text-slate-600 mb-8">
              {t('usage.quotaMessage')}
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link 
                to="/signup"
                className="bg-gradient-to-r from-blue-600 to-purple-600 text-white px-8 py-3 rounded-lg font-medium hover:shadow-lg transition-all duration-200"
              >
                {t('usage.createFreeAccount')}
              </Link>
              <Link 
                to="/tools"
                className="border border-slate-300 text-slate-700 px-8 py-3 rounded-lg font-medium hover:bg-slate-50 transition-all duration-200"
              >
                {t('usage.backToTools')}
              </Link>
            </div>
          </div>
        </div>
      </div>
      </>
    );
  }

  // Premium-only tools require premium plan
  if (isPremiumOnly && isAuthenticated && user?.subscriptionPlan !== 'premium') {
    return (
      <>
        <SEOHead
          title={title}
          description={description}
          canonicalUrl={location.pathname}
          structuredData={{
            "@context": "https://schema.org",
            "@type": "WebApplication",
            "name": `${title} - PDFTools Pro`,
            "description": description,
            "applicationCategory": "BusinessApplication",
            "operatingSystem": "Web Browser"
          }}
        />
      <div className="min-h-screen py-8 px-4 sm:px-6 lg:px-8">
        <div className="max-w-4xl mx-auto">
          <div className="mb-8">
            <Link 
              to="/" 
              className="inline-flex items-center text-slate-600 hover:text-blue-600 transition-colors mb-6"
            >
              <ArrowLeft className="w-5 h-5 mr-2" />
              {t('action.backToTools')}
            </Link>
          </div>

          <div className="bg-white rounded-xl shadow-lg border border-slate-200 p-8 text-center">
            <div className="w-16 h-16 bg-yellow-100 rounded-full flex items-center justify-center mx-auto mb-6">
              <Crown className="w-8 h-8 text-yellow-600" />
            </div>
            <h2 className="text-2xl font-bold text-slate-800 mb-4">Premium requis</h2>
            <p className="text-slate-600 mb-8">
              Cet outil est réservé aux utilisateurs Premium. 
              Passez Premium pour accéder à tous les outils avancés !
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <button className="bg-gradient-to-r from-yellow-500 to-orange-500 text-white px-8 py-3 rounded-lg font-medium hover:shadow-lg transition-all duration-200">
                Passer Premium - 9,99€/mois
              </button>
              <Link 
                to="/tools"
                className="border border-slate-300 text-slate-700 px-8 py-3 rounded-lg font-medium hover:bg-slate-50 transition-all duration-200"
              >
                Retour aux outils
              </Link>
            </div>
          </div>
        </div>
      </div>
      </>
    );
  }

  // Get SEO data for this tool
  const toolPath = location.pathname.replace('/', '');
  const seoData = getToolSEO(toolPath, language);

  return (
    <>
      <SEOHead
        title={seoData?.title || title}
        description={seoData?.description || description}
        keywords={seoData?.keywords}
        canonicalUrl={location.pathname}
        structuredData={{
          "@context": "https://schema.org",
          "@type": "WebApplication",
          "name": `${title} - PDFTools Pro`,
          "description": description,
          "applicationCategory": "BusinessApplication",
          "operatingSystem": "Web Browser",
          "offers": {
            "@type": "Offer",
            "price": "0",
            "priceCurrency": "USD"
          }
        }}
      />
    <div className="min-h-screen py-8 px-4 sm:px-6 lg:px-8">
      <div className="max-w-4xl mx-auto">
        <div className="mb-8">
          <Link 
            to="/" 
            className="inline-flex items-center text-slate-600 hover:text-blue-600 transition-colors mb-6"
          >
            <ArrowLeft className="w-5 h-5 mr-2" />
            {t('action.backToTools')}
          </Link>
          
          <div className="flex items-center mb-4">
            <div className={`w-16 h-16 rounded-xl bg-gradient-to-r ${color} flex items-center justify-center text-white mr-4`}>
              {icon}
            </div>
            <div>
              <h1 className="text-3xl font-bold text-slate-800">{title}</h1>
              <p className="text-slate-600 mt-1">{description}</p>
              {!isAuthenticated && !isPremiumOnly && (
                <p className="text-sm text-blue-600 mt-1">
                  {getRemainingUsage()} {t('usage.remainingUses')}
                </p>
              )}
              {isAuthenticated && user?.subscriptionPlan === 'free' && !isPremiumOnly && (
                <p className="text-sm text-green-600 mt-1">
                  {getRemainingUsage()} {t('usage.remainingUsesAuth')}
                </p>
              )}
              {isAuthenticated && user?.subscriptionPlan === 'premium' && !isPremiumOnly && (
                <p className="text-sm text-purple-600 mt-1">
                  {t('usage.unlimitedUses')}
                </p>
              )}
              {isPremiumOnly && (
                <div className="flex items-center space-x-2 mt-2">
                  <Crown className="w-4 h-4 text-yellow-500" />
                  <span className="text-sm text-yellow-600 font-medium">Outil Premium</span>
                </div>
              )}
            </div>
          </div>
        </div>

        <div className="bg-white rounded-xl shadow-lg border border-slate-200 p-8">
          {children}
        </div>

        <div className="mt-8 text-center">
          <p className="text-slate-500 text-sm">
            {t('upload.security')}
          </p>
        </div>
      </div>
    </div>
    </>
  );
};

export default ToolLayout;