import React, { useState } from 'react';
import { FileText, Download, ArrowRight } from 'lucide-react';
import ToolLayout from '../components/ToolLayout';
import FileUpload from '../components/FileUpload';

const PDFToExcel = () => {
  const [files, setFiles] = useState<File[]>([]);
  const [isProcessing, setIsProcessing] = useState(false);
  const [outputFormat, setOutputFormat] = useState<'xlsx' | 'xls'>('xlsx');

  const handleFileSelect = (selectedFiles: File[]) => {
    setFiles(selectedFiles);
  };

  const handleConvert = () => {
    if (files.length === 0) return;
    
    setIsProcessing(true);
    // Simulate processing
    setTimeout(() => {
      setIsProcessing(false);
      alert('PDF converti en Excel avec succès!');
    }, 2000);
  };

  return (
    <ToolLayout
      title="PDF en Excel"
      description="Transférez les données de fichiers PDF vers des feuilles de calcul Excel en quelques secondes"
      icon={<FileText className="w-8 h-8" />}
      color="from-emerald-500 to-emerald-600"
    >
      <div className="space-y-6">
        <FileUpload
          onFileSelect={handleFileSelect}
          accept=".pdf"
          multiple={false}
          maxFiles={1}
          title="Sélectionnez votre fichier PDF"
          description="Glissez-déposez un fichier PDF ici ou cliquez pour sélectionner"
        />

        {files.length > 0 && (
          <div className="bg-slate-50 p-6 rounded-xl">
            <h3 className="text-lg font-semibold text-slate-700 mb-4">
              Format de sortie
            </h3>
            
            <div className="space-y-4">
              <label className="flex items-center space-x-3">
                <input
                  type="radio"
                  name="format"
                  value="xlsx"
                  checked={outputFormat === 'xlsx'}
                  onChange={(e) => setOutputFormat(e.target.value as 'xlsx')}
                  className="text-emerald-600"
                />
                <div>
                  <span className="text-slate-700 font-medium">XLSX (recommandé)</span>
                  <p className="text-sm text-slate-500">Compatible avec Excel 2007 et versions ultérieures</p>
                </div>
              </label>

              <label className="flex items-center space-x-3">
                <input
                  type="radio"
                  name="format"
                  value="xls"
                  checked={outputFormat === 'xls'}
                  onChange={(e) => setOutputFormat(e.target.value as 'xls')}
                  className="text-emerald-600"
                />
                <div>
                  <span className="text-slate-700 font-medium">XLS</span>
                  <p className="text-sm text-slate-500">Compatible avec les anciennes versions d'Excel</p>
                </div>
              </label>
            </div>
          </div>
        )}

        {files.length > 0 && (
          <div className="flex justify-center">
            <button
              onClick={handleConvert}
              disabled={isProcessing}
              className="bg-gradient-to-r from-emerald-600 to-green-600 text-white px-8 py-4 rounded-xl text-lg font-medium hover:shadow-xl transition-all duration-200 transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2"
            >
              {isProcessing ? (
                <>
                  <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white"></div>
                  <span>Conversion en cours...</span>
                </>
              ) : (
                <>
                  <span>Convertir en Excel</span>
                  <ArrowRight className="w-5 h-5" />
                </>
              )}
            </button>
          </div>
        )}
      </div>
    </ToolLayout>
  );
};

export default PDFToExcel;