"""
Word to PDF conversion tool.
"""
import os
import tempfile
from typing import List, Dict, Any, Optional
import structlog

from .base_tool import <PERSON><PERSON><PERSON><PERSON>, ProcessingError, ValidationError

logger = structlog.get_logger()


class WordToPDFTool(BasePDFTool):
    """Tool for converting Word documents to PDF format."""
    
    def __init__(self):
        super().__init__("word_to_pdf")
        
        # Supported input formats
        self.supported_formats = {
            ".docx": "Word Document (Office 2007+)",
            ".doc": "Word Document (Legacy)",
            ".rtf": "Rich Text Format",
            ".odt": "OpenDocument Text"
        }
    
    def validate_input_files(self, input_files: List[str]) -> bool:
        """Validate that input files are Word documents."""
        for file_path in input_files:
            if not os.path.exists(file_path):
                raise FileNotFoundError(f"Input file not found: {file_path}")
            
            if not os.access(file_path, os.R_OK):
                raise PermissionError(f"Cannot read input file: {file_path}")
            
            # Check if file is a Word document
            file_ext = os.path.splitext(file_path)[1].lower()
            if file_ext not in self.supported_formats:
                raise ValidationError(f"Unsupported Word format: {file_ext}")
        
        return True
    
    async def process(
        self,
        input_files: List[str],
        output_path: str,
        parameters: Optional[Dict[str, Any]] = None
    ) -> List[str]:
        """
        Convert Word documents to PDF format.
        
        Args:
            input_files: List of Word document paths to convert
            output_path: Output directory for PDF files
            parameters: Conversion parameters (quality, layout, etc.)
            
        Returns:
            List containing paths to converted PDF files
        """
        # Validate inputs
        if len(input_files) == 0:
            raise ValidationError("At least 1 Word document is required for conversion")
        
        self.validate_input_files(input_files)
        
        # Prepare parameters
        params = parameters or {}
        quality = params.get("quality", "high")
        preserve_layout = params.get("preserve_layout", True)
        include_metadata = params.get("include_metadata", True)
        
        # Ensure output directory exists
        self.ensure_output_directory(output_path)
        
        output_files = []
        
        try:
            self.logger.info(
                "Starting Word to PDF conversion",
                input_count=len(input_files),
                quality=quality,
                preserve_layout=preserve_layout
            )
            
            for i, input_file in enumerate(input_files):
                try:
                    self.logger.debug(f"Converting file {i+1}/{len(input_files)}", file_path=input_file)
                    
                    # Generate output filename
                    base_name = os.path.splitext(os.path.basename(input_file))[0]
                    output_filename = f"{base_name}.pdf"
                    output_file = os.path.join(output_path, output_filename)
                    
                    # Convert the Word document
                    await self._convert_word_to_pdf(
                        input_file,
                        output_file,
                        quality,
                        preserve_layout,
                        include_metadata
                    )
                    
                    # Verify output file was created
                    if not os.path.exists(output_file):
                        raise ProcessingError("Failed to create PDF file")
                    
                    output_size = self.get_file_size_mb(output_file)
                    input_size = self.get_file_size_mb(input_file)
                    
                    self.logger.info(
                        f"File {i+1} converted successfully",
                        input_file=input_file,
                        output_file=output_file,
                        input_size_mb=round(input_size, 2),
                        output_size_mb=round(output_size, 2)
                    )
                    
                    output_files.append(output_file)
                    
                except Exception as e:
                    self.logger.error(f"Failed to convert file {i+1}", file_path=input_file, error=str(e))
                    # Clean up any partial output files
                    self.cleanup_files(output_files)
                    raise ProcessingError(f"Failed to convert {os.path.basename(input_file)}: {str(e)}")
            
            total_input_size = sum(self.get_file_size_mb(f) for f in input_files)
            total_output_size = sum(self.get_file_size_mb(f) for f in output_files)
            
            self.logger.info(
                "Word to PDF conversion completed successfully",
                input_files=len(input_files),
                output_files=len(output_files),
                total_input_size_mb=round(total_input_size, 2),
                total_output_size_mb=round(total_output_size, 2)
            )
            
            return output_files
            
        except Exception as e:
            # Clean up any output files on error
            self.cleanup_files(output_files)
            
            if isinstance(e, (ValidationError, ProcessingError)):
                raise e
            else:
                raise ProcessingError(f"Unexpected error during Word to PDF conversion: {str(e)}")
    
    async def _convert_word_to_pdf(
        self,
        input_file: str,
        output_file: str,
        quality: str,
        preserve_layout: bool,
        include_metadata: bool
    ):
        """Convert a single Word document to PDF."""
        try:
            # Try different conversion methods based on availability
            
            # Method 1: Try python-docx2pdf (if available)
            try:
                from docx2pdf import convert
                convert(input_file, output_file)
                return
            except ImportError:
                self.logger.debug("docx2pdf not available, trying alternative methods")
            except Exception as e:
                self.logger.warning(f"docx2pdf conversion failed: {str(e)}")
            
            # Method 2: Try LibreOffice headless conversion
            try:
                await self._convert_with_libreoffice(input_file, output_file)
                return
            except Exception as e:
                self.logger.warning(f"LibreOffice conversion failed: {str(e)}")
            
            # Method 3: Try python-docx + reportlab (for .docx files only)
            if input_file.lower().endswith('.docx'):
                try:
                    await self._convert_docx_with_reportlab(
                        input_file, output_file, preserve_layout, include_metadata
                    )
                    return
                except Exception as e:
                    self.logger.warning(f"python-docx conversion failed: {str(e)}")
            
            # If all methods fail
            raise ProcessingError("No suitable conversion method available. Please install LibreOffice or docx2pdf.")
            
        except Exception as e:
            raise ProcessingError(f"Failed to convert Word document: {str(e)}")
    
    async def _convert_with_libreoffice(self, input_file: str, output_file: str):
        """Convert using LibreOffice headless mode."""
        import subprocess
        import shutil
        
        # Check if LibreOffice is available
        libreoffice_cmd = None
        for cmd in ['libreoffice', 'soffice']:
            if shutil.which(cmd):
                libreoffice_cmd = cmd
                break
        
        if not libreoffice_cmd:
            raise ProcessingError("LibreOffice not found in system PATH")
        
        # Create temporary directory for output
        with tempfile.TemporaryDirectory() as temp_dir:
            # Run LibreOffice conversion
            cmd = [
                libreoffice_cmd,
                '--headless',
                '--convert-to', 'pdf',
                '--outdir', temp_dir,
                input_file
            ]
            
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=60)
            
            if result.returncode != 0:
                raise ProcessingError(f"LibreOffice conversion failed: {result.stderr}")
            
            # Find the generated PDF file
            base_name = os.path.splitext(os.path.basename(input_file))[0]
            temp_pdf = os.path.join(temp_dir, f"{base_name}.pdf")
            
            if not os.path.exists(temp_pdf):
                raise ProcessingError("LibreOffice did not generate expected PDF file")
            
            # Move to final location
            shutil.move(temp_pdf, output_file)
    
    async def _convert_docx_with_reportlab(
        self,
        input_file: str,
        output_file: str,
        preserve_layout: bool,
        include_metadata: bool
    ):
        """Convert DOCX using python-docx + reportlab (basic conversion)."""
        try:
            from docx import Document
            from reportlab.pdfgen import canvas
            from reportlab.lib.pagesizes import letter
            from reportlab.lib.units import inch
            
            # Read Word document
            doc = Document(input_file)
            
            # Create PDF
            c = canvas.Canvas(output_file, pagesize=letter)
            width, height = letter
            
            y_position = height - inch
            line_height = 14
            
            # Add metadata if requested
            if include_metadata:
                try:
                    core_props = doc.core_properties
                    if core_props.title:
                        c.setTitle(core_props.title)
                    if core_props.author:
                        c.setAuthor(core_props.author)
                    if core_props.subject:
                        c.setSubject(core_props.subject)
                except Exception:
                    pass
            
            # Process paragraphs
            for paragraph in doc.paragraphs:
                if paragraph.text.strip():
                    # Simple text extraction (no formatting preservation in this basic version)
                    text = paragraph.text.strip()
                    
                    # Word wrap for long lines
                    max_chars = 80
                    words = text.split()
                    lines = []
                    current_line = ""
                    
                    for word in words:
                        if len(current_line + " " + word) <= max_chars:
                            current_line += " " + word if current_line else word
                        else:
                            if current_line:
                                lines.append(current_line)
                            current_line = word
                    
                    if current_line:
                        lines.append(current_line)
                    
                    # Add lines to PDF
                    for line in lines:
                        if y_position < inch:
                            c.showPage()
                            y_position = height - inch
                        
                        c.drawString(inch, y_position, line)
                        y_position -= line_height
                    
                    # Add extra space after paragraph
                    y_position -= line_height / 2
            
            c.save()
            
        except ImportError:
            raise ProcessingError("python-docx and reportlab are required for DOCX conversion")
        except Exception as e:
            raise ProcessingError(f"Failed to convert DOCX with python-docx: {str(e)}")
    
    def get_conversion_options(self) -> Dict[str, Dict[str, Any]]:
        """Get available conversion options."""
        return {
            "quality": {
                "description": "PDF output quality",
                "type": "string",
                "options": ["low", "medium", "high"],
                "default": "high"
            },
            "preserve_layout": {
                "description": "Attempt to preserve document layout",
                "type": "boolean",
                "default": True
            },
            "include_metadata": {
                "description": "Include document metadata in PDF",
                "type": "boolean",
                "default": True
            }
        }
    
    def get_supported_formats(self) -> Dict[str, str]:
        """Get supported input formats."""
        return self.supported_formats.copy()
    
    async def check_conversion_support(self) -> Dict[str, Any]:
        """Check what conversion methods are available."""
        support_info = {
            "docx2pdf": False,
            "libreoffice": False,
            "python_docx": False,
            "recommended_method": None,
            "installation_notes": []
        }
        
        # Check docx2pdf
        try:
            import docx2pdf
            support_info["docx2pdf"] = True
        except ImportError:
            support_info["installation_notes"].append("Install docx2pdf: pip install docx2pdf")
        
        # Check LibreOffice
        import shutil
        if shutil.which('libreoffice') or shutil.which('soffice'):
            support_info["libreoffice"] = True
        else:
            support_info["installation_notes"].append("Install LibreOffice for best compatibility")
        
        # Check python-docx
        try:
            import docx
            import reportlab
            support_info["python_docx"] = True
        except ImportError:
            support_info["installation_notes"].append("Install python-docx and reportlab: pip install python-docx reportlab")
        
        # Determine recommended method
        if support_info["docx2pdf"]:
            support_info["recommended_method"] = "docx2pdf"
        elif support_info["libreoffice"]:
            support_info["recommended_method"] = "libreoffice"
        elif support_info["python_docx"]:
            support_info["recommended_method"] = "python_docx"
        else:
            support_info["recommended_method"] = "none"
        
        return support_info


# Create tool instance
word_to_pdf_tool = WordToPDFTool()
