import React, { useState } from 'react';
import { Scissors, Download, ArrowRight } from 'lucide-react';
import ToolLayout from '../components/ToolLayout';
import FileUpload from '../components/FileUpload';

const RedactPDF = () => {
  const [files, setFiles] = useState<File[]>([]);
  const [isProcessing, setIsProcessing] = useState(false);
  const [redactionType, setRedactionType] = useState<'manual' | 'automatic' | 'pattern'>('manual');

  const handleFileSelect = (selectedFiles: File[]) => {
    setFiles(selectedFiles);
  };

  const handleRedact = () => {
    if (files.length === 0) return;
    
    setIsProcessing(true);
    // Simulate processing
    setTimeout(() => {
      setIsProcessing(false);
      alert('Éditeur de censure PDF ouvert avec succès!');
    }, 2000);
  };

  return (
    <ToolLayout
      title="Censurer PDF"
      description="Censurez le texte et les graphiques pour supprimer définitivement les informations sensibles d'un PDF"
      icon={<Scissors className="w-8 h-8" />}
      color="from-red-500 to-red-600"
    >
      <div className="space-y-6">
        <FileUpload
          onFileSelect={handleFileSelect}
          accept=".pdf"
          multiple={false}
          maxFiles={1}
          title="Sélectionnez votre fichier PDF"
          description="Glissez-déposez un fichier PDF ici ou cliquez pour sélectionner"
        />

        {files.length > 0 && (
          <div className="bg-slate-50 p-6 rounded-xl">
            <h3 className="text-lg font-semibold text-slate-700 mb-4">
              Options de censure
            </h3>
            
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-slate-700 mb-2">
                  Type de censure
                </label>
                <div className="space-y-2">
                  <label className="flex items-center space-x-3">
                    <input
                      type="radio"
                      name="redactionType"
                      value="manual"
                      checked={redactionType === 'manual'}
                      onChange={(e) => setRedactionType(e.target.value as 'manual')}
                      className="text-red-600"
                    />
                    <div>
                      <span className="text-slate-700 font-medium">Censure manuelle</span>
                      <p className="text-sm text-slate-500">Sélectionnez manuellement les zones à censurer</p>
                    </div>
                  </label>
                  <label className="flex items-center space-x-3">
                    <input
                      type="radio"
                      name="redactionType"
                      value="automatic"
                      checked={redactionType === 'automatic'}
                      onChange={(e) => setRedactionType(e.target.value as 'automatic')}
                      className="text-red-600"
                    />
                    <div>
                      <span className="text-slate-700 font-medium">Censure automatique</span>
                      <p className="text-sm text-slate-500">Détection automatique d'informations sensibles</p>
                    </div>
                  </label>
                  <label className="flex items-center space-x-3">
                    <input
                      type="radio"
                      name="redactionType"
                      value="pattern"
                      checked={redactionType === 'pattern'}
                      onChange={(e) => setRedactionType(e.target.value as 'pattern')}
                      className="text-red-600"
                    />
                    <div>
                      <span className="text-slate-700 font-medium">Censure par motif</span>
                      <p className="text-sm text-slate-500">Recherche et censure par mots-clés ou expressions</p>
                    </div>
                  </label>
                </div>
              </div>

              {redactionType === 'automatic' && (
                <div className="space-y-3">
                  <div className="flex items-center space-x-3">
                    <input
                      type="checkbox"
                      id="redact-emails"
                      defaultChecked
                      className="text-red-600 rounded"
                    />
                    <label htmlFor="redact-emails" className="text-slate-700">
                      Adresses e-mail
                    </label>
                  </div>

                  <div className="flex items-center space-x-3">
                    <input
                      type="checkbox"
                      id="redact-phones"
                      defaultChecked
                      className="text-red-600 rounded"
                    />
                    <label htmlFor="redact-phones" className="text-slate-700">
                      Numéros de téléphone
                    </label>
                  </div>

                  <div className="flex items-center space-x-3">
                    <input
                      type="checkbox"
                      id="redact-ssn"
                      defaultChecked
                      className="text-red-600 rounded"
                    />
                    <label htmlFor="redact-ssn" className="text-slate-700">
                      Numéros de sécurité sociale
                    </label>
                  </div>

                  <div className="flex items-center space-x-3">
                    <input
                      type="checkbox"
                      id="redact-credit-cards"
                      defaultChecked
                      className="text-red-600 rounded"
                    />
                    <label htmlFor="redact-credit-cards" className="text-slate-700">
                      Numéros de carte de crédit
                    </label>
                  </div>
                </div>
              )}

              {redactionType === 'pattern' && (
                <div>
                  <label className="block text-sm font-medium text-slate-700 mb-2">
                    Mots-clés à censurer (séparés par des virgules)
                  </label>
                  <textarea
                    placeholder="Ex: confidentiel, secret, privé, nom de société"
                    className="w-full p-3 border border-slate-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-red-500 h-20"
                  />
                </div>
              )}

              <div className="bg-yellow-50 p-4 rounded-lg">
                <div className="flex items-center space-x-2">
                  <div className="w-2 h-2 bg-yellow-500 rounded-full"></div>
                  <span className="text-sm text-yellow-800 font-medium">Attention</span>
                </div>
                <p className="text-sm text-yellow-700 mt-1">
                  La censure est définitive et irréversible. Assurez-vous de conserver une copie de sauvegarde de votre document original.
                </p>
              </div>

              <div className="bg-blue-50 p-4 rounded-lg">
                <div className="flex items-center space-x-2">
                  <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                  <span className="text-sm text-blue-800 font-medium">Outils de censure</span>
                </div>
                <ul className="text-sm text-blue-700 mt-1 space-y-1">
                  <li>• Rectangle noir pour masquer le texte</li>
                  <li>• Suppression définitive du contenu</li>
                  <li>• Préservation de la mise en page</li>
                  <li>• Aperçu avant application</li>
                </ul>
              </div>
            </div>
          </div>
        )}

        {files.length > 0 && (
          <div className="flex justify-center">
            <button
              onClick={handleRedact}
              disabled={isProcessing}
              className="bg-gradient-to-r from-red-600 to-rose-600 text-white px-8 py-4 rounded-xl text-lg font-medium hover:shadow-xl transition-all duration-200 transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2"
            >
              {isProcessing ? (
                <>
                  <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white"></div>
                  <span>Ouverture de l'éditeur...</span>
                </>
              ) : (
                <>
                  <span>Ouvrir l'éditeur de censure</span>
                  <ArrowRight className="w-5 h-5" />
                </>
              )}
            </button>
          </div>
        )}
      </div>
    </ToolLayout>
  );
};

export default RedactPDF;