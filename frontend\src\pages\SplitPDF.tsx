import React, { useState } from 'react';
import { Split, Download, ArrowRight } from 'lucide-react';
import { useUsage } from '../contexts/UsageContext';
import ToolLayout from '../components/ToolLayout';
import FileUpload from '../components/FileUpload';

const SplitPDF = () => {
  const [files, setFiles] = useState<File[]>([]);
  const [isProcessing, setIsProcessing] = useState(false);
  const [splitOptions, setSplitOptions] = useState<'all' | 'range' | 'specific'>('all');
  const [pageRange, setPageRange] = useState('');
  const [specificPages, setSpecificPages] = useState('');
  const { incrementUsage } = useUsage();

  const handleFileSelect = (selectedFiles: File[]) => {
    setFiles(selectedFiles);
  };

  const handleSplit = () => {
    if (files.length === 0) return;
    
    // Check and increment usage before processing
    if (!incrementUsage()) {
      alert('Quota quotidien atteint!');
      return;
    }
    
    setIsProcessing(true);
    // Simulate processing
    setTimeout(() => {
      setIsProcessing(false);
      alert('PDF divisé avec succès!');
    }, 2000);
  };

  return (
    <ToolLayout
      title="Diviser PDF"
      description="Sélectionner la portée de pages, séparer une page, ou convertir chaque page du document en fichier PDF indépendant"
      icon={<Split className="w-8 h-8" />}
      color="from-purple-500 to-purple-600"
    >
      <div className="space-y-6">
        <FileUpload
          onFileSelect={handleFileSelect}
          accept=".pdf"
          multiple={false}
          maxFiles={1}
          title="Sélectionnez votre fichier PDF"
          description="Glissez-déposez un fichier PDF ici ou cliquez pour sélectionner"
        />

        {files.length > 0 && (
          <div className="bg-slate-50 p-6 rounded-xl">
            <h3 className="text-lg font-semibold text-slate-700 mb-4">
              Options de division
            </h3>
            
            <div className="space-y-4">
              <label className="flex items-center space-x-3">
                <input
                  type="radio"
                  name="splitOption"
                  value="all"
                  checked={splitOptions === 'all'}
                  onChange={(e) => setSplitOptions(e.target.value as 'all')}
                  className="text-blue-600"
                />
                <span className="text-slate-700">Diviser chaque page en fichier séparé</span>
              </label>

              <label className="flex items-center space-x-3">
                <input
                  type="radio"
                  name="splitOption"
                  value="range"
                  checked={splitOptions === 'range'}
                  onChange={(e) => setSplitOptions(e.target.value as 'range')}
                  className="text-blue-600"
                />
                <span className="text-slate-700">Diviser par plage de pages</span>
              </label>

              {splitOptions === 'range' && (
                <div className="ml-6">
                  <input
                    type="text"
                    placeholder="Ex: 1-5, 8-12"
                    value={pageRange}
                    onChange={(e) => setPageRange(e.target.value)}
                    className="w-full p-3 border border-slate-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                </div>
              )}

              <label className="flex items-center space-x-3">
                <input
                  type="radio"
                  name="splitOption"
                  value="specific"
                  checked={splitOptions === 'specific'}
                  onChange={(e) => setSplitOptions(e.target.value as 'specific')}
                  className="text-blue-600"
                />
                <span className="text-slate-700">Pages spécifiques</span>
              </label>

              {splitOptions === 'specific' && (
                <div className="ml-6">
                  <input
                    type="text"
                    placeholder="Ex: 1, 3, 5, 7"
                    value={specificPages}
                    onChange={(e) => setSpecificPages(e.target.value)}
                    className="w-full p-3 border border-slate-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  />
                </div>
              )}
            </div>
          </div>
        )}

        {files.length > 0 && (
          <div className="flex justify-center">
            <button
              onClick={handleSplit}
              disabled={isProcessing}
              className="bg-gradient-to-r from-purple-600 to-blue-600 text-white px-8 py-4 rounded-xl text-lg font-medium hover:shadow-xl transition-all duration-200 transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2"
            >
              {isProcessing ? (
                <>
                  <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white"></div>
                  <span>Division en cours...</span>
                </>
              ) : (
                <>
                  <span>Diviser le PDF</span>
                  <ArrowRight className="w-5 h-5" />
                </>
              )}
            </button>
          </div>
        )}
      </div>
    </ToolLayout>
  );
};

export default SplitPDF;