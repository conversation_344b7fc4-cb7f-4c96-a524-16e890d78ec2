"""
PDF repair tool for fixing corrupted or damaged PDF files.
"""
import os
import tempfile
from typing import List, Dict, Any, Optional
import fitz  # PyMuPDF
from PyPDF2 import PdfWriter, PdfReader
import structlog

from .base_tool import BasePDFTool, ProcessingError, ValidationError

logger = structlog.get_logger()


class PDFRepairTool(BasePDFTool):
    """Tool for repairing corrupted or damaged PDF files."""
    
    def __init__(self):
        super().__init__("repair")
        
        # Repair strategies
        self.repair_strategies = {
            "basic": "Basic repair using PyMuPDF",
            "advanced": "Advanced repair with multiple methods",
            "reconstruct": "Reconstruct PDF from extractable content"
        }
    
    async def process(
        self,
        input_files: List[str],
        output_path: str,
        parameters: Optional[Dict[str, Any]] = None
    ) -> List[str]:
        """
        Repair corrupted PDF files.
        
        Args:
            input_files: List of PDF file paths to repair
            output_path: Output directory for repaired PDFs
            parameters: Repair parameters (strategy, etc.)
            
        Returns:
            List containing paths to repaired PDF files
        """
        # Validate inputs
        if len(input_files) == 0:
            raise ValidationError("At least 1 PDF file is required for repair")
        
        self.validate_input_files(input_files)
        self.validate_pdf_files(input_files)
        
        # Prepare parameters
        params = parameters or {}
        strategy = params.get("strategy", "advanced")
        preserve_metadata = params.get("preserve_metadata", True)
        remove_invalid_objects = params.get("remove_invalid_objects", True)
        
        # Validate parameters
        if strategy not in self.repair_strategies:
            raise ValidationError(f"Invalid repair strategy: {strategy}. Available: {list(self.repair_strategies.keys())}")
        
        # Ensure output directory exists
        self.ensure_output_directory(output_path)
        
        output_files = []
        repair_reports = []
        
        try:
            self.logger.info(
                "Starting PDF repair",
                input_count=len(input_files),
                strategy=strategy,
                preserve_metadata=preserve_metadata
            )
            
            for i, input_file in enumerate(input_files):
                try:
                    self.logger.debug(f"Repairing file {i+1}/{len(input_files)}", file_path=input_file)
                    
                    # Generate output filename
                    base_name = os.path.splitext(os.path.basename(input_file))[0]
                    output_filename = f"{base_name}_repaired.pdf"
                    output_file = os.path.join(output_path, output_filename)
                    
                    # Analyze the PDF first
                    analysis = await self._analyze_pdf_damage(input_file)
                    
                    # Repair the PDF
                    repair_result = await self._repair_pdf(
                        input_file,
                        output_file,
                        strategy,
                        preserve_metadata,
                        remove_invalid_objects,
                        analysis
                    )
                    
                    # Verify output file was created
                    if not os.path.exists(output_file):
                        raise ProcessingError("Failed to create repaired PDF file")
                    
                    output_size = self.get_file_size_mb(output_file)
                    input_size = self.get_file_size_mb(input_file)
                    
                    repair_reports.append({
                        "input_file": os.path.basename(input_file),
                        "output_file": os.path.basename(output_file),
                        "strategy_used": strategy,
                        "repair_result": repair_result,
                        "input_size_mb": round(input_size, 2),
                        "output_size_mb": round(output_size, 2)
                    })
                    
                    self.logger.info(
                        f"File {i+1} repaired successfully",
                        input_file=input_file,
                        output_file=output_file,
                        repair_success=repair_result["success"],
                        issues_found=len(repair_result["issues_found"]),
                        issues_fixed=len(repair_result["issues_fixed"])
                    )
                    
                    output_files.append(output_file)
                    
                except Exception as e:
                    self.logger.error(f"Failed to repair file {i+1}", file_path=input_file, error=str(e))
                    # Don't clean up other files, continue with remaining files
                    repair_reports.append({
                        "input_file": os.path.basename(input_file),
                        "error": str(e),
                        "repair_success": False
                    })
            
            # Save repair report
            if repair_reports:
                report_file = os.path.join(output_path, "repair_report.json")
                await self._save_repair_report(report_file, repair_reports)
                output_files.append(report_file)
            
            total_input_size = sum(self.get_file_size_mb(f) for f in input_files)
            total_output_size = sum(self.get_file_size_mb(f) for f in output_files if f.endswith('.pdf'))
            
            self.logger.info(
                "PDF repair completed",
                input_files=len(input_files),
                successful_repairs=len([r for r in repair_reports if r.get("repair_success", False)]),
                total_input_size_mb=round(total_input_size, 2),
                total_output_size_mb=round(total_output_size, 2)
            )
            
            return output_files
            
        except Exception as e:
            if isinstance(e, (ValidationError, ProcessingError)):
                raise e
            else:
                raise ProcessingError(f"Unexpected error during PDF repair: {str(e)}")
    
    async def _analyze_pdf_damage(self, input_file: str) -> Dict[str, Any]:
        """Analyze PDF file to identify damage and corruption."""
        analysis = {
            "file_readable": False,
            "encryption_status": "unknown",
            "page_count": 0,
            "issues_found": [],
            "severity": "unknown"
        }
        
        try:
            # Try with PyMuPDF first
            try:
                doc = fitz.open(input_file)
                analysis["file_readable"] = True
                analysis["page_count"] = len(doc)
                analysis["encryption_status"] = "encrypted" if doc.needs_pass else "not_encrypted"
                
                # Check for common issues
                for page_num in range(min(len(doc), 5)):  # Check first 5 pages
                    try:
                        page = doc[page_num]
                        # Try to get page content
                        text = page.get_text()
                        images = page.get_images()
                    except Exception as e:
                        analysis["issues_found"].append(f"Page {page_num + 1}: {str(e)}")
                
                doc.close()
                
            except Exception as e:
                analysis["issues_found"].append(f"PyMuPDF error: {str(e)}")
            
            # Try with PyPDF2
            try:
                with open(input_file, 'rb') as f:
                    reader = PdfReader(f)
                    if not analysis["file_readable"]:
                        analysis["file_readable"] = True
                        analysis["page_count"] = len(reader.pages)
                    
                    # Check for encryption
                    if reader.is_encrypted:
                        analysis["encryption_status"] = "encrypted"
                    
                    # Try to read first few pages
                    for page_num in range(min(len(reader.pages), 3)):
                        try:
                            page = reader.pages[page_num]
                            text = page.extract_text()
                        except Exception as e:
                            analysis["issues_found"].append(f"PyPDF2 Page {page_num + 1}: {str(e)}")
            
            except Exception as e:
                analysis["issues_found"].append(f"PyPDF2 error: {str(e)}")
            
            # Determine severity
            if not analysis["file_readable"]:
                analysis["severity"] = "critical"
            elif len(analysis["issues_found"]) > 5:
                analysis["severity"] = "high"
            elif len(analysis["issues_found"]) > 0:
                analysis["severity"] = "medium"
            else:
                analysis["severity"] = "low"
            
            return analysis
            
        except Exception as e:
            analysis["issues_found"].append(f"Analysis error: {str(e)}")
            analysis["severity"] = "critical"
            return analysis
    
    async def _repair_pdf(
        self,
        input_file: str,
        output_file: str,
        strategy: str,
        preserve_metadata: bool,
        remove_invalid_objects: bool,
        analysis: Dict[str, Any]
    ) -> Dict[str, Any]:
        """Repair a PDF file using the specified strategy."""
        repair_result = {
            "success": False,
            "strategy_used": strategy,
            "issues_found": analysis["issues_found"].copy(),
            "issues_fixed": [],
            "warnings": []
        }
        
        try:
            if strategy == "basic":
                repair_result = await self._basic_repair(input_file, output_file, repair_result)
            elif strategy == "advanced":
                repair_result = await self._advanced_repair(input_file, output_file, repair_result, preserve_metadata)
            elif strategy == "reconstruct":
                repair_result = await self._reconstruct_pdf(input_file, output_file, repair_result)
            
            return repair_result
            
        except Exception as e:
            repair_result["error"] = str(e)
            return repair_result
    
    async def _basic_repair(self, input_file: str, output_file: str, repair_result: Dict[str, Any]) -> Dict[str, Any]:
        """Basic repair using PyMuPDF's built-in repair capabilities."""
        try:
            # Open with PyMuPDF (automatically attempts repair)
            doc = fitz.open(input_file)
            
            # Save as new PDF (this often fixes minor corruption)
            doc.save(output_file, garbage=4, deflate=True, clean=True)
            doc.close()
            
            repair_result["success"] = True
            repair_result["issues_fixed"].append("Applied PyMuPDF automatic repair")
            
            return repair_result
            
        except Exception as e:
            repair_result["error"] = f"Basic repair failed: {str(e)}"
            return repair_result
    
    async def _advanced_repair(
        self, 
        input_file: str, 
        output_file: str, 
        repair_result: Dict[str, Any],
        preserve_metadata: bool
    ) -> Dict[str, Any]:
        """Advanced repair using multiple methods."""
        try:
            # Try PyMuPDF repair first
            try:
                doc = fitz.open(input_file)
                
                # Create new document
                new_doc = fitz.open()
                
                # Copy pages one by one, skipping corrupted ones
                for page_num in range(len(doc)):
                    try:
                        page = doc[page_num]
                        new_doc.insert_pdf(doc, from_page=page_num, to_page=page_num)
                        repair_result["issues_fixed"].append(f"Successfully copied page {page_num + 1}")
                    except Exception as e:
                        repair_result["warnings"].append(f"Skipped corrupted page {page_num + 1}: {str(e)}")
                
                # Preserve metadata if requested
                if preserve_metadata:
                    try:
                        metadata = doc.metadata
                        if metadata:
                            new_doc.set_metadata(metadata)
                            repair_result["issues_fixed"].append("Preserved document metadata")
                    except Exception as e:
                        repair_result["warnings"].append(f"Could not preserve metadata: {str(e)}")
                
                new_doc.save(output_file, garbage=4, deflate=True, clean=True)
                new_doc.close()
                doc.close()
                
                repair_result["success"] = True
                return repair_result
                
            except Exception as e:
                repair_result["warnings"].append(f"PyMuPDF advanced repair failed: {str(e)}")
            
            # Fallback to PyPDF2 repair
            try:
                with open(input_file, 'rb') as f:
                    reader = PdfReader(f, strict=False)  # Non-strict mode for damaged files
                    writer = PdfWriter()
                    
                    # Copy pages that can be read
                    for page_num in range(len(reader.pages)):
                        try:
                            page = reader.pages[page_num]
                            writer.add_page(page)
                            repair_result["issues_fixed"].append(f"Recovered page {page_num + 1} with PyPDF2")
                        except Exception as e:
                            repair_result["warnings"].append(f"Could not recover page {page_num + 1}: {str(e)}")
                    
                    # Save repaired PDF
                    with open(output_file, 'wb') as output_f:
                        writer.write(output_f)
                    
                    repair_result["success"] = True
                    return repair_result
                    
            except Exception as e:
                repair_result["error"] = f"All repair methods failed: {str(e)}"
                return repair_result
            
        except Exception as e:
            repair_result["error"] = f"Advanced repair failed: {str(e)}"
            return repair_result

    async def _reconstruct_pdf(self, input_file: str, output_file: str, repair_result: Dict[str, Any]) -> Dict[str, Any]:
        """Reconstruct PDF from extractable content."""
        try:
            from reportlab.pdfgen import canvas
            from reportlab.lib.pagesizes import letter

            # Try to extract any readable content
            extracted_content = []

            # Try PyMuPDF extraction
            try:
                doc = fitz.open(input_file)
                for page_num in range(len(doc)):
                    try:
                        page = doc[page_num]
                        text = page.get_text()
                        if text.strip():
                            extracted_content.append(f"Page {page_num + 1}:\n{text}")
                    except Exception:
                        continue
                doc.close()
            except Exception:
                pass

            # Try PyPDF2 extraction as fallback
            if not extracted_content:
                try:
                    with open(input_file, 'rb') as f:
                        reader = PdfReader(f, strict=False)
                        for page_num in range(len(reader.pages)):
                            try:
                                page = reader.pages[page_num]
                                text = page.extract_text()
                                if text.strip():
                                    extracted_content.append(f"Page {page_num + 1}:\n{text}")
                            except Exception:
                                continue
                except Exception:
                    pass

            if not extracted_content:
                repair_result["error"] = "No readable content could be extracted for reconstruction"
                return repair_result

            # Create new PDF with extracted content
            c = canvas.Canvas(output_file, pagesize=letter)

            for content in extracted_content:
                lines = content.split('\n')
                y_position = 750

                for line in lines:
                    if line.strip():
                        c.drawString(50, y_position, line.strip()[:80])  # Limit line length
                        y_position -= 15

                        if y_position < 50:
                            c.showPage()
                            y_position = 750

                c.showPage()

            c.save()

            repair_result["success"] = True
            repair_result["issues_fixed"].append(f"Reconstructed PDF with {len(extracted_content)} pages of content")
            repair_result["warnings"].append("PDF was reconstructed - formatting may be lost")

            return repair_result

        except Exception as e:
            repair_result["error"] = f"Reconstruction failed: {str(e)}"
            return repair_result

    async def _save_repair_report(self, report_file: str, repair_reports: List[Dict[str, Any]]):
        """Save repair report to JSON file."""
        try:
            import json

            report_data = {
                "repair_session": {
                    "total_files": len(repair_reports),
                    "successful_repairs": len([r for r in repair_reports if r.get("repair_success", False)]),
                    "failed_repairs": len([r for r in repair_reports if not r.get("repair_success", False)])
                },
                "file_reports": repair_reports
            }

            with open(report_file, 'w', encoding='utf-8') as f:
                json.dump(report_data, f, indent=2, ensure_ascii=False)

        except Exception as e:
            self.logger.error("Failed to save repair report", error=str(e))

    def get_repair_options(self) -> Dict[str, Dict[str, Any]]:
        """Get available repair options."""
        return {
            "strategy": {
                "description": "Repair strategy to use",
                "type": "string",
                "options": list(self.repair_strategies.keys()),
                "default": "advanced",
                "strategy_descriptions": self.repair_strategies
            },
            "preserve_metadata": {
                "description": "Attempt to preserve document metadata",
                "type": "boolean",
                "default": True
            },
            "remove_invalid_objects": {
                "description": "Remove invalid PDF objects during repair",
                "type": "boolean",
                "default": True
            }
        }

    def get_repair_strategies(self) -> Dict[str, str]:
        """Get available repair strategies."""
        return self.repair_strategies.copy()

    async def diagnose_pdf(self, file_path: str) -> Dict[str, Any]:
        """Diagnose PDF file issues without repairing."""
        try:
            analysis = await self._analyze_pdf_damage(file_path)

            # Add recommendations
            recommendations = []

            if analysis["severity"] == "critical":
                recommendations.append("File is severely damaged - try 'reconstruct' strategy")
            elif analysis["severity"] == "high":
                recommendations.append("Multiple issues found - use 'advanced' strategy")
            elif analysis["severity"] == "medium":
                recommendations.append("Some issues found - 'basic' or 'advanced' strategy recommended")
            else:
                recommendations.append("File appears healthy - repair may not be necessary")

            if analysis["encryption_status"] == "encrypted":
                recommendations.append("File is encrypted - password may be required")

            return {
                "filename": os.path.basename(file_path),
                "file_size_mb": round(self.get_file_size_mb(file_path), 2),
                "analysis": analysis,
                "recommendations": recommendations,
                "repair_needed": analysis["severity"] in ["medium", "high", "critical"]
            }

        except Exception as e:
            return {
                "filename": os.path.basename(file_path),
                "error": str(e),
                "analysis": {"severity": "unknown", "issues_found": [str(e)]},
                "recommendations": ["File could not be analyzed - try repair with caution"]
            }


# Create tool instance
repair_pdf_tool = PDFRepairTool()
