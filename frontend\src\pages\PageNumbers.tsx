import React, { useState } from 'react';
import { Hash, Download, ArrowRight } from 'lucide-react';
import ToolLayout from '../components/ToolLayout';
import FileUpload from '../components/FileUpload';

const PageNumbers = () => {
  const [files, setFiles] = useState<File[]>([]);
  const [isProcessing, setIsProcessing] = useState(false);
  const [position, setPosition] = useState<'bottom-center' | 'bottom-left' | 'bottom-right' | 'top-center' | 'top-left' | 'top-right'>('bottom-center');
  const [format, setFormat] = useState<'number' | 'x-of-y' | 'roman'>('number');

  const handleFileSelect = (selectedFiles: File[]) => {
    setFiles(selectedFiles);
  };

  const handleAddPageNumbers = () => {
    if (files.length === 0) return;
    
    setIsProcessing(true);
    // Simulate processing
    setTimeout(() => {
      setIsProcessing(false);
      alert('Numéros de pages ajoutés avec succès!');
    }, 2000);
  };

  return (
    <ToolLayout
      title="Numéros de pages"
      description="Insérez des numéros de pages dans les documents PDF, en toute simplicité"
      icon={<Hash className="w-8 h-8" />}
      color="from-fuchsia-500 to-fuchsia-600"
    >
      <div className="space-y-6">
        <FileUpload
          onFileSelect={handleFileSelect}
          accept=".pdf"
          multiple={false}
          maxFiles={1}
          title="Sélectionnez votre fichier PDF"
          description="Glissez-déposez un fichier PDF ici ou cliquez pour sélectionner"
        />

        {files.length > 0 && (
          <div className="bg-slate-50 p-6 rounded-xl">
            <h3 className="text-lg font-semibold text-slate-700 mb-4">
              Configuration des numéros de pages
            </h3>
            
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-slate-700 mb-2">
                  Position
                </label>
                <div className="grid grid-cols-3 gap-2">
                  <label className="flex items-center justify-center p-3 bg-white rounded-lg cursor-pointer hover:bg-slate-50 transition-colors">
                    <input
                      type="radio"
                      name="position"
                      value="top-left"
                      checked={position === 'top-left'}
                      onChange={(e) => setPosition(e.target.value as 'top-left')}
                      className="text-fuchsia-600 mr-2"
                    />
                    <span className="text-sm">Haut gauche</span>
                  </label>
                  <label className="flex items-center justify-center p-3 bg-white rounded-lg cursor-pointer hover:bg-slate-50 transition-colors">
                    <input
                      type="radio"
                      name="position"
                      value="top-center"
                      checked={position === 'top-center'}
                      onChange={(e) => setPosition(e.target.value as 'top-center')}
                      className="text-fuchsia-600 mr-2"
                    />
                    <span className="text-sm">Haut centre</span>
                  </label>
                  <label className="flex items-center justify-center p-3 bg-white rounded-lg cursor-pointer hover:bg-slate-50 transition-colors">
                    <input
                      type="radio"
                      name="position"
                      value="top-right"
                      checked={position === 'top-right'}
                      onChange={(e) => setPosition(e.target.value as 'top-right')}
                      className="text-fuchsia-600 mr-2"
                    />
                    <span className="text-sm">Haut droite</span>
                  </label>
                  <label className="flex items-center justify-center p-3 bg-white rounded-lg cursor-pointer hover:bg-slate-50 transition-colors">
                    <input
                      type="radio"
                      name="position"
                      value="bottom-left"
                      checked={position === 'bottom-left'}
                      onChange={(e) => setPosition(e.target.value as 'bottom-left')}
                      className="text-fuchsia-600 mr-2"
                    />
                    <span className="text-sm">Bas gauche</span>
                  </label>
                  <label className="flex items-center justify-center p-3 bg-white rounded-lg cursor-pointer hover:bg-slate-50 transition-colors">
                    <input
                      type="radio"
                      name="position"
                      value="bottom-center"
                      checked={position === 'bottom-center'}
                      onChange={(e) => setPosition(e.target.value as 'bottom-center')}
                      className="text-fuchsia-600 mr-2"
                    />
                    <span className="text-sm">Bas centre</span>
                  </label>
                  <label className="flex items-center justify-center p-3 bg-white rounded-lg cursor-pointer hover:bg-slate-50 transition-colors">
                    <input
                      type="radio"
                      name="position"
                      value="bottom-right"
                      checked={position === 'bottom-right'}
                      onChange={(e) => setPosition(e.target.value as 'bottom-right')}
                      className="text-fuchsia-600 mr-2"
                    />
                    <span className="text-sm">Bas droite</span>
                  </label>
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-slate-700 mb-2">
                  Format
                </label>
                <div className="space-y-2">
                  <label className="flex items-center space-x-3">
                    <input
                      type="radio"
                      name="format"
                      value="number"
                      checked={format === 'number'}
                      onChange={(e) => setFormat(e.target.value as 'number')}
                      className="text-fuchsia-600"
                    />
                    <span className="text-slate-700">Numérique (1, 2, 3...)</span>
                  </label>
                  <label className="flex items-center space-x-3">
                    <input
                      type="radio"
                      name="format"
                      value="x-of-y"
                      checked={format === 'x-of-y'}
                      onChange={(e) => setFormat(e.target.value as 'x-of-y')}
                      className="text-fuchsia-600"
                    />
                    <span className="text-slate-700">X sur Y (1 sur 10, 2 sur 10...)</span>
                  </label>
                  <label className="flex items-center space-x-3">
                    <input
                      type="radio"
                      name="format"
                      value="roman"
                      checked={format === 'roman'}
                      onChange={(e) => setFormat(e.target.value as 'roman')}
                      className="text-fuchsia-600"
                    />
                    <span className="text-slate-700">Chiffres romains (I, II, III...)</span>
                  </label>
                </div>
              </div>

              <div className="space-y-3">
                <div className="flex items-center space-x-3">
                  <input
                    type="checkbox"
                    id="skip-first-page"
                    className="text-fuchsia-600 rounded"
                  />
                  <label htmlFor="skip-first-page" className="text-slate-700">
                    Ignorer la première page
                  </label>
                </div>

                <div className="flex items-center space-x-3">
                  <input
                    type="checkbox"
                    id="start-from-number"
                    className="text-fuchsia-600 rounded"
                  />
                  <label htmlFor="start-from-number" className="text-slate-700">
                    Commencer à partir du numéro 1
                  </label>
                </div>
              </div>
            </div>
          </div>
        )}

        {files.length > 0 && (
          <div className="flex justify-center">
            <button
              onClick={handleAddPageNumbers}
              disabled={isProcessing}
              className="bg-gradient-to-r from-fuchsia-600 to-purple-600 text-white px-8 py-4 rounded-xl text-lg font-medium hover:shadow-xl transition-all duration-200 transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2"
            >
              {isProcessing ? (
                <>
                  <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white"></div>
                  <span>Ajout des numéros...</span>
                </>
              ) : (
                <>
                  <span>Ajouter les numéros de pages</span>
                  <ArrowRight className="w-5 h-5" />
                </>
              )}
            </button>
          </div>
        )}
      </div>
    </ToolLayout>
  );
};

export default PageNumbers;