#!/usr/bin/env python3
"""
Wrapper script for PDF repair tool.
Usage: python repair_pdf_wrapper.py --config config.json
"""
import sys
import json
import argparse
import asyncio
from pathlib import Path

# Add tools directory to path
sys.path.insert(0, str(Path(__file__).parent))

from repair_pdf import repair_pdf_tool


async def main():
    parser = argparse.ArgumentParser(description='PDF Repair Tool Wrapper')
    parser.add_argument('--config', required=True, help='Path to configuration JSON file')
    
    args = parser.parse_args()
    
    try:
        # Load configuration
        with open(args.config, 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        input_files = config['input_files']
        output_path = config['output_path']
        parameters = config.get('parameters', {})
        
        # Execute the tool
        output_files = await repair_pdf_tool.process_with_timeout(
            input_files=input_files,
            output_path=output_path,
            parameters=parameters,
            timeout_seconds=300
        )
        
        # Return result
        result = {
            'success': True,
            'output_files': output_files,
            'logs': []
        }
        
        print(json.dumps(result, indent=2))
        sys.exit(0)
        
    except Exception as e:
        result = {
            'success': False,
            'error': str(e),
            'output_files': [],
            'logs': []
        }
        
        print(json.dumps(result, indent=2))
        sys.exit(1)


if __name__ == '__main__':
    asyncio.run(main())
