// Simple test script to verify the system is working
const axios = require('axios');
const FormData = require('form-data');
const fs = require('fs');

const BASE_URL = 'http://localhost:3001/api';

async function testSystem() {
  console.log('🧪 Testing PDF Processing System...\n');

  try {
    // Test 1: Health Check
    console.log('1. Testing health endpoint...');
    const healthResponse = await axios.get(`${BASE_URL}/health`);
    console.log('✅ Health check passed:', healthResponse.data.status);

    // Test 2: Get Available Tools
    console.log('\n2. Testing available tools endpoint...');
    const toolsResponse = await axios.get(`${BASE_URL}/pdf-tools/tools`);
    console.log('✅ Available tools:', toolsResponse.data.data.count, 'tools found');

    // Test 3: User Registration
    console.log('\n3. Testing user registration...');
    const userData = {
      email: '<EMAIL>',
      password: 'TestPassword123!',
      confirmPassword: 'TestPassword123!',
      firstName: 'Test',
      lastName: 'User'
    };

    try {
      const registerResponse = await axios.post(`${BASE_URL}/auth/register`, userData);
      console.log('✅ User registration successful');
      
      const token = registerResponse.data.data.tokens.accessToken;
      
      // Test 4: Get User Profile
      console.log('\n4. Testing authenticated request...');
      const profileResponse = await axios.get(`${BASE_URL}/auth/profile`, {
        headers: { Authorization: `Bearer ${token}` }
      });
      console.log('✅ Authenticated request successful:', profileResponse.data.data.user.email);

      // Test 5: Get Usage Stats
      console.log('\n5. Testing usage endpoint...');
      const usageResponse = await axios.get(`${BASE_URL}/user/usage`, {
        headers: { Authorization: `Bearer ${token}` }
      });
      console.log('✅ Usage stats retrieved:', usageResponse.data.data.subscriptionPlan);

    } catch (authError) {
      if (authError.response?.status === 400 && authError.response?.data?.error?.message?.includes('already exists')) {
        console.log('ℹ️  User already exists, trying login...');
        
        const loginResponse = await axios.post(`${BASE_URL}/auth/login`, {
          email: userData.email,
          password: userData.password
        });
        console.log('✅ Login successful');
      } else {
        throw authError;
      }
    }

    console.log('\n🎉 All tests passed! System is working correctly with MySQL database.');
    console.log('\n📋 Next steps:');
    console.log('   • Frontend: http://localhost:5173');
    console.log('   • Backend API: http://localhost:3001/api');
    console.log('   • MySQL Database: Connected and synchronized');
    console.log('   • Upload a PDF file through the frontend to test PDF processing');

  } catch (error) {
    console.error('\n❌ Test failed:', error.response?.data || error.message);
    console.log('\n🔧 Troubleshooting:');
    console.log('   • Make sure backend is running on port 3001');
    console.log('   • Check that all dependencies are installed');
    console.log('   • Verify Python tools are accessible');
  }
}

// Run if axios is available
if (typeof require !== 'undefined') {
  try {
    testSystem();
  } catch (e) {
    console.log('❌ Please install axios first: npm install axios form-data');
    console.log('Then run: node test_system.js');
  }
} else {
  console.log('This script should be run with Node.js: node test_system.js');
}
