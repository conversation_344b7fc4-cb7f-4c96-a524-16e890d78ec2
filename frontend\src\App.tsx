import React from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { AuthProvider } from './contexts/AuthContext';
import { UsageProvider } from './contexts/UsageContext';
import Navbar from './components/Navbar';
import UpgradePrompt from './components/UpgradePrompt';
import { useUsage } from './contexts/UsageContext';
import LandingPage from './pages/LandingPage';
import SignIn from './pages/SignIn';
import SignUp from './pages/SignUp';
import ForgotPassword from './pages/ForgotPassword';
import ToolsPage from './pages/ToolsPage';
import MergePDF from './pages/MergePDF';
import SplitPDF from './pages/SplitPDF';
import CompressPDF from './pages/CompressPDF';
import PDFToWord from './pages/PDFToWord';
import PDFToPowerPoint from './pages/PDFToPowerPoint';
import PDFToExcel from './pages/PDFToExcel';
import WordToPDF from './pages/WordToPDF';
import PowerPointToPDF from './pages/PowerPointToPDF';
import ExcelToPDF from './pages/ExcelToPDF';
import EditPDF from './pages/EditPDF';
import PDFToJPG from './pages/PDFToJPG';
import JPGToPDF from './pages/JPGToPDF';
import SignPDF from './pages/SignPDF';
import WatermarkPDF from './pages/WatermarkPDF';
import RotatePDF from './pages/RotatePDF';
import HTMLToPDF from './pages/HTMLToPDF';
import UnlockPDF from './pages/UnlockPDF';
import ProtectPDF from './pages/ProtectPDF';
import OrganizePDF from './pages/OrganizePDF';
import PDFToPDFA from './pages/PDFToPDFA';
import RepairPDF from './pages/RepairPDF';
import PageNumbers from './pages/PageNumbers';
import ScanToPDF from './pages/ScanToPDF';
import OCRPDF from './pages/OCRPDF';
import ComparePDF from './pages/ComparePDF';
import RedactPDF from './pages/RedactPDF';
import CropPDF from './pages/CropPDF';

const AppContent = () => {
  const { showUpgradePrompt, setShowUpgradePrompt } = useUsage();

  return (
    <>
      <div className="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50">
        <Navbar />
        <Routes>
          <Route path="/" element={<LandingPage />} />
          <Route path="/signin" element={<SignIn />} />
          <Route path="/signup" element={<SignUp />} />
          <Route path="/forgot-password" element={<ForgotPassword />} />
          <Route path="/tools" element={<ToolsPage />} />
          <Route path="/fusionner-pdf" element={<MergePDF />} />
          <Route path="/diviser-pdf" element={<SplitPDF />} />
          <Route path="/compresser-pdf" element={<CompressPDF />} />
          <Route path="/pdf-vers-word" element={<PDFToWord />} />
          <Route path="/pdf-vers-powerpoint" element={<PDFToPowerPoint />} />
          <Route path="/pdf-vers-excel" element={<PDFToExcel />} />
          <Route path="/word-vers-pdf" element={<WordToPDF />} />
          <Route path="/powerpoint-vers-pdf" element={<PowerPointToPDF />} />
          <Route path="/excel-vers-pdf" element={<ExcelToPDF />} />
          <Route path="/modifier-pdf" element={<EditPDF />} />
          <Route path="/pdf-vers-jpg" element={<PDFToJPG />} />
          <Route path="/jpg-vers-pdf" element={<JPGToPDF />} />
          <Route path="/signer-pdf" element={<SignPDF />} />
          <Route path="/filigrane-pdf" element={<WatermarkPDF />} />
          <Route path="/pivoter-pdf" element={<RotatePDF />} />
          <Route path="/html-vers-pdf" element={<HTMLToPDF />} />
          <Route path="/deverrouiller-pdf" element={<UnlockPDF />} />
          <Route path="/proteger-pdf" element={<ProtectPDF />} />
          <Route path="/organiser-pdf" element={<OrganizePDF />} />
          <Route path="/pdf-vers-pdfa" element={<PDFToPDFA />} />
          <Route path="/reparer-pdf" element={<RepairPDF />} />
          <Route path="/numeros-pages-pdf" element={<PageNumbers />} />
          <Route path="/scanner-vers-pdf" element={<ScanToPDF />} />
          <Route path="/ocr-pdf" element={<OCRPDF />} />
          <Route path="/comparer-pdf" element={<ComparePDF />} />
          <Route path="/censurer-pdf" element={<RedactPDF />} />
          <Route path="/rogner-pdf" element={<CropPDF />} />
        </Routes>
      </div>
      
      <UpgradePrompt 
        isOpen={showUpgradePrompt} 
        onClose={() => setShowUpgradePrompt(false)} 
      />
    </>
  );
};
function App() {
  return (
    <Router>
      <AuthProvider>
        <UsageProvider>
          <AppContent />
        </UsageProvider>
      </AuthProvider>
    </Router>
  );
}

export default App;