#!/usr/bin/env python3
"""
Tool wrapper for integrating PDF tools with Node.js backend.
This script provides a standardized interface for all PDF tools.
"""
import sys
import json
import argparse
import asyncio
import traceback
import os
from pathlib import Path
from typing import Dict, Any, List

# Add current directory to Python path
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

# Simple logging for now
def log_info(message, **kwargs):
    print(f"INFO: {message}", file=sys.stderr)
    if kwargs:
        print(f"  Details: {kwargs}", file=sys.stderr)

def log_error(message, **kwargs):
    print(f"ERROR: {message}", file=sys.stderr)
    if kwargs:
        print(f"  Details: {kwargs}", file=sys.stderr)

# Available tools (discovered dynamically)
def get_available_tools():
    """Get list of available PDF tools by scanning the directory."""
    tools = []
    tool_files = [
        'repair_pdf.py', 'merge_pdf.py', 'split_pdf.py', 'compress_pdf.py',
        'excel_to_pdf.py', 'word_to_pdf.py', 'powerpoint_to_pdf.py', 'html_to_pdf.py',
        'pdf_to_excel.py', 'pdf_to_word.py', 'pdf_to_powerpoint.py', 'pdf_to_image.py',
        'pdf_to_pdfa.py', 'ocr_pdf.py', 'sign_pdf.py', 'protect_pdf.py', 'unlock_pdf.py',
        'watermark_pdf.py', 'rotate_pdf.py', 'crop_pdf.py', 'organize_pdf.py',
        'page_numbers_pdf.py', 'redact_pdf.py', 'compare_pdf.py', 'edit_pdf.py',
        'scan_to_pdf.py', 'image_to_pdf.py'
    ]

    for tool_file in tool_files:
        tool_path = current_dir / tool_file
        if tool_path.exists():
            tool_name = tool_file.replace('.py', '')
            tools.append({
                'name': tool_name,
                'file': tool_file,
                'description': f'{tool_name.replace("_", " ").title()} tool'
            })

    return tools


class ToolWrapper:
    """Wrapper class for executing PDF tools with standardized interface."""

    def __init__(self, config_path: str):
        self.config_path = config_path
        self.config = self._load_config()

    def _load_config(self) -> Dict[str, Any]:
        """Load configuration from JSON file."""
        try:
            with open(self.config_path, 'r', encoding='utf-8') as f:
                config = json.load(f)

            # Validate required fields
            required_fields = ['job_id', 'tool_name', 'input_files', 'output_path']
            for field in required_fields:
                if field not in config:
                    raise ValueError(f"Missing required field: {field}")

            return config
        except Exception as e:
            log_error("Failed to load configuration", error=str(e), config_path=self.config_path)
            raise

    async def execute(self) -> Dict[str, Any]:
        """Execute the specified PDF tool."""
        tool_name = self.config['tool_name']
        input_files = self.config['input_files']
        output_path = self.config['output_path']
        parameters = self.config.get('parameters', {})

        log_info("Starting tool execution",
                tool_name=tool_name,
                input_files=len(input_files),
                parameters=parameters)

        try:
            # For now, just return a mock success response
            # TODO: Implement actual tool execution once imports are fixed

            result = {
                'success': True,
                'output_files': [],  # Mock empty output for now
                'tool_name': tool_name,
                'input_count': len(input_files),
                'output_count': 0,
                'message': f'Tool {tool_name} executed successfully (mock response)'
            }

            log_info("Tool execution completed successfully",
                    tool_name=tool_name)

            return result

        except Exception as e:
            error_msg = str(e)
            error_trace = traceback.format_exc()

            log_error("Tool execution failed",
                     error=error_msg,
                     traceback=error_trace)

            return {
                'success': False,
                'error': error_msg,
                'tool_name': tool_name,
                'traceback': error_trace
            }


async def main():
    """Main entry point for the tool wrapper."""
    parser = argparse.ArgumentParser(description='PDF Tool Wrapper')
    parser.add_argument('--config', help='Path to configuration JSON file')
    parser.add_argument('--list-tools', action='store_true', help='List available tools')

    args = parser.parse_args()

    if args.list_tools:
        # List available tools
        tools_info = get_available_tools()

        result = {
            'success': True,
            'tools': tools_info,
            'count': len(tools_info)
        }
        print(json.dumps(result, indent=2))
        return

    if not args.config:
        parser.error('--config is required when not using --list-tools')
    
    try:
        # Execute the tool
        wrapper = ToolWrapper(args.config)
        result = await wrapper.execute()
        
        # Output result as JSON
        print(json.dumps(result, indent=2, ensure_ascii=False))
        
        # Exit with appropriate code
        sys.exit(0 if result['success'] else 1)
        
    except Exception as e:
        error_result = {
            'success': False,
            'error': str(e),
            'traceback': traceback.format_exc()
        }
        print(json.dumps(error_result, indent=2))
        sys.exit(1)


if __name__ == '__main__':
    asyncio.run(main())
