#!/usr/bin/env python3
"""
Tool wrapper for integrating PDF tools with Node.js backend.
This script provides a standardized interface for all PDF tools.
"""
import sys
import json
import argparse
import asyncio
import traceback
from pathlib import Path
from typing import Dict, Any, List
import structlog

# Configure structured logging
structlog.configure(
    processors=[
        structlog.stdlib.filter_by_level,
        structlog.stdlib.add_logger_name,
        structlog.stdlib.add_log_level,
        structlog.stdlib.PositionalArgumentsFormatter(),
        structlog.processors.TimeStamper(fmt="iso"),
        structlog.processors.StackInfoRenderer(),
        structlog.processors.format_exc_info,
        structlog.processors.UnicodeDecoder(),
        structlog.processors.JSONRenderer()
    ],
    context_class=dict,
    logger_factory=structlog.stdlib.LoggerFactory(),
    wrapper_class=structlog.stdlib.BoundLogger,
    cache_logger_on_first_use=True,
)

logger = structlog.get_logger()

# Import all available tools
try:
    from repair_pdf import repair_pdf_tool
    from merge_pdf import merge_pdf_tool
    from split_pdf import split_pdf_tool
    from compress_pdf import compress_pdf_tool
    from excel_to_pdf import excel_to_pdf_tool
    from word_to_pdf import word_to_pdf_tool
    from powerpoint_to_pdf import powerpoint_to_pdf_tool
    from html_to_pdf import html_to_pdf_tool
    from pdf_to_excel import pdf_to_excel_tool
    from pdf_to_word import pdf_to_word_tool
    from pdf_to_powerpoint import pdf_to_powerpoint_tool
    from pdf_to_image import pdf_to_image_tool
    from pdf_to_pdfa import pdf_to_pdfa_tool
    from ocr_pdf import ocr_pdf_tool
    from sign_pdf import sign_pdf_tool
    from protect_pdf import protect_pdf_tool
    from unlock_pdf import unlock_pdf_tool
    from watermark_pdf import watermark_pdf_tool
    from rotate_pdf import rotate_pdf_tool
    from crop_pdf import crop_pdf_tool
    from organize_pdf import organize_pdf_tool
    from page_numbers_pdf import page_numbers_pdf_tool
    from redact_pdf import redact_pdf_tool
    from compare_pdf import compare_pdf_tool
    from edit_pdf import edit_pdf_tool
    from scan_to_pdf import scan_to_pdf_tool
    from image_to_pdf import image_to_pdf_tool
except ImportError as e:
    logger.error("Failed to import PDF tools", error=str(e))
    sys.exit(1)

# Tool registry
TOOLS = {
    'repair_pdf': repair_pdf_tool,
    'merge_pdf': merge_pdf_tool,
    'split_pdf': split_pdf_tool,
    'compress_pdf': compress_pdf_tool,
    'excel_to_pdf': excel_to_pdf_tool,
    'word_to_pdf': word_to_pdf_tool,
    'powerpoint_to_pdf': powerpoint_to_pdf_tool,
    'html_to_pdf': html_to_pdf_tool,
    'pdf_to_excel': pdf_to_excel_tool,
    'pdf_to_word': pdf_to_word_tool,
    'pdf_to_powerpoint': pdf_to_powerpoint_tool,
    'pdf_to_image': pdf_to_image_tool,
    'pdf_to_pdfa': pdf_to_pdfa_tool,
    'ocr_pdf': ocr_pdf_tool,
    'sign_pdf': sign_pdf_tool,
    'protect_pdf': protect_pdf_tool,
    'unlock_pdf': unlock_pdf_tool,
    'watermark_pdf': watermark_pdf_tool,
    'rotate_pdf': rotate_pdf_tool,
    'crop_pdf': crop_pdf_tool,
    'organize_pdf': organize_pdf_tool,
    'page_numbers_pdf': page_numbers_pdf_tool,
    'redact_pdf': redact_pdf_tool,
    'compare_pdf': compare_pdf_tool,
    'edit_pdf': edit_pdf_tool,
    'scan_to_pdf': scan_to_pdf_tool,
    'image_to_pdf': image_to_pdf_tool
}


class ToolWrapper:
    """Wrapper class for executing PDF tools with standardized interface."""
    
    def __init__(self, config_path: str):
        self.config_path = config_path
        self.config = self._load_config()
        self.logger = logger.bind(
            job_id=self.config.get('job_id'),
            tool_name=self.config.get('tool_name')
        )
    
    def _load_config(self) -> Dict[str, Any]:
        """Load configuration from JSON file."""
        try:
            with open(self.config_path, 'r', encoding='utf-8') as f:
                config = json.load(f)
            
            # Validate required fields
            required_fields = ['job_id', 'tool_name', 'input_files', 'output_path']
            for field in required_fields:
                if field not in config:
                    raise ValueError(f"Missing required field: {field}")
            
            return config
        except Exception as e:
            logger.error("Failed to load configuration", error=str(e), config_path=self.config_path)
            raise
    
    async def execute(self) -> Dict[str, Any]:
        """Execute the specified PDF tool."""
        tool_name = self.config['tool_name']
        input_files = self.config['input_files']
        output_path = self.config['output_path']
        parameters = self.config.get('parameters', {})
        
        self.logger.info("Starting tool execution", 
                        tool_name=tool_name,
                        input_files=len(input_files),
                        parameters=parameters)
        
        try:
            # Get the tool instance
            if tool_name not in TOOLS:
                raise ValueError(f"Unknown tool: {tool_name}")
            
            tool = TOOLS[tool_name]
            
            # Execute the tool
            output_files = await tool.process_with_timeout(
                input_files=input_files,
                output_path=output_path,
                parameters=parameters,
                timeout_seconds=300  # 5 minutes default timeout
            )
            
            # Validate output files exist
            existing_files = []
            for file_path in output_files:
                if Path(file_path).exists():
                    existing_files.append(file_path)
                else:
                    self.logger.warning("Output file not found", file_path=file_path)
            
            result = {
                'success': True,
                'output_files': existing_files,
                'tool_name': tool_name,
                'input_count': len(input_files),
                'output_count': len(existing_files)
            }
            
            self.logger.info("Tool execution completed successfully", 
                           output_files=len(existing_files))
            
            return result
            
        except Exception as e:
            error_msg = str(e)
            error_trace = traceback.format_exc()
            
            self.logger.error("Tool execution failed", 
                            error=error_msg,
                            traceback=error_trace)
            
            return {
                'success': False,
                'error': error_msg,
                'tool_name': tool_name,
                'traceback': error_trace
            }


async def main():
    """Main entry point for the tool wrapper."""
    parser = argparse.ArgumentParser(description='PDF Tool Wrapper')
    parser.add_argument('--config', required=True, help='Path to configuration JSON file')
    parser.add_argument('--list-tools', action='store_true', help='List available tools')
    
    args = parser.parse_args()
    
    if args.list_tools:
        # List available tools
        tools_info = []
        for tool_name, tool_instance in TOOLS.items():
            tool_info = {
                'name': tool_name,
                'class': tool_instance.__class__.__name__,
                'description': getattr(tool_instance, '__doc__', 'No description available')
            }
            tools_info.append(tool_info)
        
        result = {
            'success': True,
            'tools': tools_info,
            'count': len(tools_info)
        }
        print(json.dumps(result, indent=2))
        return
    
    try:
        # Execute the tool
        wrapper = ToolWrapper(args.config)
        result = await wrapper.execute()
        
        # Output result as JSON
        print(json.dumps(result, indent=2, ensure_ascii=False))
        
        # Exit with appropriate code
        sys.exit(0 if result['success'] else 1)
        
    except Exception as e:
        error_result = {
            'success': False,
            'error': str(e),
            'traceback': traceback.format_exc()
        }
        print(json.dumps(error_result, indent=2))
        sys.exit(1)


if __name__ == '__main__':
    asyncio.run(main())
