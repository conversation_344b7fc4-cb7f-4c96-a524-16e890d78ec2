import React, { useState } from 'react';
import { FileText, Download, ArrowRight } from 'lucide-react';
import ToolLayout from '../components/ToolLayout';
import FileUpload from '../components/FileUpload';

const WordToPDF = () => {
  const [files, setFiles] = useState<File[]>([]);
  const [isProcessing, setIsProcessing] = useState(false);

  const handleFileSelect = (selectedFiles: File[]) => {
    setFiles(selectedFiles);
  };

  const handleConvert = () => {
    if (files.length === 0) return;
    
    setIsProcessing(true);
    // Simulate processing
    setTimeout(() => {
      setIsProcessing(false);
      alert('Document Word converti en PDF avec succès!');
    }, 2000);
  };

  return (
    <ToolLayout
      title="Word en PDF"
      description="Convertir vos documents dans un fichier PDF qui est exactement le même que le DOC ou DOCX original"
      icon={<FileText className="w-8 h-8" />}
      color="from-sky-500 to-sky-600"
    >
      <div className="space-y-6">
        <FileUpload
          onFileSelect={handleFileSelect}
          accept=".doc,.docx"
          multiple={false}
          maxFiles={1}
          title="Sélectionnez votre document Word"
          description="Glissez-déposez un fichier DOC ou DOCX ici ou cliquez pour sélectionner"
        />

        {files.length > 0 && (
          <div className="bg-slate-50 p-6 rounded-xl">
            <h3 className="text-lg font-semibold text-slate-700 mb-4">
              Options de conversion
            </h3>
            
            <div className="space-y-4">
              <div className="flex items-center space-x-3">
                <input
                  type="checkbox"
                  id="preserve-formatting"
                  defaultChecked
                  className="text-sky-600 rounded"
                />
                <label htmlFor="preserve-formatting" className="text-slate-700">
                  Préserver le formatage original
                </label>
              </div>

              <div className="flex items-center space-x-3">
                <input
                  type="checkbox"
                  id="preserve-links"
                  defaultChecked
                  className="text-sky-600 rounded"
                />
                <label htmlFor="preserve-links" className="text-slate-700">
                  Préserver les liens hypertextes
                </label>
              </div>

              <div className="flex items-center space-x-3">
                <input
                  type="checkbox"
                  id="preserve-bookmarks"
                  defaultChecked
                  className="text-sky-600 rounded"
                />
                <label htmlFor="preserve-bookmarks" className="text-slate-700">
                  Préserver les signets
                </label>
              </div>
            </div>
          </div>
        )}

        {files.length > 0 && (
          <div className="flex justify-center">
            <button
              onClick={handleConvert}
              disabled={isProcessing}
              className="bg-gradient-to-r from-sky-600 to-blue-600 text-white px-8 py-4 rounded-xl text-lg font-medium hover:shadow-xl transition-all duration-200 transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2"
            >
              {isProcessing ? (
                <>
                  <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white"></div>
                  <span>Conversion en cours...</span>
                </>
              ) : (
                <>
                  <span>Convertir en PDF</span>
                  <ArrowRight className="w-5 h-5" />
                </>
              )}
            </button>
          </div>
        )}
      </div>
    </ToolLayout>
  );
};

export default WordToPDF;