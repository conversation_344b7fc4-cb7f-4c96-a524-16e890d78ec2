import { DataTypes, Model, Optional } from 'sequelize';
import { sequelize } from '../config/database';
import { User } from './User';

// Usage record attributes interface
export interface UsageRecordAttributes {
  id: string;
  user_id: string;
  tool_name: string;
  file_size_mb: number;
  processing_time_seconds: number;
  input_file_count: number;
  output_file_count: number;
  success: boolean;
  error_message?: string;
  parameters?: object;
  created_at?: Date;
  updated_at?: Date;
}

// Usage record creation attributes
export interface UsageRecordCreationAttributes extends Optional<UsageRecordAttributes, 
  'id' | 'success' | 'error_message' | 'parameters' | 'created_at' | 'updated_at'> {}

// Usage record model class
export class UsageRecord extends Model<UsageRecordAttributes, UsageRecordCreationAttributes> 
  implements UsageRecordAttributes {
  public id!: string;
  public user_id!: string;
  public tool_name!: string;
  public file_size_mb!: number;
  public processing_time_seconds!: number;
  public input_file_count!: number;
  public output_file_count!: number;
  public success!: boolean;
  public error_message?: string;
  public parameters?: object;

  // Timestamps
  public readonly created_at!: Date;
  public readonly updated_at!: Date;

  // Associations
  public readonly user?: User;
}

// Initialize UsageRecord model
UsageRecord.init(
  {
    id: {
      type: DataTypes.UUID,
      defaultValue: DataTypes.UUIDV4,
      primaryKey: true,
    },
    user_id: {
      type: DataTypes.UUID,
      allowNull: false,
      references: {
        model: User,
        key: 'id',
      },
      onUpdate: 'CASCADE',
      onDelete: 'CASCADE',
    },
    tool_name: {
      type: DataTypes.STRING(100),
      allowNull: false,
      validate: {
        isIn: [[
          'repair_pdf', 'merge_pdf', 'split_pdf', 'compress_pdf',
          'excel_to_pdf', 'word_to_pdf', 'powerpoint_to_pdf', 'html_to_pdf',
          'pdf_to_excel', 'pdf_to_word', 'pdf_to_powerpoint', 'pdf_to_image',
          'pdf_to_pdfa', 'ocr_pdf', 'sign_pdf', 'protect_pdf', 'unlock_pdf',
          'watermark_pdf', 'rotate_pdf', 'crop_pdf', 'organize_pdf',
          'page_numbers_pdf', 'redact_pdf', 'compare_pdf', 'edit_pdf',
          'scan_to_pdf', 'image_to_pdf'
        ]],
      },
    },
    file_size_mb: {
      type: DataTypes.DECIMAL(10, 2),
      allowNull: false,
      validate: {
        min: 0,
        max: 1000, // 1GB max
      },
    },
    processing_time_seconds: {
      type: DataTypes.INTEGER,
      allowNull: false,
      validate: {
        min: 0,
        max: 3600, // 1 hour max
      },
    },
    input_file_count: {
      type: DataTypes.INTEGER,
      allowNull: false,
      validate: {
        min: 1,
        max: 100,
      },
    },
    output_file_count: {
      type: DataTypes.INTEGER,
      allowNull: false,
      defaultValue: 0,
      validate: {
        min: 0,
        max: 100,
      },
    },
    success: {
      type: DataTypes.BOOLEAN,
      allowNull: false,
      defaultValue: true,
    },
    error_message: {
      type: DataTypes.TEXT,
      allowNull: true,
    },
    parameters: {
      type: DataTypes.JSON,
      allowNull: true,
    },
  },
  {
    sequelize,
    modelName: 'UsageRecord',
    tableName: 'usage_records',
    timestamps: true,
    underscored: true,
    indexes: [
      {
        fields: ['user_id'],
      },
      {
        fields: ['tool_name'],
      },
      {
        fields: ['success'],
      },
      {
        fields: ['created_at'],
      },
      {
        fields: ['user_id', 'created_at'],
      },
      {
        fields: ['user_id', 'tool_name'],
      },
    ],
  }
);

export default UsageRecord;
