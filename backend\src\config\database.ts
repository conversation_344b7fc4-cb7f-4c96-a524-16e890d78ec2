import { Sequelize } from 'sequelize';
import { logger } from '../utils/logger';

// Database configuration
const dbConfig = {
  host: process.env.DB_HOST || 'localhost',
  port: parseInt(process.env.DB_PORT || '3306'),
  database: process.env.DB_NAME || 'pdf',
  username: process.env.DB_USER || 'root',
  password: process.env.DB_PASSWORD || '',
  dialect: 'mysql' as const,
  logging: (sql: string) => {
    if (process.env.NODE_ENV === 'development') {
      logger.debug('SQL Query', { sql });
    }
  },
  pool: {
    max: 10,
    min: 0,
    acquire: 30000,
    idle: 10000
  },
  define: {
    timestamps: true,
    underscored: true,
    freezeTableName: true
  }
};

// Create Sequelize instance
export const sequelize = new Sequelize(
  dbConfig.database,
  dbConfig.username,
  dbConfig.password,
  {
    host: dbConfig.host,
    port: dbConfig.port,
    dialect: dbConfig.dialect,
    logging: dbConfig.logging,
    pool: dbConfig.pool,
    define: dbConfig.define,
    dialectOptions: {
      charset: 'utf8mb4',
      collate: 'utf8mb4_unicode_ci',
      connectTimeout: 60000,
      acquireTimeout: 60000,
      timeout: 60000
    }
  }
);

// Test database connection
export async function testConnection(): Promise<boolean> {
  try {
    await sequelize.authenticate();
    logger.info('Database connection established successfully', {
      host: dbConfig.host,
      port: dbConfig.port,
      database: dbConfig.database
    });
    return true;
  } catch (error) {
    logger.error('Unable to connect to database', {
      error: error instanceof Error ? error.message : String(error),
      host: dbConfig.host,
      port: dbConfig.port,
      database: dbConfig.database
    });
    return false;
  }
}

// Initialize database
export async function initializeDatabase(): Promise<void> {
  try {
    // Test connection first
    const isConnected = await testConnection();
    if (!isConnected) {
      throw new Error('Failed to connect to database');
    }

    // Sync models (create tables if they don't exist)
    await sequelize.sync({ alter: process.env.NODE_ENV === 'development' });
    
    logger.info('Database initialized successfully');
  } catch (error) {
    logger.error('Failed to initialize database', {
      error: error instanceof Error ? error.message : String(error)
    });
    throw error;
  }
}

// Close database connection
export async function closeConnection(): Promise<void> {
  try {
    await sequelize.close();
    logger.info('Database connection closed');
  } catch (error) {
    logger.error('Error closing database connection', {
      error: error instanceof Error ? error.message : String(error)
    });
  }
}

export default sequelize;
