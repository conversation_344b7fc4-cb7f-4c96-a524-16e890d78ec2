"""
Scan to PDF tool for converting scanned images and documents to searchable PDFs.
"""
import os
from typing import List, Dict, Any, Optional
import structlog

from .base_tool import BasePDFTool, ProcessingError, ValidationError

logger = structlog.get_logger()


class ScanToPDFTool(BasePDFTool):
    """Tool for converting scanned images to searchable PDF documents."""
    
    def __init__(self):
        super().__init__("scan_to_pdf")
        
        # Supported image formats
        self.supported_formats = {
            ".jpg": "JPEG Image",
            ".jpeg": "JPEG Image",
            ".png": "PNG Image",
            ".tiff": "TIFF Image",
            ".tif": "TIFF Image",
            ".bmp": "Bitmap Image",
            ".gif": "GIF Image"
        }
        
        # OCR languages (same as OCR tool)
        self.ocr_languages = {
            "eng": "English",
            "fra": "French",
            "deu": "German",
            "spa": "Spanish",
            "ita": "Italian",
            "por": "Portuguese",
            "rus": "Russian",
            "chi_sim": "Chinese Simplified",
            "chi_tra": "Chinese Traditional",
            "jpn": "Japanese",
            "kor": "Korean",
            "ara": "Arabic"
        }
        
        # Page sizes
        self.page_sizes = {
            "auto": "Auto-detect from image",
            "a4": "A4 (210 × 297 mm)",
            "letter": "Letter (8.5 × 11 in)",
            "legal": "Legal (8.5 × 14 in)",
            "a3": "A3 (297 × 420 mm)",
            "tabloid": "Tabloid (11 × 17 in)"
        }
    
    def validate_input_files(self, input_files: List[str]) -> bool:
        """Validate that input files are supported image formats."""
        for file_path in input_files:
            if not os.path.exists(file_path):
                raise FileNotFoundError(f"Input file not found: {file_path}")
            
            if not os.access(file_path, os.R_OK):
                raise PermissionError(f"Cannot read input file: {file_path}")
            
            # Check if file is a supported image format
            file_ext = os.path.splitext(file_path)[1].lower()
            if file_ext not in self.supported_formats:
                raise ValidationError(f"Unsupported image format: {file_ext}")
        
        return True
    
    async def process(
        self,
        input_files: List[str],
        output_path: str,
        parameters: Optional[Dict[str, Any]] = None
    ) -> List[str]:
        """
        Convert scanned images to searchable PDF documents.
        
        Args:
            input_files: List of image file paths to convert
            output_path: Output directory for PDF files
            parameters: Conversion parameters (OCR, page size, etc.)
            
        Returns:
            List containing paths to converted PDF files
        """
        # Validate inputs
        if len(input_files) == 0:
            raise ValidationError("At least 1 image file is required for scan to PDF conversion")
        
        self.validate_input_files(input_files)
        
        # Prepare parameters
        params = parameters or {}
        ocr_enabled = params.get("ocr_enabled", True)
        ocr_language = params.get("ocr_language", "eng")
        page_size = params.get("page_size", "auto")
        combine_pages = params.get("combine_pages", True)
        dpi = params.get("dpi", 300)
        enhance_image = params.get("enhance_image", True)
        
        # Validate parameters
        if ocr_language not in self.ocr_languages:
            raise ValidationError(f"Invalid OCR language: {ocr_language}. Available: {list(self.ocr_languages.keys())}")
        
        if page_size not in self.page_sizes:
            raise ValidationError(f"Invalid page size: {page_size}. Available: {list(self.page_sizes.keys())}")
        
        if not isinstance(dpi, int) or dpi < 72 or dpi > 600:
            raise ValidationError("DPI must be between 72 and 600")
        
        # Ensure output directory exists
        self.ensure_output_directory(output_path)
        
        output_files = []
        
        try:
            self.logger.info(
                "Starting scan to PDF conversion",
                input_count=len(input_files),
                ocr_enabled=ocr_enabled,
                ocr_language=ocr_language,
                combine_pages=combine_pages,
                dpi=dpi
            )
            
            if combine_pages:
                # Combine all images into a single PDF
                output_filename = "scanned_document.pdf"
                output_file = os.path.join(output_path, output_filename)
                
                conversion_result = await self._convert_images_to_pdf(
                    input_files,
                    output_file,
                    ocr_enabled,
                    ocr_language,
                    page_size,
                    dpi,
                    enhance_image
                )
                
                if os.path.exists(output_file):
                    output_files.append(output_file)
                    
                    output_size = self.get_file_size_mb(output_file)
                    total_input_size = sum(self.get_file_size_mb(f) for f in input_files)
                    
                    self.logger.info(
                        "Combined PDF created successfully",
                        output_file=output_file,
                        pages_created=conversion_result["pages_created"],
                        ocr_applied=conversion_result["ocr_applied"],
                        total_input_size_mb=round(total_input_size, 2),
                        output_size_mb=round(output_size, 2)
                    )
            else:
                # Create separate PDF for each image
                for i, input_file in enumerate(input_files):
                    try:
                        self.logger.debug(f"Converting file {i+1}/{len(input_files)}", file_path=input_file)
                        
                        # Generate output filename
                        base_name = os.path.splitext(os.path.basename(input_file))[0]
                        output_filename = f"{base_name}_scanned.pdf"
                        output_file = os.path.join(output_path, output_filename)
                        
                        # Convert the image
                        conversion_result = await self._convert_images_to_pdf(
                            [input_file],
                            output_file,
                            ocr_enabled,
                            ocr_language,
                            page_size,
                            dpi,
                            enhance_image
                        )
                        
                        # Verify output file was created
                        if not os.path.exists(output_file):
                            raise ProcessingError("Failed to create PDF file")
                        
                        output_size = self.get_file_size_mb(output_file)
                        input_size = self.get_file_size_mb(input_file)
                        
                        self.logger.info(
                            f"File {i+1} converted successfully",
                            input_file=input_file,
                            output_file=output_file,
                            ocr_applied=conversion_result["ocr_applied"],
                            input_size_mb=round(input_size, 2),
                            output_size_mb=round(output_size, 2)
                        )
                        
                        output_files.append(output_file)
                        
                    except Exception as e:
                        self.logger.error(f"Failed to convert file {i+1}", file_path=input_file, error=str(e))
                        # Clean up any partial output files
                        self.cleanup_files(output_files)
                        raise ProcessingError(f"Failed to convert {os.path.basename(input_file)}: {str(e)}")
            
            total_input_size = sum(self.get_file_size_mb(f) for f in input_files)
            total_output_size = sum(self.get_file_size_mb(f) for f in output_files)
            
            self.logger.info(
                "Scan to PDF conversion completed successfully",
                input_files=len(input_files),
                output_files=len(output_files),
                total_input_size_mb=round(total_input_size, 2),
                total_output_size_mb=round(total_output_size, 2)
            )
            
            return output_files
            
        except Exception as e:
            # Clean up any output files on error
            self.cleanup_files(output_files)
            
            if isinstance(e, (ValidationError, ProcessingError)):
                raise e
            else:
                raise ProcessingError(f"Unexpected error during scan to PDF conversion: {str(e)}")
    
    async def _convert_images_to_pdf(
        self,
        input_files: List[str],
        output_file: str,
        ocr_enabled: bool,
        ocr_language: str,
        page_size: str,
        dpi: int,
        enhance_image: bool
    ) -> Dict[str, Any]:
        """Convert images to PDF with optional OCR."""
        try:
            # Try different conversion methods
            
            # Method 1: Try img2pdf (fastest for simple conversion)
            if not ocr_enabled:
                try:
                    return await self._convert_with_img2pdf(input_files, output_file, page_size)
                except Exception as e:
                    self.logger.warning(f"img2pdf conversion failed: {str(e)}")
            
            # Method 2: Try PIL + reportlab (with optional OCR)
            return await self._convert_with_pil_reportlab(
                input_files, output_file, ocr_enabled, ocr_language, page_size, dpi, enhance_image
            )
            
        except Exception as e:
            raise ProcessingError(f"Failed to convert images to PDF: {str(e)}")
    
    async def _convert_with_img2pdf(
        self,
        input_files: List[str],
        output_file: str,
        page_size: str
    ) -> Dict[str, Any]:
        """Convert images using img2pdf (fast, no OCR)."""
        try:
            import img2pdf
            
            # Convert images to PDF
            with open(output_file, "wb") as f:
                f.write(img2pdf.convert(input_files))
            
            return {
                "pages_created": len(input_files),
                "ocr_applied": False,
                "conversion_method": "img2pdf"
            }
            
        except ImportError:
            raise ProcessingError("img2pdf is not installed. Install with: pip install img2pdf")
        except Exception as e:
            raise ProcessingError(f"img2pdf conversion failed: {str(e)}")
    
    async def _convert_with_pil_reportlab(
        self,
        input_files: List[str],
        output_file: str,
        ocr_enabled: bool,
        ocr_language: str,
        page_size: str,
        dpi: int,
        enhance_image: bool
    ) -> Dict[str, Any]:
        """Convert images using PIL + reportlab with optional OCR."""
        try:
            from PIL import Image, ImageEnhance, ImageFilter
            from reportlab.pdfgen import canvas
            from reportlab.lib.pagesizes import letter, a4, legal, a3, tabloid
            import tempfile
            
            # OCR setup if enabled
            ocr_applied = False
            if ocr_enabled:
                try:
                    import pytesseract
                    ocr_applied = True
                except ImportError:
                    self.logger.warning("pytesseract not available, skipping OCR")
                    ocr_enabled = False
            
            # Determine page size
            page_size_map = {
                "a4": a4,
                "letter": letter,
                "legal": legal,
                "a3": a3,
                "tabloid": tabloid
            }
            
            if page_size == "auto":
                # Use first image dimensions
                with Image.open(input_files[0]) as img:
                    pagesize = (img.width * 72 / dpi, img.height * 72 / dpi)
            else:
                pagesize = page_size_map.get(page_size, letter)
            
            # Create PDF
            c = canvas.Canvas(output_file, pagesize=pagesize)
            page_width, page_height = pagesize
            
            pages_created = 0
            
            for input_file in input_files:
                try:
                    # Open and process image
                    with Image.open(input_file) as img:
                        # Convert to RGB if necessary
                        if img.mode != 'RGB':
                            img = img.convert('RGB')
                        
                        # Enhance image if requested
                        if enhance_image:
                            # Enhance contrast and sharpness
                            enhancer = ImageEnhance.Contrast(img)
                            img = enhancer.enhance(1.2)
                            
                            enhancer = ImageEnhance.Sharpness(img)
                            img = enhancer.enhance(1.1)
                            
                            # Apply slight denoising
                            img = img.filter(ImageFilter.MedianFilter(size=3))
                        
                        # Save processed image to temporary file
                        with tempfile.NamedTemporaryFile(suffix=".jpg", delete=False) as temp_img:
                            img.save(temp_img.name, "JPEG", quality=95)
                            temp_img_path = temp_img.name
                        
                        try:
                            # Calculate image dimensions to fit page
                            img_width, img_height = img.size
                            
                            # Scale image to fit page while maintaining aspect ratio
                            scale_x = (page_width - 72) / img_width  # 1 inch margin
                            scale_y = (page_height - 72) / img_height
                            scale = min(scale_x, scale_y)
                            
                            new_width = img_width * scale
                            new_height = img_height * scale
                            
                            # Center image on page
                            x = (page_width - new_width) / 2
                            y = (page_height - new_height) / 2
                            
                            # Add image to PDF
                            c.drawImage(temp_img_path, x, y, new_width, new_height)
                            
                            # Add OCR text if enabled
                            if ocr_enabled:
                                try:
                                    # Extract text using OCR
                                    ocr_text = pytesseract.image_to_string(
                                        img, 
                                        lang=ocr_language,
                                        config='--psm 6'
                                    )
                                    
                                    if ocr_text.strip():
                                        # Add invisible text layer for searchability
                                        c.setFillColorRGB(1, 1, 1, alpha=0)  # Transparent text
                                        c.setFont("Helvetica", 8)
                                        
                                        # Add text in small chunks to avoid overflow
                                        text_lines = ocr_text.split('\n')
                                        text_y = y + new_height - 20
                                        
                                        for line in text_lines[:20]:  # Limit to first 20 lines
                                            if line.strip() and text_y > y:
                                                c.drawString(x + 10, text_y, line.strip()[:100])
                                                text_y -= 12
                                        
                                        c.setFillColorRGB(0, 0, 0, alpha=1)  # Reset to opaque
                                
                                except Exception as ocr_error:
                                    self.logger.warning(f"OCR failed for {input_file}: {str(ocr_error)}")
                            
                            pages_created += 1
                            c.showPage()
                            
                        finally:
                            # Clean up temporary image
                            os.unlink(temp_img_path)
                
                except Exception as e:
                    self.logger.warning(f"Failed to process image {input_file}: {str(e)}")
            
            c.save()
            
            return {
                "pages_created": pages_created,
                "ocr_applied": ocr_applied,
                "conversion_method": "pil_reportlab"
            }
            
        except ImportError as e:
            missing_lib = str(e).split("'")[1] if "'" in str(e) else "required library"
            raise ProcessingError(f"{missing_lib} is not installed. Install required packages.")
        except Exception as e:
            raise ProcessingError(f"PIL/ReportLab conversion failed: {str(e)}")
    
    def get_conversion_options(self) -> Dict[str, Dict[str, Any]]:
        """Get available conversion options."""
        return {
            "ocr_enabled": {
                "description": "Enable OCR for searchable text",
                "type": "boolean",
                "default": True
            },
            "ocr_language": {
                "description": "OCR language",
                "type": "string",
                "options": list(self.ocr_languages.keys()),
                "default": "eng",
                "language_descriptions": self.ocr_languages
            },
            "page_size": {
                "description": "PDF page size",
                "type": "string",
                "options": list(self.page_sizes.keys()),
                "default": "auto",
                "size_descriptions": self.page_sizes
            },
            "combine_pages": {
                "description": "Combine all images into single PDF",
                "type": "boolean",
                "default": True
            },
            "dpi": {
                "description": "Image resolution for processing",
                "type": "integer",
                "min": 72,
                "max": 600,
                "default": 300
            },
            "enhance_image": {
                "description": "Apply image enhancement for better OCR",
                "type": "boolean",
                "default": True
            }
        }
    
    def get_supported_formats(self) -> Dict[str, str]:
        """Get supported image formats."""
        return self.supported_formats.copy()
    
    def get_ocr_languages(self) -> Dict[str, str]:
        """Get available OCR languages."""
        return self.ocr_languages.copy()
    
    def get_page_sizes(self) -> Dict[str, str]:
        """Get available page sizes."""
        return self.page_sizes.copy()


# Create tool instance
scan_to_pdf_tool = ScanToPDFTool()
