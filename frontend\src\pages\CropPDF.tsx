import React, { useState } from 'react';
import { Crop, Download, ArrowRight } from 'lucide-react';
import ToolLayout from '../components/ToolLayout';
import FileUpload from '../components/FileUpload';

const CropPDF = () => {
  const [files, setFiles] = useState<File[]>([]);
  const [isProcessing, setIsProcessing] = useState(false);
  const [cropMode, setCropMode] = useState<'margins' | 'custom' | 'preset'>('margins');

  const handleFileSelect = (selectedFiles: File[]) => {
    setFiles(selectedFiles);
  };

  const handleCrop = () => {
    if (files.length === 0) return;
    
    setIsProcessing(true);
    // Simulate processing
    setTimeout(() => {
      setIsProcessing(false);
      alert('Éditeur de rognage PDF ouvert avec succès!');
    }, 2000);
  };

  return (
    <ToolLayout
      title="Rogner PDF"
      description="Réduisez les marges de vos documents PDF ou sélectionnez une zone à rogner"
      icon={<Crop className="w-8 h-8" />}
      color="from-orange-500 to-orange-600"
    >
      <div className="space-y-6">
        <FileUpload
          onFileSelect={handleFileSelect}
          accept=".pdf"
          multiple={false}
          maxFiles={1}
          title="Sélectionnez votre fichier PDF"
          description="Glissez-déposez un fichier PDF ici ou cliquez pour sélectionner"
        />

        {files.length > 0 && (
          <div className="bg-slate-50 p-6 rounded-xl">
            <h3 className="text-lg font-semibold text-slate-700 mb-4">
              Options de rognage
            </h3>
            
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-slate-700 mb-2">
                  Mode de rognage
                </label>
                <div className="space-y-2">
                  <label className="flex items-center space-x-3">
                    <input
                      type="radio"
                      name="cropMode"
                      value="margins"
                      checked={cropMode === 'margins'}
                      onChange={(e) => setCropMode(e.target.value as 'margins')}
                      className="text-orange-600"
                    />
                    <div>
                      <span className="text-slate-700 font-medium">Réduire les marges</span>
                      <p className="text-sm text-slate-500">Supprime automatiquement les marges excessives</p>
                    </div>
                  </label>
                  <label className="flex items-center space-x-3">
                    <input
                      type="radio"
                      name="cropMode"
                      value="custom"
                      checked={cropMode === 'custom'}
                      onChange={(e) => setCropMode(e.target.value as 'custom')}
                      className="text-orange-600"
                    />
                    <div>
                      <span className="text-slate-700 font-medium">Sélection personnalisée</span>
                      <p className="text-sm text-slate-500">Sélectionnez manuellement la zone à conserver</p>
                    </div>
                  </label>
                  <label className="flex items-center space-x-3">
                    <input
                      type="radio"
                      name="cropMode"
                      value="preset"
                      checked={cropMode === 'preset'}
                      onChange={(e) => setCropMode(e.target.value as 'preset')}
                      className="text-orange-600"
                    />
                    <div>
                      <span className="text-slate-700 font-medium">Formats prédéfinis</span>
                      <p className="text-sm text-slate-500">Utilise des formats standard (A4, Letter, etc.)</p>
                    </div>
                  </label>
                </div>
              </div>

              {cropMode === 'preset' && (
                <div>
                  <label className="block text-sm font-medium text-slate-700 mb-2">
                    Format de page
                  </label>
                  <select className="w-full p-3 border border-slate-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500">
                    <option value="a4">A4 (210 × 297 mm)</option>
                    <option value="letter">Letter (8.5 × 11 in)</option>
                    <option value="legal">Legal (8.5 × 14 in)</option>
                    <option value="a3">A3 (297 × 420 mm)</option>
                    <option value="tabloid">Tabloid (11 × 17 in)</option>
                  </select>
                </div>
              )}

              {cropMode === 'margins' && (
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <label className="block text-sm font-medium text-slate-700 mb-2">
                      Marge supérieure (mm)
                    </label>
                    <input
                      type="number"
                      defaultValue="10"
                      className="w-full p-3 border border-slate-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-slate-700 mb-2">
                      Marge inférieure (mm)
                    </label>
                    <input
                      type="number"
                      defaultValue="10"
                      className="w-full p-3 border border-slate-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-slate-700 mb-2">
                      Marge gauche (mm)
                    </label>
                    <input
                      type="number"
                      defaultValue="10"
                      className="w-full p-3 border border-slate-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-slate-700 mb-2">
                      Marge droite (mm)
                    </label>
                    <input
                      type="number"
                      defaultValue="10"
                      className="w-full p-3 border border-slate-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-orange-500"
                    />
                  </div>
                </div>
              )}

              <div className="space-y-3">
                <div className="flex items-center space-x-3">
                  <input
                    type="checkbox"
                    id="apply-all-pages"
                    defaultChecked
                    className="text-orange-600 rounded"
                  />
                  <label htmlFor="apply-all-pages" className="text-slate-700">
                    Appliquer à toutes les pages
                  </label>
                </div>

                <div className="flex items-center space-x-3">
                  <input
                    type="checkbox"
                    id="preserve-aspect-ratio"
                    defaultChecked
                    className="text-orange-600 rounded"
                  />
                  <label htmlFor="preserve-aspect-ratio" className="text-slate-700">
                    Préserver les proportions
                  </label>
                </div>

                <div className="flex items-center space-x-3">
                  <input
                    type="checkbox"
                    id="center-content"
                    className="text-orange-600 rounded"
                  />
                  <label htmlFor="center-content" className="text-slate-700">
                    Centrer le contenu après rognage
                  </label>
                </div>
              </div>

              <div className="bg-blue-50 p-4 rounded-lg">
                <div className="flex items-center space-x-2">
                  <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                  <span className="text-sm text-blue-800 font-medium">Outils de rognage</span>
                </div>
                <ul className="text-sm text-blue-700 mt-1 space-y-1">
                  <li>• Aperçu en temps réel</li>
                  <li>• Sélection précise au pixel près</li>
                  <li>• Grille d'aide pour l'alignement</li>
                  <li>• Annulation/rétablissement des modifications</li>
                </ul>
              </div>
            </div>
          </div>
        )}

        {files.length > 0 && (
          <div className="flex justify-center">
            <button
              onClick={handleCrop}
              disabled={isProcessing}
              className="bg-gradient-to-r from-orange-600 to-red-600 text-white px-8 py-4 rounded-xl text-lg font-medium hover:shadow-xl transition-all duration-200 transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2"
            >
              {isProcessing ? (
                <>
                  <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white"></div>
                  <span>Ouverture de l'éditeur...</span>
                </>
              ) : (
                <>
                  <span>Ouvrir l'éditeur de rognage</span>
                  <ArrowRight className="w-5 h-5" />
                </>
              )}
            </button>
          </div>
        )}
      </div>
    </ToolLayout>
  );
};

export default CropPDF;