import React, { useState } from 'react';
import { Minimize2, Download, ArrowRight } from 'lucide-react';
import { useLanguage } from '../contexts/LanguageContext';
import { useUsage } from '../contexts/UsageContext';
import ToolLayout from '../components/ToolLayout';
import FileUpload from '../components/FileUpload';

const CompressPDF = () => {
  const [files, setFiles] = useState<File[]>([]);
  const [isProcessing, setIsProcessing] = useState(false);
  const [compressionLevel, setCompressionLevel] = useState<'low' | 'medium' | 'high'>('medium');
  const { t } = useLanguage();
  const { incrementUsage } = useUsage();

  const handleFileSelect = (selectedFiles: File[]) => {
    setFiles(selectedFiles);
  };

  const handleCompress = () => {
    if (files.length === 0) return;
    
    // Check and increment usage before processing
    if (!incrementUsage()) {
      alert('Quota quotidien atteint!');
      return;
    }
    
    setIsProcessing(true);
    // Simulate processing
    setTimeout(() => {
      setIsProcessing(false);
      alert('PDF compressé avec succès!');
    }, 2000);
  };

  return (
    <ToolLayout
      title={t('tool.compress.title')}
      description={t('tool.compress.description')}
      icon={<Minimize2 className="w-8 h-8" />}
      color="from-green-500 to-green-600"
    >
      <div className="space-y-6">
        <FileUpload
          onFileSelect={handleFileSelect}
          accept=".pdf"
          multiple={false}
          maxFiles={1}
          title="Sélectionnez votre fichier PDF"
          description="Glissez-déposez un fichier PDF ici ou cliquez pour sélectionner"
        />

        {files.length > 0 && (
          <div className="bg-slate-50 p-6 rounded-xl">
            <h3 className="text-lg font-semibold text-slate-700 mb-4">
              Niveau de compression
            </h3>
            
            <div className="space-y-4">
              <label className="flex items-center justify-between p-4 bg-white rounded-lg cursor-pointer hover:bg-slate-50 transition-colors">
                <div className="flex items-center space-x-3">
                  <input
                    type="radio"
                    name="compression"
                    value="low"
                    checked={compressionLevel === 'low'}
                    onChange={(e) => setCompressionLevel(e.target.value as 'low')}
                    className="text-green-600"
                  />
                  <div>
                    <span className="text-slate-700 font-medium">Compression légère</span>
                    <p className="text-sm text-slate-500">Qualité maximale, taille légèrement réduite</p>
                  </div>
                </div>
                <div className="text-green-600 font-medium">~10-20%</div>
              </label>

              <label className="flex items-center justify-between p-4 bg-white rounded-lg cursor-pointer hover:bg-slate-50 transition-colors">
                <div className="flex items-center space-x-3">
                  <input
                    type="radio"
                    name="compression"
                    value="medium"
                    checked={compressionLevel === 'medium'}
                    onChange={(e) => setCompressionLevel(e.target.value as 'medium')}
                    className="text-green-600"
                  />
                  <div>
                    <span className="text-slate-700 font-medium">Compression recommandée</span>
                    <p className="text-sm text-slate-500">Bon équilibre entre qualité et taille</p>
                  </div>
                </div>
                <div className="text-green-600 font-medium">~30-50%</div>
              </label>

              <label className="flex items-center justify-between p-4 bg-white rounded-lg cursor-pointer hover:bg-slate-50 transition-colors">
                <div className="flex items-center space-x-3">
                  <input
                    type="radio"
                    name="compression"
                    value="high"
                    checked={compressionLevel === 'high'}
                    onChange={(e) => setCompressionLevel(e.target.value as 'high')}
                    className="text-green-600"
                  />
                  <div>
                    <span className="text-slate-700 font-medium">Compression maximale</span>
                    <p className="text-sm text-slate-500">Taille minimale, qualité réduite</p>
                  </div>
                </div>
                <div className="text-green-600 font-medium">~60-80%</div>
              </label>
            </div>
          </div>
        )}

        {files.length > 0 && (
          <div className="flex justify-center">
            <button
              onClick={handleCompress}
              disabled={isProcessing}
              className="bg-gradient-to-r from-green-600 to-blue-600 text-white px-8 py-4 rounded-xl text-lg font-medium hover:shadow-xl transition-all duration-200 transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2"
            >
              {isProcessing ? (
                <>
                  <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white"></div>
                  <span>Compression en cours...</span>
                </>
              ) : (
                <>
                  <span>{t('button.compress')}</span>
                  <ArrowRight className="w-5 h-5" />
                </>
              )}
            </button>
          </div>
        )}
      </div>
    </ToolLayout>
  );
};

export default CompressPDF;