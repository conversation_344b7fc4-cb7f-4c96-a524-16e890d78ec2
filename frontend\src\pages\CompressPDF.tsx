import React, { useState } from 'react';
import { Minimize2, Download, ArrowRight } from 'lucide-react';
import { useLanguage } from '../contexts/LanguageContext';
import ToolLayout from '../components/ToolLayout';
import FileUpload from '../components/FileUpload';
import { quotaService } from '../services/quotaService';

const CompressPDF = () => {
  const [files, setFiles] = useState<File[]>([]);
  const [isProcessing, setIsProcessing] = useState(false);
  const [results, setResults] = useState<any>(null);
  const [quotaWarning, setQuotaWarning] = useState<any>(null);
  const [compressionLevel, setCompressionLevel] = useState<'low' | 'medium' | 'high'>('medium');
  const { t } = useLanguage();

  const handleFileSelect = (selectedFiles: File[]) => {
    setFiles(selectedFiles);
  };

  const handleCompress = async () => {
    if (files.length === 0) return;

    try {
      setIsProcessing(true);
      setQuotaWarning(null);

      // Step 1: Check quota and start session
      const quotaCheck = await quotaService.checkQuotaAndStartSession('compress_pdf');

      if (!quotaCheck.canProcess) {
        setQuotaWarning({
          type: 'quota_exceeded',
          message: quotaCheck.error.message,
          upgradeRequired: quotaCheck.error.upgradeRequired
        });
        return;
      }

      // Step 2: Process files with session
      const result = await quotaService.processFiles(files, 'compress_pdf', {
        quality: compressionLevel,
        optimize_images: true
      });

      if (result.success) {
        setResults(result.data);
      } else {
        alert('Erreur lors de la compression: ' + result.error.message);
      }

    } catch (error) {
      console.error('Compression failed:', error);
      alert('Erreur lors de la compression du PDF');
    } finally {
      setIsProcessing(false);
    }
  };

  const handleDownloadComplete = async () => {
    // Complete session after downloads
    const completedSession = await quotaService.completeSession();

    // Show quota warning if needed
    if (completedSession && completedSession.remainingQuota) {
      const { files: remainingFiles } = completedSession.remainingQuota;

      if (remainingFiles <= 2) {
        setQuotaWarning({
          type: 'quota_warning',
          message: `Il vous reste ${remainingFiles} fichiers dans votre quota quotidien.`,
          upgradeRequired: remainingFiles === 0
        });
      }
    }
  };

  return (
    <ToolLayout
      title={t('tool.compress.title')}
      description={t('tool.compress.description')}
      icon={<Minimize2 className="w-8 h-8" />}
      color="from-green-500 to-green-600"
    >
      <div className="space-y-6">
        <FileUpload
          onFileSelect={handleFileSelect}
          accept=".pdf"
          multiple={false}
          maxFiles={1}
          title="Sélectionnez votre fichier PDF"
          description="Glissez-déposez un fichier PDF ici ou cliquez pour sélectionner"
        />

        {files.length > 0 && (
          <div className="bg-slate-50 p-6 rounded-xl">
            <h3 className="text-lg font-semibold text-slate-700 mb-4">
              Niveau de compression
            </h3>
            
            <div className="space-y-4">
              <label className="flex items-center justify-between p-4 bg-white rounded-lg cursor-pointer hover:bg-slate-50 transition-colors">
                <div className="flex items-center space-x-3">
                  <input
                    type="radio"
                    name="compression"
                    value="low"
                    checked={compressionLevel === 'low'}
                    onChange={(e) => setCompressionLevel(e.target.value as 'low')}
                    className="text-green-600"
                  />
                  <div>
                    <span className="text-slate-700 font-medium">Compression légère</span>
                    <p className="text-sm text-slate-500">Qualité maximale, taille légèrement réduite</p>
                  </div>
                </div>
                <div className="text-green-600 font-medium">~10-20%</div>
              </label>

              <label className="flex items-center justify-between p-4 bg-white rounded-lg cursor-pointer hover:bg-slate-50 transition-colors">
                <div className="flex items-center space-x-3">
                  <input
                    type="radio"
                    name="compression"
                    value="medium"
                    checked={compressionLevel === 'medium'}
                    onChange={(e) => setCompressionLevel(e.target.value as 'medium')}
                    className="text-green-600"
                  />
                  <div>
                    <span className="text-slate-700 font-medium">Compression recommandée</span>
                    <p className="text-sm text-slate-500">Bon équilibre entre qualité et taille</p>
                  </div>
                </div>
                <div className="text-green-600 font-medium">~30-50%</div>
              </label>

              <label className="flex items-center justify-between p-4 bg-white rounded-lg cursor-pointer hover:bg-slate-50 transition-colors">
                <div className="flex items-center space-x-3">
                  <input
                    type="radio"
                    name="compression"
                    value="high"
                    checked={compressionLevel === 'high'}
                    onChange={(e) => setCompressionLevel(e.target.value as 'high')}
                    className="text-green-600"
                  />
                  <div>
                    <span className="text-slate-700 font-medium">Compression maximale</span>
                    <p className="text-sm text-slate-500">Taille minimale, qualité réduite</p>
                  </div>
                </div>
                <div className="text-green-600 font-medium">~60-80%</div>
              </label>
            </div>
          </div>
        )}

        {files.length > 0 && (
          <div className="flex justify-center">
            <button
              onClick={handleCompress}
              disabled={isProcessing}
              className="bg-gradient-to-r from-green-600 to-blue-600 text-white px-8 py-4 rounded-xl text-lg font-medium hover:shadow-xl transition-all duration-200 transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2"
            >
              {isProcessing ? (
                <>
                  <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white"></div>
                  <span>Compression en cours...</span>
                </>
              ) : (
                <>
                  <span>{t('button.compress')}</span>
                  <ArrowRight className="w-5 h-5" />
                </>
              )}
            </button>
          </div>
        )}

        {/* Results Section */}
        {results && (
          <div className="bg-white rounded-lg shadow-sm border border-slate-200 p-6">
            <h3 className="text-lg font-semibold text-slate-800 mb-4">Fichiers compressés</h3>
            <div className="space-y-3">
              {results.outputFiles.map((file: any, index: number) => (
                <div key={index} className="flex items-center justify-between p-3 bg-slate-50 rounded-lg">
                  <div className="flex items-center space-x-3">
                    <Download className="w-5 h-5 text-green-600" />
                    <span className="text-slate-700">{file.name}</span>
                  </div>
                  <a
                    href={file.downloadUrl}
                    download={file.name}
                    onClick={handleDownloadComplete}
                    className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
                  >
                    Télécharger
                  </a>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Quota Warning */}
        {quotaWarning && (
          <div className={`rounded-lg p-4 ${
            quotaWarning.type === 'quota_exceeded'
              ? 'bg-red-50 border border-red-200'
              : 'bg-yellow-50 border border-yellow-200'
          }`}>
            <p className={`${
              quotaWarning.type === 'quota_exceeded' ? 'text-red-700' : 'text-yellow-700'
            }`}>
              {quotaWarning.message}
            </p>
            {quotaWarning.upgradeRequired && (
              <button className="mt-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors">
                Mettre à niveau
              </button>
            )}
          </div>
        )}
      </div>
    </ToolLayout>
  );
};

export default CompressPDF;