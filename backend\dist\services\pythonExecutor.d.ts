export interface PythonToolParams {
    toolName: string;
    inputFiles: string[];
    outputPath: string;
    parameters?: Record<string, any>;
    timeout?: number;
}
export interface PythonToolResult {
    success: boolean;
    outputFiles: string[];
    error?: string;
    processingTime: number;
    logs?: string[];
}
export declare class PythonExecutor {
    private readonly toolsPath;
    private readonly pythonPath;
    private readonly maxTimeout;
    constructor();
    executeTool(params: PythonToolParams): Promise<PythonToolResult>;
    private validateInputs;
    private createTempConfig;
    private runPythonScript;
    private cleanup;
    getAvailableTools(): Promise<string[]>;
}
export declare const pythonExecutor: PythonExecutor;
//# sourceMappingURL=pythonExecutor.d.ts.map