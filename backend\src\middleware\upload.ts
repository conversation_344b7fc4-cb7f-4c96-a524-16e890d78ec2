import multer from 'multer';
import path from 'path';
import fs from 'fs/promises';
import { v4 as uuidv4 } from 'uuid';
import { Request } from 'express';
import { ValidationError } from './errorHandler';
import { logger } from '../utils/logger';

// Define upload directories
const UPLOAD_DIR = path.join(__dirname, '../../uploads');
const TEMP_DIR = path.join(__dirname, '../../temp');
const PROCESSED_DIR = path.join(__dirname, '../../processed');

// Ensure directories exist
async function ensureDirectories() {
  try {
    await fs.mkdir(UPLOAD_DIR, { recursive: true });
    await fs.mkdir(TEMP_DIR, { recursive: true });
    await fs.mkdir(PROCESSED_DIR, { recursive: true });
  } catch (error) {
    logger.error('Failed to create upload directories', { error });
  }
}

ensureDirectories();

// File filter function
const fileFilter = (req: Request, file: Express.Multer.File, cb: multer.FileFilterCallback) => {
  // Allowed MIME types
  const allowedMimeTypes = [
    'application/pdf',
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', // .xlsx
    'application/vnd.ms-excel', // .xls
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document', // .docx
    'application/msword', // .doc
    'application/vnd.openxmlformats-officedocument.presentationml.presentation', // .pptx
    'application/vnd.ms-powerpoint', // .ppt
    'text/html',
    'image/jpeg',
    'image/png',
    'image/bmp',
    'image/tiff',
    'text/csv'
  ];

  if (allowedMimeTypes.includes(file.mimetype)) {
    cb(null, true);
  } else {
    cb(new ValidationError(`File type ${file.mimetype} is not allowed`));
  }
};

// Storage configuration
const storage = multer.memoryStorage();

// Multer configuration
export const upload = multer({
  storage,
  fileFilter,
  limits: {
    fileSize: 50 * 1024 * 1024, // 50MB per file
    files: 10 // Maximum 10 files
  }
});

// File management service
export class FileManager {
  static async saveUploadedFiles(files: Express.Multer.File[], userId?: string): Promise<string[]> {
    const savedPaths: string[] = [];
    const userDir = userId ? path.join(UPLOAD_DIR, userId) : UPLOAD_DIR;
    
    try {
      await fs.mkdir(userDir, { recursive: true });

      for (const file of files) {
        const fileId = uuidv4();
        const fileExtension = path.extname(file.originalname);
        const fileName = `${fileId}${fileExtension}`;
        const filePath = path.join(userDir, fileName);

        await fs.writeFile(filePath, file.buffer);
        savedPaths.push(filePath);

        logger.info('File saved', {
          originalName: file.originalname,
          savedPath: filePath,
          size: file.size,
          userId
        });
      }

      return savedPaths;
    } catch (error) {
      // Clean up any files that were saved before the error
      await this.cleanupFiles(savedPaths);
      throw new Error(`Failed to save uploaded files: ${error}`);
    }
  }

  static async createOutputDirectory(userId?: string): Promise<string> {
    const jobId = uuidv4();
    const outputDir = userId 
      ? path.join(PROCESSED_DIR, userId, jobId)
      : path.join(PROCESSED_DIR, jobId);

    try {
      await fs.mkdir(outputDir, { recursive: true });
      return outputDir;
    } catch (error) {
      throw new Error(`Failed to create output directory: ${error}`);
    }
  }

  static async cleanupFiles(filePaths: string[]): Promise<void> {
    const cleanupPromises = filePaths.map(async (filePath) => {
      try {
        await fs.unlink(filePath);
        logger.debug('File cleaned up', { filePath });
      } catch (error) {
        logger.warn('Failed to cleanup file', { filePath, error });
      }
    });

    await Promise.allSettled(cleanupPromises);
  }

  static async cleanupDirectory(dirPath: string): Promise<void> {
    try {
      const files = await fs.readdir(dirPath);
      const filePaths = files.map(file => path.join(dirPath, file));
      await this.cleanupFiles(filePaths);
      await fs.rmdir(dirPath);
      logger.debug('Directory cleaned up', { dirPath });
    } catch (error) {
      logger.warn('Failed to cleanup directory', { dirPath, error });
    }
  }

  static async getFileInfo(filePath: string): Promise<{
    exists: boolean;
    size?: number;
    mtime?: Date;
  }> {
    try {
      const stats = await fs.stat(filePath);
      return {
        exists: true,
        size: stats.size,
        mtime: stats.mtime
      };
    } catch (error) {
      return { exists: false };
    }
  }

  static async scheduleCleanup(filePaths: string[], delayMs: number = 3600000): Promise<void> {
    // Schedule cleanup after 1 hour by default
    setTimeout(async () => {
      await this.cleanupFiles(filePaths);
    }, delayMs);
  }

  static async cleanupOldFiles(maxAgeMs: number = 24 * 60 * 60 * 1000): Promise<void> {
    // Clean up files older than 24 hours by default
    const directories = [UPLOAD_DIR, TEMP_DIR, PROCESSED_DIR];

    for (const dir of directories) {
      try {
        await this.cleanupOldFilesInDirectory(dir, maxAgeMs);
      } catch (error) {
        logger.error('Failed to cleanup old files in directory', { dir, error });
      }
    }
  }

  private static async cleanupOldFilesInDirectory(dirPath: string, maxAgeMs: number): Promise<void> {
    try {
      const entries = await fs.readdir(dirPath, { withFileTypes: true });
      const now = Date.now();

      for (const entry of entries) {
        const fullPath = path.join(dirPath, entry.name);
        
        try {
          const stats = await fs.stat(fullPath);
          const age = now - stats.mtime.getTime();

          if (age > maxAgeMs) {
            if (entry.isDirectory()) {
              await this.cleanupDirectory(fullPath);
            } else {
              await fs.unlink(fullPath);
            }
            logger.debug('Old file/directory cleaned up', { path: fullPath, age });
          }
        } catch (error) {
          logger.warn('Failed to process file/directory for cleanup', { path: fullPath, error });
        }
      }
    } catch (error) {
      logger.error('Failed to read directory for cleanup', { dirPath, error });
    }
  }

  static getUploadPath(): string {
    return UPLOAD_DIR;
  }

  static getTempPath(): string {
    return TEMP_DIR;
  }

  static getProcessedPath(): string {
    return PROCESSED_DIR;
  }
}

// Start periodic cleanup (every 6 hours)
setInterval(() => {
  FileManager.cleanupOldFiles();
}, 6 * 60 * 60 * 1000);
