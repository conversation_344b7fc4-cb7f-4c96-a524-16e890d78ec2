"""
Excel to PDF conversion tool.
"""
import os
import tempfile
from typing import List, Dict, Any, Optional
import structlog

from .base_tool import BasePDFTool, ProcessingError, ValidationError

logger = structlog.get_logger()


class ExcelToPDFTool(BasePDFTool):
    """Tool for converting Excel spreadsheets to PDF format."""
    
    def __init__(self):
        super().__init__("excel_to_pdf")
        
        # Supported input formats
        self.supported_formats = {
            ".xlsx": "Excel Workbook (Office 2007+)",
            ".xls": "Excel Workbook (Legacy)",
            ".xlsm": "Excel Macro-Enabled Workbook",
            ".ods": "OpenDocument Spreadsheet",
            ".csv": "Comma-Separated Values"
        }
    
    def validate_input_files(self, input_files: List[str]) -> bool:
        """Validate that input files are Excel documents."""
        for file_path in input_files:
            if not os.path.exists(file_path):
                raise FileNotFoundError(f"Input file not found: {file_path}")
            
            if not os.access(file_path, os.R_OK):
                raise PermissionError(f"Cannot read input file: {file_path}")
            
            # Check if file is an Excel document
            file_ext = os.path.splitext(file_path)[1].lower()
            if file_ext not in self.supported_formats:
                raise ValidationError(f"Unsupported Excel format: {file_ext}")
        
        return True
    
    async def process(
        self,
        input_files: List[str],
        output_path: str,
        parameters: Optional[Dict[str, Any]] = None
    ) -> List[str]:
        """
        Convert Excel spreadsheets to PDF format.
        
        Args:
            input_files: List of Excel file paths to convert
            output_path: Output directory for PDF files
            parameters: Conversion parameters (orientation, sheets, etc.)
            
        Returns:
            List containing paths to converted PDF files
        """
        # Validate inputs
        if len(input_files) == 0:
            raise ValidationError("At least 1 Excel file is required for conversion")
        
        self.validate_input_files(input_files)
        
        # Prepare parameters
        params = parameters or {}
        orientation = params.get("orientation", "portrait")  # portrait, landscape, auto
        sheets = params.get("sheets", "all")  # all, active, specific sheet names
        fit_to_page = params.get("fit_to_page", True)
        include_gridlines = params.get("include_gridlines", True)
        
        # Validate parameters
        if orientation not in ["portrait", "landscape", "auto"]:
            raise ValidationError("Orientation must be 'portrait', 'landscape', or 'auto'")
        
        # Ensure output directory exists
        self.ensure_output_directory(output_path)
        
        output_files = []
        
        try:
            self.logger.info(
                "Starting Excel to PDF conversion",
                input_count=len(input_files),
                orientation=orientation,
                sheets=sheets,
                fit_to_page=fit_to_page
            )
            
            for i, input_file in enumerate(input_files):
                try:
                    self.logger.debug(f"Converting file {i+1}/{len(input_files)}", file_path=input_file)
                    
                    # Generate output filename
                    base_name = os.path.splitext(os.path.basename(input_file))[0]
                    output_filename = f"{base_name}.pdf"
                    output_file = os.path.join(output_path, output_filename)
                    
                    # Convert the Excel file
                    await self._convert_excel_to_pdf(
                        input_file,
                        output_file,
                        orientation,
                        sheets,
                        fit_to_page,
                        include_gridlines
                    )
                    
                    # Verify output file was created
                    if not os.path.exists(output_file):
                        raise ProcessingError("Failed to create PDF file")
                    
                    output_size = self.get_file_size_mb(output_file)
                    input_size = self.get_file_size_mb(input_file)
                    
                    self.logger.info(
                        f"File {i+1} converted successfully",
                        input_file=input_file,
                        output_file=output_file,
                        input_size_mb=round(input_size, 2),
                        output_size_mb=round(output_size, 2)
                    )
                    
                    output_files.append(output_file)
                    
                except Exception as e:
                    self.logger.error(f"Failed to convert file {i+1}", file_path=input_file, error=str(e))
                    # Clean up any partial output files
                    self.cleanup_files(output_files)
                    raise ProcessingError(f"Failed to convert {os.path.basename(input_file)}: {str(e)}")
            
            total_input_size = sum(self.get_file_size_mb(f) for f in input_files)
            total_output_size = sum(self.get_file_size_mb(f) for f in output_files)
            
            self.logger.info(
                "Excel to PDF conversion completed successfully",
                input_files=len(input_files),
                output_files=len(output_files),
                total_input_size_mb=round(total_input_size, 2),
                total_output_size_mb=round(total_output_size, 2)
            )
            
            return output_files
            
        except Exception as e:
            # Clean up any output files on error
            self.cleanup_files(output_files)
            
            if isinstance(e, (ValidationError, ProcessingError)):
                raise e
            else:
                raise ProcessingError(f"Unexpected error during Excel to PDF conversion: {str(e)}")
    
    async def _convert_excel_to_pdf(
        self,
        input_file: str,
        output_file: str,
        orientation: str,
        sheets: str,
        fit_to_page: bool,
        include_gridlines: bool
    ):
        """Convert a single Excel file to PDF."""
        try:
            # Try different conversion methods based on availability
            
            # Method 1: Try LibreOffice headless conversion (most reliable)
            try:
                await self._convert_with_libreoffice(input_file, output_file)
                return
            except Exception as e:
                self.logger.warning(f"LibreOffice conversion failed: {str(e)}")
            
            # Method 2: Try openpyxl + reportlab (for .xlsx files)
            if input_file.lower().endswith(('.xlsx', '.xlsm')):
                try:
                    await self._convert_xlsx_with_openpyxl(
                        input_file, output_file, orientation, sheets, fit_to_page, include_gridlines
                    )
                    return
                except Exception as e:
                    self.logger.warning(f"openpyxl conversion failed: {str(e)}")
            
            # Method 3: Try pandas + matplotlib (for data-focused conversion)
            try:
                await self._convert_with_pandas(
                    input_file, output_file, orientation, sheets
                )
                return
            except Exception as e:
                self.logger.warning(f"pandas conversion failed: {str(e)}")
            
            # If all methods fail
            raise ProcessingError("No suitable conversion method available. Please install LibreOffice.")
            
        except Exception as e:
            raise ProcessingError(f"Failed to convert Excel file: {str(e)}")
    
    async def _convert_with_libreoffice(self, input_file: str, output_file: str):
        """Convert using LibreOffice headless mode."""
        import subprocess
        import shutil
        
        # Check if LibreOffice is available
        libreoffice_cmd = None
        for cmd in ['libreoffice', 'soffice']:
            if shutil.which(cmd):
                libreoffice_cmd = cmd
                break
        
        if not libreoffice_cmd:
            raise ProcessingError("LibreOffice not found in system PATH")
        
        # Create temporary directory for output
        with tempfile.TemporaryDirectory() as temp_dir:
            # Run LibreOffice conversion
            cmd = [
                libreoffice_cmd,
                '--headless',
                '--convert-to', 'pdf',
                '--outdir', temp_dir,
                input_file
            ]
            
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=60)
            
            if result.returncode != 0:
                raise ProcessingError(f"LibreOffice conversion failed: {result.stderr}")
            
            # Find the generated PDF file
            base_name = os.path.splitext(os.path.basename(input_file))[0]
            temp_pdf = os.path.join(temp_dir, f"{base_name}.pdf")
            
            if not os.path.exists(temp_pdf):
                raise ProcessingError("LibreOffice did not generate expected PDF file")
            
            # Move to final location
            shutil.move(temp_pdf, output_file)
    
    async def _convert_xlsx_with_openpyxl(
        self,
        input_file: str,
        output_file: str,
        orientation: str,
        sheets: str,
        fit_to_page: bool,
        include_gridlines: bool
    ):
        """Convert XLSX using openpyxl + reportlab."""
        try:
            from openpyxl import load_workbook
            from reportlab.pdfgen import canvas
            from reportlab.lib.pagesizes import letter, landscape
            from reportlab.lib.units import inch
            
            # Load workbook
            wb = load_workbook(input_file, data_only=True)
            
            # Determine page size and orientation
            if orientation == "landscape":
                pagesize = landscape(letter)
            else:
                pagesize = letter
            
            # Create PDF
            c = canvas.Canvas(output_file, pagesize=pagesize)
            page_width, page_height = pagesize
            
            # Determine which sheets to convert
            if sheets == "all":
                sheet_names = wb.sheetnames
            elif sheets == "active":
                sheet_names = [wb.active.title]
            else:
                # Assume sheets is a comma-separated list of sheet names
                sheet_names = [s.strip() for s in sheets.split(',') if s.strip() in wb.sheetnames]
            
            for sheet_name in sheet_names:
                ws = wb[sheet_name]
                
                # Add sheet title
                c.setFont("Helvetica-Bold", 14)
                c.drawString(inch, page_height - inch, f"Sheet: {sheet_name}")
                
                # Get data range
                if ws.max_row == 1 and ws.max_column == 1:
                    # Empty sheet
                    c.drawString(inch, page_height - inch * 1.5, "No data in this sheet")
                    c.showPage()
                    continue
                
                # Calculate cell dimensions
                max_cols = min(ws.max_column, 10)  # Limit columns for readability
                max_rows = min(ws.max_row, 50)     # Limit rows per page
                
                cell_width = (page_width - 2 * inch) / max_cols
                cell_height = 20
                
                y_start = page_height - inch * 2
                
                # Draw table
                for row in range(1, max_rows + 1):
                    y_pos = y_start - (row - 1) * cell_height
                    
                    if y_pos < inch:
                        c.showPage()
                        y_start = page_height - inch
                        y_pos = y_start
                    
                    for col in range(1, max_cols + 1):
                        cell = ws.cell(row=row, column=col)
                        value = str(cell.value) if cell.value is not None else ""
                        
                        x_pos = inch + (col - 1) * cell_width
                        
                        # Draw cell border if gridlines enabled
                        if include_gridlines:
                            c.rect(x_pos, y_pos - cell_height, cell_width, cell_height)
                        
                        # Draw cell value
                        if value:
                            c.setFont("Helvetica", 8)
                            # Truncate long values
                            if len(value) > 15:
                                value = value[:12] + "..."
                            c.drawString(x_pos + 2, y_pos - cell_height + 5, value)
                
                c.showPage()
            
            c.save()
            
        except ImportError:
            raise ProcessingError("openpyxl and reportlab are required for XLSX conversion")
        except Exception as e:
            raise ProcessingError(f"Failed to convert XLSX with openpyxl: {str(e)}")
    
    async def _convert_with_pandas(
        self,
        input_file: str,
        output_file: str,
        orientation: str,
        sheets: str
    ):
        """Convert using pandas + matplotlib (data-focused)."""
        try:
            import pandas as pd
            import matplotlib.pyplot as plt
            from matplotlib.backends.backend_pdf import PdfPages
            
            # Read Excel file
            if sheets == "all":
                excel_data = pd.read_excel(input_file, sheet_name=None)
            else:
                excel_data = pd.read_excel(input_file, sheet_name=0)
                excel_data = {"Sheet1": excel_data}
            
            # Create PDF
            with PdfPages(output_file) as pdf:
                for sheet_name, df in excel_data.items():
                    if df.empty:
                        continue
                    
                    # Create figure
                    fig, ax = plt.subplots(figsize=(11, 8.5) if orientation == "landscape" else (8.5, 11))
                    
                    # Hide axes
                    ax.axis('tight')
                    ax.axis('off')
                    
                    # Create table
                    table = ax.table(
                        cellText=df.values,
                        colLabels=df.columns,
                        cellLoc='center',
                        loc='center'
                    )
                    
                    # Style table
                    table.auto_set_font_size(False)
                    table.set_fontsize(8)
                    table.scale(1.2, 1.5)
                    
                    # Add title
                    plt.title(f"Sheet: {sheet_name}", fontsize=14, fontweight='bold')
                    
                    # Save page
                    pdf.savefig(fig, bbox_inches='tight')
                    plt.close(fig)
            
        except ImportError:
            raise ProcessingError("pandas and matplotlib are required for this conversion method")
        except Exception as e:
            raise ProcessingError(f"Failed to convert with pandas: {str(e)}")
    
    def get_conversion_options(self) -> Dict[str, Dict[str, Any]]:
        """Get available conversion options."""
        return {
            "orientation": {
                "description": "Page orientation",
                "type": "string",
                "options": ["portrait", "landscape", "auto"],
                "default": "portrait"
            },
            "sheets": {
                "description": "Sheets to convert",
                "type": "string",
                "options": ["all", "active", "specific sheet names"],
                "default": "all",
                "example": "Sheet1,Sheet2"
            },
            "fit_to_page": {
                "description": "Fit content to page width",
                "type": "boolean",
                "default": True
            },
            "include_gridlines": {
                "description": "Include cell gridlines in PDF",
                "type": "boolean",
                "default": True
            }
        }
    
    def get_supported_formats(self) -> Dict[str, str]:
        """Get supported input formats."""
        return self.supported_formats.copy()


# Create tool instance
excel_to_pdf_tool = ExcelToPDFTool()
