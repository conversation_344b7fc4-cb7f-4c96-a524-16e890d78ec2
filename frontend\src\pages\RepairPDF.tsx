import React, { useState } from 'react';
import { Wrench, Download, ArrowRight } from 'lucide-react';
import ToolLayout from '../components/ToolLayout';
import FileUpload from '../components/FileUpload';
import { quotaService } from '../services/quotaService';

const RepairPDF = () => {
  const [files, setFiles] = useState<File[]>([]);
  const [isProcessing, setIsProcessing] = useState(false);
  const [results, setResults] = useState<any>(null);
  const [quotaWarning, setQuotaWarning] = useState<any>(null);
  const [repairStrategy, setRepairStrategy] = useState<'basic' | 'advanced' | 'reconstruct'>('advanced');

  const handleFileSelect = (selectedFiles: File[]) => {
    setFiles(selectedFiles);
  };

  const handleRepair = async () => {
    if (files.length === 0) return;

    try {
      setIsProcessing(true);
      setQuotaWarning(null);

      // Step 1: Check quota and start session
      const quotaCheck = await quotaService.checkQuotaAndStartSession('repair_pdf');

      if (!quotaCheck.canProcess) {
        setQuotaWarning({
          type: 'quota_exceeded',
          message: quotaCheck.error.message,
          upgradeRequired: quotaCheck.error.upgradeRequired
        });
        return;
      }

      // Step 2: Process files with session
      const result = await quotaService.processFiles(files, 'repair_pdf', {
        strategy: repairStrategy,
        preserve_metadata: true
      });

      if (result.success) {
        setResults(result.data);
      } else {
        alert('Erreur lors de la réparation: ' + result.error.message);
      }

    } catch (error) {
      console.error('Repair failed:', error);
      alert('Erreur lors de la réparation du PDF');
    } finally {
      setIsProcessing(false);
    }
  };

  const handleDownloadComplete = async () => {
    const completedSession = await quotaService.completeSession();

    if (completedSession && completedSession.remainingQuota) {
      const { files: remainingFiles } = completedSession.remainingQuota;

      if (remainingFiles <= 2) {
        setQuotaWarning({
          type: 'quota_warning',
          message: `Il vous reste ${remainingFiles} fichiers dans votre quota quotidien.`,
          upgradeRequired: remainingFiles === 0
        });
      }
    }
  };

  return (
    <ToolLayout
      title="Réparer PDF"
      description="Réparez un PDF endommagé et restaurez les données d'un PDF corrompu"
      icon={<Wrench className="w-8 h-8" />}
      color="from-neutral-500 to-neutral-600"
    >
      <div className="space-y-6">
        <FileUpload
          onFileSelect={handleFileSelect}
          accept=".pdf"
          multiple={false}
          maxFiles={1}
          title="Sélectionnez votre fichier PDF endommagé"
          description="Glissez-déposez un fichier PDF corrompu ici ou cliquez pour sélectionner"
        />

        {files.length > 0 && (
          <div className="bg-slate-50 p-6 rounded-xl">
            <h3 className="text-lg font-semibold text-slate-700 mb-4">
              Outils de réparation
            </h3>
            
            <div className="space-y-4">
              <div className="flex items-center space-x-3">
                <input
                  type="checkbox"
                  id="fix-structure"
                  defaultChecked
                  className="text-neutral-600 rounded"
                />
                <label htmlFor="fix-structure" className="text-slate-700">
                  Réparer la structure du fichier
                </label>
              </div>

              <div className="flex items-center space-x-3">
                <input
                  type="checkbox"
                  id="recover-text"
                  defaultChecked
                  className="text-neutral-600 rounded"
                />
                <label htmlFor="recover-text" className="text-slate-700">
                  Récupérer le texte
                </label>
              </div>

              <div className="flex items-center space-x-3">
                <input
                  type="checkbox"
                  id="recover-images"
                  defaultChecked
                  className="text-neutral-600 rounded"
                />
                <label htmlFor="recover-images" className="text-slate-700">
                  Récupérer les images
                </label>
              </div>

              <div className="flex items-center space-x-3">
                <input
                  type="checkbox"
                  id="fix-fonts"
                  defaultChecked
                  className="text-neutral-600 rounded"
                />
                <label htmlFor="fix-fonts" className="text-slate-700">
                  Réparer les polices
                </label>
              </div>

              <div className="flex items-center space-x-3">
                <input
                  type="checkbox"
                  id="optimize-after-repair"
                  className="text-neutral-600 rounded"
                />
                <label htmlFor="optimize-after-repair" className="text-slate-700">
                  Optimiser après réparation
                </label>
              </div>
            </div>

            <div className="mt-6 bg-orange-50 p-4 rounded-lg">
              <div className="flex items-center space-x-2">
                <div className="w-2 h-2 bg-orange-500 rounded-full"></div>
                <span className="text-sm text-orange-800 font-medium">Problèmes courants détectés</span>
              </div>
              <ul className="text-sm text-orange-700 mt-1 space-y-1">
                <li>• Erreurs de structure du fichier</li>
                <li>• Références d'objets brisées</li>
                <li>• Corruption des polices</li>
                <li>• Problèmes de compression</li>
                <li>• Métadonnées corrompues</li>
              </ul>
            </div>
          </div>
        )}

        {files.length > 0 && (
          <div className="flex justify-center">
            <button
              onClick={handleRepair}
              disabled={isProcessing}
              className="bg-gradient-to-r from-neutral-600 to-slate-600 text-white px-8 py-4 rounded-xl text-lg font-medium hover:shadow-xl transition-all duration-200 transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2"
            >
              {isProcessing ? (
                <>
                  <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white"></div>
                  <span>Réparation en cours...</span>
                </>
              ) : (
                <>
                  <span>Réparer le PDF</span>
                  <ArrowRight className="w-5 h-5" />
                </>
              )}
            </button>
          </div>
        )}
      </div>
    </ToolLayout>
  );
};

export default RepairPDF;