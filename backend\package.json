{"name": "pdf-tools-backend", "version": "1.0.0", "description": "Backend API for PDF processing tools", "main": "dist/server.js", "scripts": {"dev": "nodemon src/server.ts", "build": "tsc", "start": "node dist/server.js", "test": "jest", "lint": "eslint src/**/*.ts"}, "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "multer": "^1.4.5-lts.1", "jsonwebtoken": "^9.0.2", "bcryptjs": "^2.4.3", "joi": "^17.11.0", "winston": "^3.11.0", "express-rate-limit": "^7.1.5", "uuid": "^9.0.1", "dotenv": "^16.3.1", "mysql2": "^3.6.5", "sequelize": "^6.35.2"}, "devDependencies": {"@types/express": "^4.17.21", "@types/cors": "^2.8.17", "@types/multer": "^1.4.11", "@types/jsonwebtoken": "^9.0.5", "@types/bcryptjs": "^2.4.6", "@types/uuid": "^9.0.7", "@types/node": "^20.10.0", "sequelize-cli": "^6.6.2", "typescript": "^5.3.2", "nodemon": "^3.0.2", "ts-node": "^10.9.1", "@typescript-eslint/eslint-plugin": "^6.13.1", "@typescript-eslint/parser": "^6.13.1", "eslint": "^8.54.0", "jest": "^29.7.0", "@types/jest": "^29.5.8"}, "engines": {"node": ">=18.0.0"}}