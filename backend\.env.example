# Server Configuration
NODE_ENV=development
PORT=3001

# Frontend URL (for CORS)
FRONTEND_URL=http://localhost:5173

# JWT Configuration
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
JWT_EXPIRES_IN=7d
REFRESH_TOKEN_EXPIRES_IN=30d

# Python Configuration
PYTHON_PATH=python
MAX_PROCESSING_TIMEOUT=300000

# Database Configuration (when you add a database)
# DATABASE_URL=postgresql://username:password@localhost:5432/pdf_tools
# REDIS_URL=redis://localhost:6379

# File Upload Configuration
MAX_FILE_SIZE=52428800
MAX_FILES_PER_REQUEST=10

# Email Configuration (for verification emails)
# SMTP_HOST=smtp.gmail.com
# SMTP_PORT=587
# SMTP_USER=<EMAIL>
# SMTP_PASS=your-app-password

# Payment Configuration (for subscription management)
# STRIPE_SECRET_KEY=sk_test_...
# STRIPE_WEBHOOK_SECRET=whsec_...

# Monitoring and Logging
LOG_LEVEL=info

# Security
BCRYPT_ROUNDS=12
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100
