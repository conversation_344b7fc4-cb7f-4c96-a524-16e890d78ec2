"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.UsageRecord = void 0;
const sequelize_1 = require("sequelize");
const database_1 = require("../config/database");
const User_1 = require("./User");
class UsageRecord extends sequelize_1.Model {
}
exports.UsageRecord = UsageRecord;
UsageRecord.init({
    id: {
        type: sequelize_1.DataTypes.UUID,
        defaultValue: sequelize_1.DataTypes.UUIDV4,
        primaryKey: true,
    },
    user_id: {
        type: sequelize_1.DataTypes.UUID,
        allowNull: false,
        references: {
            model: User_1.User,
            key: 'id',
        },
        onUpdate: 'CASCADE',
        onDelete: 'CASCADE',
    },
    tool_name: {
        type: sequelize_1.DataTypes.STRING(100),
        allowNull: false,
        validate: {
            isIn: [[
                    'repair_pdf', 'merge_pdf', 'split_pdf', 'compress_pdf',
                    'excel_to_pdf', 'word_to_pdf', 'powerpoint_to_pdf', 'html_to_pdf',
                    'pdf_to_excel', 'pdf_to_word', 'pdf_to_powerpoint', 'pdf_to_image',
                    'pdf_to_pdfa', 'ocr_pdf', 'sign_pdf', 'protect_pdf', 'unlock_pdf',
                    'watermark_pdf', 'rotate_pdf', 'crop_pdf', 'organize_pdf',
                    'page_numbers_pdf', 'redact_pdf', 'compare_pdf', 'edit_pdf',
                    'scan_to_pdf', 'image_to_pdf'
                ]],
        },
    },
    file_size_mb: {
        type: sequelize_1.DataTypes.DECIMAL(10, 2),
        allowNull: false,
        validate: {
            min: 0,
            max: 1000,
        },
    },
    processing_time_seconds: {
        type: sequelize_1.DataTypes.INTEGER,
        allowNull: false,
        validate: {
            min: 0,
            max: 3600,
        },
    },
    input_file_count: {
        type: sequelize_1.DataTypes.INTEGER,
        allowNull: false,
        validate: {
            min: 1,
            max: 100,
        },
    },
    output_file_count: {
        type: sequelize_1.DataTypes.INTEGER,
        allowNull: false,
        defaultValue: 0,
        validate: {
            min: 0,
            max: 100,
        },
    },
    success: {
        type: sequelize_1.DataTypes.BOOLEAN,
        allowNull: false,
        defaultValue: true,
    },
    error_message: {
        type: sequelize_1.DataTypes.TEXT,
        allowNull: true,
    },
    parameters: {
        type: sequelize_1.DataTypes.JSON,
        allowNull: true,
    },
}, {
    sequelize: database_1.sequelize,
    modelName: 'UsageRecord',
    tableName: 'usage_records',
    timestamps: true,
    underscored: true,
    indexes: [
        {
            fields: ['user_id'],
        },
        {
            fields: ['tool_name'],
        },
        {
            fields: ['success'],
        },
        {
            fields: ['created_at'],
        },
        {
            fields: ['user_id', 'created_at'],
        },
        {
            fields: ['user_id', 'tool_name'],
        },
    ],
});
exports.default = UsageRecord;
//# sourceMappingURL=UsageRecord.js.map