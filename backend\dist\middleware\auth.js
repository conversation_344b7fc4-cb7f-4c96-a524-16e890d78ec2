"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.userRateLimit = exports.requireVerification = exports.authorize = exports.optionalAuth = exports.authenticate = exports.AuthService = void 0;
const jsonwebtoken_1 = __importDefault(require("jsonwebtoken"));
const errorHandler_1 = require("./errorHandler");
class AuthService {
    static generateTokens(payload) {
        const accessToken = jsonwebtoken_1.default.sign(payload, this.JWT_SECRET, { expiresIn: this.JWT_EXPIRES_IN });
        const refreshToken = jsonwebtoken_1.default.sign({ userId: payload.userId }, this.JWT_SECRET, { expiresIn: this.REFRESH_TOKEN_EXPIRES_IN });
        return { accessToken, refreshToken };
    }
    static verifyToken(token) {
        try {
            return jsonwebtoken_1.default.verify(token, this.JWT_SECRET);
        }
        catch (error) {
            if (error instanceof jsonwebtoken_1.default.TokenExpiredError) {
                throw new errorHandler_1.AuthenticationError('Token has expired');
            }
            else if (error instanceof jsonwebtoken_1.default.JsonWebTokenError) {
                throw new errorHandler_1.AuthenticationError('Invalid token');
            }
            else {
                throw new errorHandler_1.AuthenticationError('Token verification failed');
            }
        }
    }
    static extractTokenFromHeader(authHeader) {
        if (!authHeader) {
            throw new errorHandler_1.AuthenticationError('Authorization header is required');
        }
        const parts = authHeader.split(' ');
        if (parts.length !== 2 || parts[0] !== 'Bearer') {
            throw new errorHandler_1.AuthenticationError('Authorization header must be in format: Bearer <token>');
        }
        return parts[1];
    }
}
exports.AuthService = AuthService;
AuthService.JWT_SECRET = process.env['JWT_SECRET'] || 'your-super-secret-jwt-key';
AuthService.JWT_EXPIRES_IN = process.env['JWT_EXPIRES_IN'] || '7d';
AuthService.REFRESH_TOKEN_EXPIRES_IN = process.env['REFRESH_TOKEN_EXPIRES_IN'] || '30d';
const authenticate = (req, _res, next) => {
    try {
        const token = AuthService.extractTokenFromHeader(req.headers.authorization);
        const payload = AuthService.verifyToken(token);
        req.user = {
            id: payload.userId,
            email: payload.email,
            subscriptionPlan: payload.subscriptionPlan,
            isVerified: payload.isVerified
        };
        next();
    }
    catch (error) {
        next(error);
    }
};
exports.authenticate = authenticate;
const optionalAuth = (req, _res, next) => {
    try {
        const authHeader = req.headers.authorization;
        if (authHeader) {
            const token = AuthService.extractTokenFromHeader(authHeader);
            const payload = AuthService.verifyToken(token);
            req.user = {
                id: payload.userId,
                email: payload.email,
                subscriptionPlan: payload.subscriptionPlan,
                isVerified: payload.isVerified
            };
        }
        next();
    }
    catch (error) {
        next();
    }
};
exports.optionalAuth = optionalAuth;
const authorize = (requiredPlans = ['free', 'premium', 'enterprise']) => {
    return (req, _res, next) => {
        if (!req.user) {
            throw new errorHandler_1.AuthenticationError('Authentication required');
        }
        if (!requiredPlans.includes(req.user.subscriptionPlan)) {
            throw new errorHandler_1.AuthorizationError(`This feature requires one of the following plans: ${requiredPlans.join(', ')}`);
        }
        next();
    };
};
exports.authorize = authorize;
const requireVerification = (req, _res, next) => {
    if (!req.user) {
        throw new errorHandler_1.AuthenticationError('Authentication required');
    }
    if (!req.user.isVerified) {
        throw new errorHandler_1.AuthorizationError('Email verification required');
    }
    next();
};
exports.requireVerification = requireVerification;
const userRateLimit = (maxRequests, windowMs) => {
    const userRequests = new Map();
    return (req, res, next) => {
        const userId = req.user?.id || req.ip || 'anonymous';
        const now = Date.now();
        const userLimit = userRequests.get(userId);
        if (!userLimit || now > userLimit.resetTime) {
            userRequests.set(userId, {
                count: 1,
                resetTime: now + windowMs
            });
            next();
        }
        else if (userLimit.count < maxRequests) {
            userLimit.count++;
            next();
        }
        else {
            const resetIn = Math.ceil((userLimit.resetTime - now) / 1000);
            res.status(429).json({
                success: false,
                error: {
                    message: `Rate limit exceeded. Try again in ${resetIn} seconds.`,
                    type: 'RateLimitError'
                },
                retryAfter: resetIn
            });
        }
    };
};
exports.userRateLimit = userRateLimit;
//# sourceMappingURL=auth.js.map