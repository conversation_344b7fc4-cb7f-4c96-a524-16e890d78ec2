import Joi from 'joi';
import { Request, Response, NextFunction } from 'express';
export declare const schemas: {
    fileUpload: Joi.ObjectSchema<any>;
    pdfToolParams: Joi.ObjectSchema<any>;
    userRegistration: Joi.ObjectSchema<any>;
    userLogin: Joi.ObjectSchema<any>;
    passwordResetRequest: Joi.ObjectSchema<any>;
    passwordReset: Joi.ObjectSchema<any>;
};
export declare const validate: (schema: Joi.ObjectSchema, property?: "body" | "query" | "params") => (req: Request, res: Response, next: NextFunction) => void;
export declare const validateFiles: (req: Request, res: Response, next: NextFunction) => void;
//# sourceMappingURL=validation.d.ts.map