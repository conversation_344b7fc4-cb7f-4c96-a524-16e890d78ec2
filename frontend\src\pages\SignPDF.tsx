import React, { useState } from 'react';
import { PenTool, Download, ArrowRight } from 'lucide-react';
import ToolLayout from '../components/ToolLayout';
import FileUpload from '../components/FileUpload';

const SignPDF = () => {
  const [files, setFiles] = useState<File[]>([]);
  const [isProcessing, setIsProcessing] = useState(false);

  const handleFileSelect = (selectedFiles: File[]) => {
    setFiles(selectedFiles);
  };

  const handleSign = () => {
    if (files.length === 0) return;
    
    setIsProcessing(true);
    // Simulate processing
    setTimeout(() => {
      setIsProcessing(false);
      alert('Éditeur de signature PDF ouvert avec succès!');
    }, 2000);
  };

  return (
    <ToolLayout
      title="Signer PDF"
      description="Signez vous-même ou demandez des signatures électroniques à des tiers"
      icon={<PenTool className="w-8 h-8" />}
      color="from-cyan-500 to-cyan-600"
    >
      <div className="space-y-6">
        <FileUpload
          onFileSelect={handleFileSelect}
          accept=".pdf"
          multiple={false}
          maxFiles={1}
          title="Sélectionnez votre fichier PDF"
          description="Glissez-déposez un fichier PDF ici ou cliquez pour sélectionner"
        />

        {files.length > 0 && (
          <div className="bg-slate-50 p-6 rounded-xl">
            <h3 className="text-lg font-semibold text-slate-700 mb-4">
              Options de signature
            </h3>
            
            <div className="space-y-4">
              <div className="flex items-center space-x-3">
                <input
                  type="checkbox"
                  id="my-signature"
                  defaultChecked
                  className="text-cyan-600 rounded"
                />
                <label htmlFor="my-signature" className="text-slate-700">
                  Ajouter ma signature
                </label>
              </div>

              <div className="flex items-center space-x-3">
                <input
                  type="checkbox"
                  id="request-signature"
                  className="text-cyan-600 rounded"
                />
                <label htmlFor="request-signature" className="text-slate-700">
                  Demander une signature à un tiers
                </label>
              </div>

              <div className="flex items-center space-x-3">
                <input
                  type="checkbox"
                  id="add-date"
                  className="text-cyan-600 rounded"
                />
                <label htmlFor="add-date" className="text-slate-700">
                  Ajouter la date automatiquement
                </label>
              </div>

              <div className="flex items-center space-x-3">
                <input
                  type="checkbox"
                  id="add-text"
                  className="text-cyan-600 rounded"
                />
                <label htmlFor="add-text" className="text-slate-700">
                  Ajouter du texte personnalisé
                </label>
              </div>
            </div>

            <div className="mt-6 p-4 bg-blue-50 rounded-lg">
              <h4 className="font-semibold text-blue-800 mb-2">Types de signatures disponibles</h4>
              <div className="grid grid-cols-3 gap-4">
                <div className="text-center">
                  <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mb-2 mx-auto">
                    <span className="text-blue-600">✏️</span>
                  </div>
                  <span className="text-sm text-blue-700">Dessinée</span>
                </div>
                <div className="text-center">
                  <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mb-2 mx-auto">
                    <span className="text-blue-600">📝</span>
                  </div>
                  <span className="text-sm text-blue-700">Tapée</span>
                </div>
                <div className="text-center">
                  <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mb-2 mx-auto">
                    <span className="text-blue-600">📁</span>
                  </div>
                  <span className="text-sm text-blue-700">Importée</span>
                </div>
              </div>
            </div>
          </div>
        )}

        {files.length > 0 && (
          <div className="flex justify-center">
            <button
              onClick={handleSign}
              disabled={isProcessing}
              className="bg-gradient-to-r from-cyan-600 to-blue-600 text-white px-8 py-4 rounded-xl text-lg font-medium hover:shadow-xl transition-all duration-200 transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2"
            >
              {isProcessing ? (
                <>
                  <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white"></div>
                  <span>Ouverture de l'éditeur...</span>
                </>
              ) : (
                <>
                  <span>Ouvrir l'éditeur de signature</span>
                  <ArrowRight className="w-5 h-5" />
                </>
              )}
            </button>
          </div>
        )}
      </div>
    </ToolLayout>
  );
};

export default SignPDF;