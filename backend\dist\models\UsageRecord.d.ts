import { Model, Optional } from 'sequelize';
import { User } from './User';
export interface UsageRecordAttributes {
    id: string;
    user_id: string;
    tool_name: string;
    file_size_mb: number;
    processing_time_seconds: number;
    input_file_count: number;
    output_file_count: number;
    success: boolean;
    error_message?: string;
    parameters?: object;
    created_at?: Date;
    updated_at?: Date;
}
export interface UsageRecordCreationAttributes extends Optional<UsageRecordAttributes, 'id' | 'success' | 'error_message' | 'parameters' | 'created_at' | 'updated_at'> {
}
export declare class UsageRecord extends Model<UsageRecordAttributes, UsageRecordCreationAttributes> implements UsageRecordAttributes {
    id: string;
    user_id: string;
    tool_name: string;
    file_size_mb: number;
    processing_time_seconds: number;
    input_file_count: number;
    output_file_count: number;
    success: boolean;
    error_message?: string;
    parameters?: object;
    readonly created_at: Date;
    readonly updated_at: Date;
    readonly user?: User;
}
export default UsageRecord;
//# sourceMappingURL=UsageRecord.d.ts.map