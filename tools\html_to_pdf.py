"""
HTML to PDF conversion tool.
"""
import os
import tempfile
import requests
from typing import List, Dict, Any, Optional
from urllib.parse import urlparse
import structlog

from .base_tool import BasePDFTool, ProcessingError, ValidationError

logger = structlog.get_logger()


class HTMLToPDFTool(BasePDFTool):
    """Tool for converting HTML content and web pages to PDF format."""
    
    def __init__(self):
        super().__init__("html_to_pdf")
        
        # Supported input types
        self.input_types = {
            "file": "HTML file",
            "url": "Web page URL",
            "content": "Raw HTML content"
        }
    
    def validate_input_files(self, input_files: List[str]) -> bool:
        """Validate that input files are HTML files or URLs."""
        for file_path in input_files:
            # Check if it's a URL
            if file_path.startswith(('http://', 'https://')):
                continue
            
            # Check if it's a file
            if not os.path.exists(file_path):
                raise FileNotFoundError(f"Input file not found: {file_path}")
            
            if not os.access(file_path, os.R_OK):
                raise PermissionError(f"Cannot read input file: {file_path}")
            
            # Check if file is HTML
            file_ext = os.path.splitext(file_path)[1].lower()
            if file_ext not in ['.html', '.htm']:
                raise ValidationError(f"Unsupported file format: {file_ext}. Expected .html or .htm")
        
        return True
    
    async def process(
        self,
        input_files: List[str],
        output_path: str,
        parameters: Optional[Dict[str, Any]] = None
    ) -> List[str]:
        """
        Convert HTML content to PDF format.
        
        Args:
            input_files: List of HTML file paths, URLs, or HTML content
            output_path: Output directory for PDF files
            parameters: Conversion parameters (page size, margins, etc.)
            
        Returns:
            List containing paths to converted PDF files
        """
        # Validate inputs
        if len(input_files) == 0:
            raise ValidationError("At least 1 HTML input is required for conversion")
        
        self.validate_input_files(input_files)
        
        # Prepare parameters
        params = parameters or {}
        page_size = params.get("page_size", "A4")
        orientation = params.get("orientation", "portrait")
        margin_top = params.get("margin_top", "1cm")
        margin_bottom = params.get("margin_bottom", "1cm")
        margin_left = params.get("margin_left", "1cm")
        margin_right = params.get("margin_right", "1cm")
        include_background = params.get("include_background", True)
        wait_for_load = params.get("wait_for_load", 2)  # seconds
        
        # Ensure output directory exists
        self.ensure_output_directory(output_path)
        
        output_files = []
        
        try:
            self.logger.info(
                "Starting HTML to PDF conversion",
                input_count=len(input_files),
                page_size=page_size,
                orientation=orientation
            )
            
            for i, input_source in enumerate(input_files):
                try:
                    self.logger.debug(f"Converting input {i+1}/{len(input_files)}", source=input_source)
                    
                    # Determine input type and generate output filename
                    if input_source.startswith(('http://', 'https://')):
                        # URL input
                        parsed_url = urlparse(input_source)
                        base_name = parsed_url.netloc.replace('.', '_') + parsed_url.path.replace('/', '_')
                        if not base_name or base_name == '_':
                            base_name = f"webpage_{i+1}"
                    else:
                        # File input
                        base_name = os.path.splitext(os.path.basename(input_source))[0]
                    
                    output_filename = f"{base_name}.pdf"
                    output_file = os.path.join(output_path, output_filename)
                    
                    # Convert the HTML
                    await self._convert_html_to_pdf(
                        input_source,
                        output_file,
                        page_size,
                        orientation,
                        margin_top,
                        margin_bottom,
                        margin_left,
                        margin_right,
                        include_background,
                        wait_for_load
                    )
                    
                    # Verify output file was created
                    if not os.path.exists(output_file):
                        raise ProcessingError("Failed to create PDF file")
                    
                    output_size = self.get_file_size_mb(output_file)
                    
                    self.logger.info(
                        f"Input {i+1} converted successfully",
                        input_source=input_source,
                        output_file=output_file,
                        output_size_mb=round(output_size, 2)
                    )
                    
                    output_files.append(output_file)
                    
                except Exception as e:
                    self.logger.error(f"Failed to convert input {i+1}", source=input_source, error=str(e))
                    # Clean up any partial output files
                    self.cleanup_files(output_files)
                    raise ProcessingError(f"Failed to convert {input_source}: {str(e)}")
            
            total_output_size = sum(self.get_file_size_mb(f) for f in output_files)
            
            self.logger.info(
                "HTML to PDF conversion completed successfully",
                input_count=len(input_files),
                output_files=len(output_files),
                total_output_size_mb=round(total_output_size, 2)
            )
            
            return output_files
            
        except Exception as e:
            # Clean up any output files on error
            self.cleanup_files(output_files)
            
            if isinstance(e, (ValidationError, ProcessingError)):
                raise e
            else:
                raise ProcessingError(f"Unexpected error during HTML to PDF conversion: {str(e)}")
    
    async def _convert_html_to_pdf(
        self,
        input_source: str,
        output_file: str,
        page_size: str,
        orientation: str,
        margin_top: str,
        margin_bottom: str,
        margin_left: str,
        margin_right: str,
        include_background: bool,
        wait_for_load: int
    ):
        """Convert HTML to PDF using available methods."""
        try:
            # Try different conversion methods based on availability
            
            # Method 1: Try wkhtmltopdf (most reliable for complex HTML)
            try:
                await self._convert_with_wkhtmltopdf(
                    input_source, output_file, page_size, orientation,
                    margin_top, margin_bottom, margin_left, margin_right,
                    include_background, wait_for_load
                )
                return
            except Exception as e:
                self.logger.warning(f"wkhtmltopdf conversion failed: {str(e)}")
            
            # Method 2: Try weasyprint
            try:
                await self._convert_with_weasyprint(
                    input_source, output_file, page_size, orientation,
                    margin_top, margin_bottom, margin_left, margin_right
                )
                return
            except Exception as e:
                self.logger.warning(f"weasyprint conversion failed: {str(e)}")
            
            # Method 3: Try pdfkit (wkhtmltopdf wrapper)
            try:
                await self._convert_with_pdfkit(
                    input_source, output_file, page_size, orientation,
                    margin_top, margin_bottom, margin_left, margin_right,
                    include_background
                )
                return
            except Exception as e:
                self.logger.warning(f"pdfkit conversion failed: {str(e)}")
            
            # If all methods fail
            raise ProcessingError("No suitable HTML to PDF conversion method available. Please install wkhtmltopdf or weasyprint.")
            
        except Exception as e:
            raise ProcessingError(f"Failed to convert HTML to PDF: {str(e)}")
    
    async def _convert_with_wkhtmltopdf(
        self,
        input_source: str,
        output_file: str,
        page_size: str,
        orientation: str,
        margin_top: str,
        margin_bottom: str,
        margin_left: str,
        margin_right: str,
        include_background: bool,
        wait_for_load: int
    ):
        """Convert using wkhtmltopdf command line tool."""
        import subprocess
        import shutil
        
        # Check if wkhtmltopdf is available
        if not shutil.which('wkhtmltopdf'):
            raise ProcessingError("wkhtmltopdf not found in system PATH")
        
        # Build command
        cmd = [
            'wkhtmltopdf',
            '--page-size', page_size,
            '--orientation', orientation,
            '--margin-top', margin_top,
            '--margin-bottom', margin_bottom,
            '--margin-left', margin_left,
            '--margin-right', margin_right,
            '--javascript-delay', str(wait_for_load * 1000),  # Convert to milliseconds
        ]
        
        if include_background:
            cmd.append('--print-media-type')
        
        cmd.extend([input_source, output_file])
        
        # Run conversion
        result = subprocess.run(cmd, capture_output=True, text=True, timeout=60)
        
        if result.returncode != 0:
            raise ProcessingError(f"wkhtmltopdf conversion failed: {result.stderr}")
    
    async def _convert_with_weasyprint(
        self,
        input_source: str,
        output_file: str,
        page_size: str,
        orientation: str,
        margin_top: str,
        margin_bottom: str,
        margin_left: str,
        margin_right: str
    ):
        """Convert using WeasyPrint library."""
        try:
            import weasyprint
            from weasyprint import HTML, CSS
            from weasyprint.css import get_all_computed_styles
            
            # Prepare CSS for page setup
            css_content = f"""
            @page {{
                size: {page_size} {orientation};
                margin-top: {margin_top};
                margin-bottom: {margin_bottom};
                margin-left: {margin_left};
                margin-right: {margin_right};
            }}
            """
            
            # Load HTML
            if input_source.startswith(('http://', 'https://')):
                html_doc = HTML(url=input_source)
            else:
                html_doc = HTML(filename=input_source)
            
            # Apply CSS and convert
            css_doc = CSS(string=css_content)
            html_doc.write_pdf(output_file, stylesheets=[css_doc])
            
        except ImportError:
            raise ProcessingError("WeasyPrint is not installed. Install with: pip install weasyprint")
        except Exception as e:
            raise ProcessingError(f"WeasyPrint conversion failed: {str(e)}")
    
    async def _convert_with_pdfkit(
        self,
        input_source: str,
        output_file: str,
        page_size: str,
        orientation: str,
        margin_top: str,
        margin_bottom: str,
        margin_left: str,
        margin_right: str,
        include_background: bool
    ):
        """Convert using pdfkit library (wkhtmltopdf wrapper)."""
        try:
            import pdfkit
            
            # Configure options
            options = {
                'page-size': page_size,
                'orientation': orientation,
                'margin-top': margin_top,
                'margin-bottom': margin_bottom,
                'margin-left': margin_left,
                'margin-right': margin_right,
                'encoding': "UTF-8",
                'no-outline': None
            }
            
            if include_background:
                options['print-media-type'] = None
            
            # Convert based on input type
            if input_source.startswith(('http://', 'https://')):
                pdfkit.from_url(input_source, output_file, options=options)
            else:
                pdfkit.from_file(input_source, output_file, options=options)
            
        except ImportError:
            raise ProcessingError("pdfkit is not installed. Install with: pip install pdfkit")
        except Exception as e:
            raise ProcessingError(f"pdfkit conversion failed: {str(e)}")
    
    def get_conversion_options(self) -> Dict[str, Dict[str, Any]]:
        """Get available conversion options."""
        return {
            "page_size": {
                "description": "PDF page size",
                "type": "string",
                "options": ["A4", "A3", "A5", "Letter", "Legal", "Tabloid"],
                "default": "A4"
            },
            "orientation": {
                "description": "Page orientation",
                "type": "string",
                "options": ["portrait", "landscape"],
                "default": "portrait"
            },
            "margin_top": {
                "description": "Top margin",
                "type": "string",
                "default": "1cm",
                "example": "1cm, 0.5in, 10mm"
            },
            "margin_bottom": {
                "description": "Bottom margin",
                "type": "string",
                "default": "1cm"
            },
            "margin_left": {
                "description": "Left margin",
                "type": "string",
                "default": "1cm"
            },
            "margin_right": {
                "description": "Right margin",
                "type": "string",
                "default": "1cm"
            },
            "include_background": {
                "description": "Include background colors and images",
                "type": "boolean",
                "default": True
            },
            "wait_for_load": {
                "description": "Seconds to wait for page to load (for URLs)",
                "type": "integer",
                "min": 0,
                "max": 30,
                "default": 2
            }
        }
    
    def get_input_types(self) -> Dict[str, str]:
        """Get supported input types."""
        return self.input_types.copy()
    
    async def check_conversion_support(self) -> Dict[str, Any]:
        """Check what conversion methods are available."""
        support_info = {
            "wkhtmltopdf": False,
            "weasyprint": False,
            "pdfkit": False,
            "recommended_method": None,
            "installation_notes": []
        }
        
        # Check wkhtmltopdf
        import shutil
        if shutil.which('wkhtmltopdf'):
            support_info["wkhtmltopdf"] = True
        else:
            support_info["installation_notes"].append("Install wkhtmltopdf for best HTML/CSS support")
        
        # Check weasyprint
        try:
            import weasyprint
            support_info["weasyprint"] = True
        except ImportError:
            support_info["installation_notes"].append("Install weasyprint: pip install weasyprint")
        
        # Check pdfkit
        try:
            import pdfkit
            support_info["pdfkit"] = True
        except ImportError:
            support_info["installation_notes"].append("Install pdfkit: pip install pdfkit")
        
        # Determine recommended method
        if support_info["wkhtmltopdf"]:
            support_info["recommended_method"] = "wkhtmltopdf"
        elif support_info["weasyprint"]:
            support_info["recommended_method"] = "weasyprint"
        elif support_info["pdfkit"]:
            support_info["recommended_method"] = "pdfkit"
        else:
            support_info["recommended_method"] = "none"
        
        return support_info


# Create tool instance
html_to_pdf_tool = HTMLToPDFTool()
